{"ast": null, "code": "import { UserManager } from 'oidc-client-ts';\nconst oidcConfig = {\n  authority: 'http://localhost:8000',\n  client_id: 'client1',\n  redirect_uri: 'http://127.0.0.1:3001/callback/authorized',\n  response_type: 'code',\n  scope: 'openid read mywrite',\n  post_logout_redirect_uri: 'http://127.0.0.1:3001/',\n  automaticSilentRenew: true,\n  silent_redirect_uri: 'http://127.0.0.1:3001/silent-renew'\n};\nconst userManager = new UserManager(oidcConfig);\nexport default userManager;", "map": {"version": 3, "names": ["UserManager", "oidcConfig", "authority", "client_id", "redirect_uri", "response_type", "scope", "post_logout_redirect_uri", "automaticSilentRenew", "silent_redirect_uri", "userManager"], "sources": ["D:/Technology/00_SpringSecurity_SpringBoot_2025/react-sso-project/react-spa/src/authService.js"], "sourcesContent": ["import { UserManager } from 'oidc-client-ts';\n\nconst oidcConfig = {\n  authority: 'http://localhost:8000',\n  client_id: 'client1',\n  redirect_uri: 'http://127.0.0.1:3001/callback/authorized',\n  response_type: 'code',\n  scope: 'openid read mywrite',\n  post_logout_redirect_uri: 'http://127.0.0.1:3001/',\n  automaticSilentRenew: true,\n  silent_redirect_uri: 'http://127.0.0.1:3001/silent-renew'\n};\n\nconst userManager = new UserManager(oidcConfig);\nexport default userManager;"], "mappings": "AAAA,SAASA,WAAW,QAAQ,gBAAgB;AAE5C,MAAMC,UAAU,GAAG;EACjBC,SAAS,EAAE,uBAAuB;EAClCC,SAAS,EAAE,SAAS;EACpBC,YAAY,EAAE,2CAA2C;EACzDC,aAAa,EAAE,MAAM;EACrBC,KAAK,EAAE,qBAAqB;EAC5BC,wBAAwB,EAAE,wBAAwB;EAClDC,oBAAoB,EAAE,IAAI;EAC1BC,mBAAmB,EAAE;AACvB,CAAC;AAED,MAAMC,WAAW,GAAG,IAAIV,WAAW,CAACC,UAAU,CAAC;AAC/C,eAAeS,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}