[{"D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\src\\index.js": "1", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\src\\Login.js": "2", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\src\\Callback.js": "3", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\src\\Protected.js": "4", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\src\\authService.js": "5"}, {"size": 551, "mtime": 1759023820728, "results": "6", "hashOfConfig": "7"}, {"size": 1576, "mtime": 1759023866358, "results": "8", "hashOfConfig": "7"}, {"size": 1017, "mtime": 1759023833968, "results": "9", "hashOfConfig": "7"}, {"size": 1581, "mtime": 1759023849754, "results": "10", "hashOfConfig": "7"}, {"size": 474, "mtime": 1759024341860, "results": "11", "hashOfConfig": "7"}, {"filePath": "12", "messages": "13", "suppressedMessages": "14", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "xzwi2t", {"filePath": "15", "messages": "16", "suppressedMessages": "17", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\src\\index.js", [], [], "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\src\\Login.js", [], [], "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\src\\Callback.js", [], [], "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\src\\Protected.js", [], [], "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\src\\authService.js", [], []]