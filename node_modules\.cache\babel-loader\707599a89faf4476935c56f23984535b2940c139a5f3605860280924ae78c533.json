{"ast": null, "code": "// src/utils/Logger.ts\nvar nopLogger = {\n  debug: () => void 0,\n  info: () => void 0,\n  warn: () => void 0,\n  error: () => void 0\n};\nvar level;\nvar logger;\nvar Log = /* @__PURE__ */(Log2 => {\n  Log2[Log2[\"NONE\"] = 0] = \"NONE\";\n  Log2[Log2[\"ERROR\"] = 1] = \"ERROR\";\n  Log2[Log2[\"WARN\"] = 2] = \"WARN\";\n  Log2[Log2[\"INFO\"] = 3] = \"INFO\";\n  Log2[Log2[\"DEBUG\"] = 4] = \"DEBUG\";\n  return Log2;\n})(Log || {});\n(Log2 => {\n  function reset() {\n    level = 3 /* INFO */;\n    logger = nopLogger;\n  }\n  Log2.reset = reset;\n  function setLevel(value) {\n    if (!(0 /* NONE */ <= value && value <= 4 /* DEBUG */)) {\n      throw new Error(\"Invalid log level\");\n    }\n    level = value;\n  }\n  Log2.setLevel = setLevel;\n  function setLogger(value) {\n    logger = value;\n  }\n  Log2.setLogger = setLogger;\n})(Log || (Log = {}));\nvar Logger = class _Logger {\n  constructor(_name) {\n    this._name = _name;\n  }\n  /* eslint-disable @typescript-eslint/no-unsafe-enum-comparison */\n  debug(...args) {\n    if (level >= 4 /* DEBUG */) {\n      logger.debug(_Logger._format(this._name, this._method), ...args);\n    }\n  }\n  info(...args) {\n    if (level >= 3 /* INFO */) {\n      logger.info(_Logger._format(this._name, this._method), ...args);\n    }\n  }\n  warn(...args) {\n    if (level >= 2 /* WARN */) {\n      logger.warn(_Logger._format(this._name, this._method), ...args);\n    }\n  }\n  error(...args) {\n    if (level >= 1 /* ERROR */) {\n      logger.error(_Logger._format(this._name, this._method), ...args);\n    }\n  }\n  /* eslint-enable @typescript-eslint/no-unsafe-enum-comparison */\n  throw(err) {\n    this.error(err);\n    throw err;\n  }\n  create(method) {\n    const methodLogger = Object.create(this);\n    methodLogger._method = method;\n    methodLogger.debug(\"begin\");\n    return methodLogger;\n  }\n  static createStatic(name, staticMethod) {\n    const staticLogger = new _Logger(`${name}.${staticMethod}`);\n    staticLogger.debug(\"begin\");\n    return staticLogger;\n  }\n  static _format(name, method) {\n    const prefix = `[${name}]`;\n    return method ? `${prefix} ${method}:` : prefix;\n  }\n  /* eslint-disable @typescript-eslint/no-unsafe-enum-comparison */\n  // helpers for static class methods\n  static debug(name, ...args) {\n    if (level >= 4 /* DEBUG */) {\n      logger.debug(_Logger._format(name), ...args);\n    }\n  }\n  static info(name, ...args) {\n    if (level >= 3 /* INFO */) {\n      logger.info(_Logger._format(name), ...args);\n    }\n  }\n  static warn(name, ...args) {\n    if (level >= 2 /* WARN */) {\n      logger.warn(_Logger._format(name), ...args);\n    }\n  }\n  static error(name, ...args) {\n    if (level >= 1 /* ERROR */) {\n      logger.error(_Logger._format(name), ...args);\n    }\n  }\n  /* eslint-enable @typescript-eslint/no-unsafe-enum-comparison */\n};\nLog.reset();\n\n// src/utils/JwtUtils.ts\nimport { jwtDecode } from \"jwt-decode\";\nvar JwtUtils = class {\n  // IMPORTANT: doesn't validate the token\n  static decode(token) {\n    try {\n      return jwtDecode(token);\n    } catch (err) {\n      Logger.error(\"JwtUtils.decode\", err);\n      throw err;\n    }\n  }\n  static async generateSignedJwt(header, payload, privateKey) {\n    const encodedHeader = CryptoUtils.encodeBase64Url(new TextEncoder().encode(JSON.stringify(header)));\n    const encodedPayload = CryptoUtils.encodeBase64Url(new TextEncoder().encode(JSON.stringify(payload)));\n    const encodedToken = `${encodedHeader}.${encodedPayload}`;\n    const signature = await window.crypto.subtle.sign({\n      name: \"ECDSA\",\n      hash: {\n        name: \"SHA-256\"\n      }\n    }, privateKey, new TextEncoder().encode(encodedToken));\n    const encodedSignature = CryptoUtils.encodeBase64Url(new Uint8Array(signature));\n    return `${encodedToken}.${encodedSignature}`;\n  }\n};\n\n// src/utils/CryptoUtils.ts\nvar UUID_V4_TEMPLATE = \"10000000-1000-4000-8000-100000000000\";\nvar toBase64 = val => btoa([...new Uint8Array(val)].map(chr => String.fromCharCode(chr)).join(\"\"));\nvar _CryptoUtils = class _CryptoUtils {\n  static _randomWord() {\n    const arr = new Uint32Array(1);\n    crypto.getRandomValues(arr);\n    return arr[0];\n  }\n  /**\n   * Generates RFC4122 version 4 guid\n   */\n  static generateUUIDv4() {\n    const uuid = UUID_V4_TEMPLATE.replace(/[018]/g, c => (+c ^ _CryptoUtils._randomWord() & 15 >> +c / 4).toString(16));\n    return uuid.replace(/-/g, \"\");\n  }\n  /**\n   * PKCE: Generate a code verifier\n   */\n  static generateCodeVerifier() {\n    return _CryptoUtils.generateUUIDv4() + _CryptoUtils.generateUUIDv4() + _CryptoUtils.generateUUIDv4();\n  }\n  /**\n   * PKCE: Generate a code challenge\n   */\n  static async generateCodeChallenge(code_verifier) {\n    if (!crypto.subtle) {\n      throw new Error(\"Crypto.subtle is available only in secure contexts (HTTPS).\");\n    }\n    try {\n      const encoder = new TextEncoder();\n      const data = encoder.encode(code_verifier);\n      const hashed = await crypto.subtle.digest(\"SHA-256\", data);\n      return toBase64(hashed).replace(/\\+/g, \"-\").replace(/\\//g, \"_\").replace(/=+$/, \"\");\n    } catch (err) {\n      Logger.error(\"CryptoUtils.generateCodeChallenge\", err);\n      throw err;\n    }\n  }\n  /**\n   * Generates a base64-encoded string for a basic auth header\n   */\n  static generateBasicAuth(client_id, client_secret) {\n    const encoder = new TextEncoder();\n    const data = encoder.encode([client_id, client_secret].join(\":\"));\n    return toBase64(data);\n  }\n  /**\n   * Generates a hash of a string using a given algorithm\n   * @param alg\n   * @param message\n   */\n  static async hash(alg, message) {\n    const msgUint8 = new TextEncoder().encode(message);\n    const hashBuffer = await crypto.subtle.digest(alg, msgUint8);\n    return new Uint8Array(hashBuffer);\n  }\n  /**\n   * Generates a rfc7638 compliant jwk thumbprint\n   * @param jwk\n   */\n  static async customCalculateJwkThumbprint(jwk) {\n    let jsonObject;\n    switch (jwk.kty) {\n      case \"RSA\":\n        jsonObject = {\n          \"e\": jwk.e,\n          \"kty\": jwk.kty,\n          \"n\": jwk.n\n        };\n        break;\n      case \"EC\":\n        jsonObject = {\n          \"crv\": jwk.crv,\n          \"kty\": jwk.kty,\n          \"x\": jwk.x,\n          \"y\": jwk.y\n        };\n        break;\n      case \"OKP\":\n        jsonObject = {\n          \"crv\": jwk.crv,\n          \"kty\": jwk.kty,\n          \"x\": jwk.x\n        };\n        break;\n      case \"oct\":\n        jsonObject = {\n          \"crv\": jwk.k,\n          \"kty\": jwk.kty\n        };\n        break;\n      default:\n        throw new Error(\"Unknown jwk type\");\n    }\n    const utf8encodedAndHashed = await _CryptoUtils.hash(\"SHA-256\", JSON.stringify(jsonObject));\n    return _CryptoUtils.encodeBase64Url(utf8encodedAndHashed);\n  }\n  static async generateDPoPProof({\n    url,\n    accessToken,\n    httpMethod,\n    keyPair,\n    nonce\n  }) {\n    let hashedToken;\n    let encodedHash;\n    const payload = {\n      \"jti\": window.crypto.randomUUID(),\n      \"htm\": httpMethod != null ? httpMethod : \"GET\",\n      \"htu\": url,\n      \"iat\": Math.floor(Date.now() / 1e3)\n    };\n    if (accessToken) {\n      hashedToken = await _CryptoUtils.hash(\"SHA-256\", accessToken);\n      encodedHash = _CryptoUtils.encodeBase64Url(hashedToken);\n      payload.ath = encodedHash;\n    }\n    if (nonce) {\n      payload.nonce = nonce;\n    }\n    try {\n      const publicJwk = await crypto.subtle.exportKey(\"jwk\", keyPair.publicKey);\n      const header = {\n        \"alg\": \"ES256\",\n        \"typ\": \"dpop+jwt\",\n        \"jwk\": {\n          \"crv\": publicJwk.crv,\n          \"kty\": publicJwk.kty,\n          \"x\": publicJwk.x,\n          \"y\": publicJwk.y\n        }\n      };\n      return await JwtUtils.generateSignedJwt(header, payload, keyPair.privateKey);\n    } catch (err) {\n      if (err instanceof TypeError) {\n        throw new Error(`Error exporting dpop public key: ${err.message}`);\n      } else {\n        throw err;\n      }\n    }\n  }\n  static async generateDPoPJkt(keyPair) {\n    try {\n      const publicJwk = await crypto.subtle.exportKey(\"jwk\", keyPair.publicKey);\n      return await _CryptoUtils.customCalculateJwkThumbprint(publicJwk);\n    } catch (err) {\n      if (err instanceof TypeError) {\n        throw new Error(`Could not retrieve dpop keys from storage: ${err.message}`);\n      } else {\n        throw err;\n      }\n    }\n  }\n  static async generateDPoPKeys() {\n    return await window.crypto.subtle.generateKey({\n      name: \"ECDSA\",\n      namedCurve: \"P-256\"\n    }, false, [\"sign\", \"verify\"]);\n  }\n};\n/**\n * Generates a base64url encoded string\n */\n_CryptoUtils.encodeBase64Url = input => {\n  return toBase64(input).replace(/=/g, \"\").replace(/\\+/g, \"-\").replace(/\\//g, \"_\");\n};\nvar CryptoUtils = _CryptoUtils;\n\n// src/utils/Event.ts\nvar Event = class {\n  constructor(_name) {\n    this._name = _name;\n    this._callbacks = [];\n    this._logger = new Logger(`Event('${this._name}')`);\n  }\n  addHandler(cb) {\n    this._callbacks.push(cb);\n    return () => this.removeHandler(cb);\n  }\n  removeHandler(cb) {\n    const idx = this._callbacks.lastIndexOf(cb);\n    if (idx >= 0) {\n      this._callbacks.splice(idx, 1);\n    }\n  }\n  async raise(...ev) {\n    this._logger.debug(\"raise:\", ...ev);\n    for (const cb of this._callbacks) {\n      await cb(...ev);\n    }\n  }\n};\n\n// src/utils/PopupUtils.ts\nvar PopupUtils = class {\n  /**\n   * Populates a map of window features with a placement centered in front of\n   * the current window. If no explicit width is given, a default value is\n   * binned into [800, 720, 600, 480, 360] based on the current window's width.\n   */\n  static center({\n    ...features\n  }) {\n    var _a, _b, _c;\n    if (features.width == null) features.width = (_a = [800, 720, 600, 480].find(width => width <= window.outerWidth / 1.618)) != null ? _a : 360;\n    (_b = features.left) != null ? _b : features.left = Math.max(0, Math.round(window.screenX + (window.outerWidth - features.width) / 2));\n    if (features.height != null) (_c = features.top) != null ? _c : features.top = Math.max(0, Math.round(window.screenY + (window.outerHeight - features.height) / 2));\n    return features;\n  }\n  static serialize(features) {\n    return Object.entries(features).filter(([, value]) => value != null).map(([key, value]) => `${key}=${typeof value !== \"boolean\" ? value : value ? \"yes\" : \"no\"}`).join(\",\");\n  }\n};\n\n// src/utils/Timer.ts\nvar Timer = class _Timer extends Event {\n  constructor() {\n    super(...arguments);\n    this._logger = new Logger(`Timer('${this._name}')`);\n    this._timerHandle = null;\n    this._expiration = 0;\n    this._callback = () => {\n      const diff = this._expiration - _Timer.getEpochTime();\n      this._logger.debug(\"timer completes in\", diff);\n      if (this._expiration <= _Timer.getEpochTime()) {\n        this.cancel();\n        void super.raise();\n      }\n    };\n  }\n  // get the time\n  static getEpochTime() {\n    return Math.floor(Date.now() / 1e3);\n  }\n  init(durationInSeconds) {\n    const logger2 = this._logger.create(\"init\");\n    durationInSeconds = Math.max(Math.floor(durationInSeconds), 1);\n    const expiration = _Timer.getEpochTime() + durationInSeconds;\n    if (this.expiration === expiration && this._timerHandle) {\n      logger2.debug(\"skipping since already initialized for expiration at\", this.expiration);\n      return;\n    }\n    this.cancel();\n    logger2.debug(\"using duration\", durationInSeconds);\n    this._expiration = expiration;\n    const timerDurationInSeconds = Math.min(durationInSeconds, 5);\n    this._timerHandle = setInterval(this._callback, timerDurationInSeconds * 1e3);\n  }\n  get expiration() {\n    return this._expiration;\n  }\n  cancel() {\n    this._logger.create(\"cancel\");\n    if (this._timerHandle) {\n      clearInterval(this._timerHandle);\n      this._timerHandle = null;\n    }\n  }\n};\n\n// src/utils/UrlUtils.ts\nvar UrlUtils = class {\n  static readParams(url, responseMode = \"query\") {\n    if (!url) throw new TypeError(\"Invalid URL\");\n    const parsedUrl = new URL(url, \"http://127.0.0.1\");\n    const params = parsedUrl[responseMode === \"fragment\" ? \"hash\" : \"search\"];\n    return new URLSearchParams(params.slice(1));\n  }\n};\nvar URL_STATE_DELIMITER = \";\";\n\n// src/errors/ErrorResponse.ts\nvar ErrorResponse = class extends Error {\n  constructor(args, form) {\n    var _a, _b, _c;\n    super(args.error_description || args.error || \"\");\n    this.form = form;\n    /** Marker to detect class: \"ErrorResponse\" */\n    this.name = \"ErrorResponse\";\n    if (!args.error) {\n      Logger.error(\"ErrorResponse\", \"No error passed\");\n      throw new Error(\"No error passed\");\n    }\n    this.error = args.error;\n    this.error_description = (_a = args.error_description) != null ? _a : null;\n    this.error_uri = (_b = args.error_uri) != null ? _b : null;\n    this.state = args.userState;\n    this.session_state = (_c = args.session_state) != null ? _c : null;\n    this.url_state = args.url_state;\n  }\n};\n\n// src/errors/ErrorTimeout.ts\nvar ErrorTimeout = class extends Error {\n  constructor(message) {\n    super(message);\n    /** Marker to detect class: \"ErrorTimeout\" */\n    this.name = \"ErrorTimeout\";\n  }\n};\n\n// src/AccessTokenEvents.ts\nvar AccessTokenEvents = class {\n  constructor(args) {\n    this._logger = new Logger(\"AccessTokenEvents\");\n    this._expiringTimer = new Timer(\"Access token expiring\");\n    this._expiredTimer = new Timer(\"Access token expired\");\n    this._expiringNotificationTimeInSeconds = args.expiringNotificationTimeInSeconds;\n  }\n  async load(container) {\n    const logger2 = this._logger.create(\"load\");\n    if (container.access_token && container.expires_in !== void 0) {\n      const duration = container.expires_in;\n      logger2.debug(\"access token present, remaining duration:\", duration);\n      if (duration > 0) {\n        let expiring = duration - this._expiringNotificationTimeInSeconds;\n        if (expiring <= 0) {\n          expiring = 1;\n        }\n        logger2.debug(\"registering expiring timer, raising in\", expiring, \"seconds\");\n        this._expiringTimer.init(expiring);\n      } else {\n        logger2.debug(\"canceling existing expiring timer because we're past expiration.\");\n        this._expiringTimer.cancel();\n      }\n      const expired = duration + 1;\n      logger2.debug(\"registering expired timer, raising in\", expired, \"seconds\");\n      this._expiredTimer.init(expired);\n    } else {\n      this._expiringTimer.cancel();\n      this._expiredTimer.cancel();\n    }\n  }\n  async unload() {\n    this._logger.debug(\"unload: canceling existing access token timers\");\n    this._expiringTimer.cancel();\n    this._expiredTimer.cancel();\n  }\n  /**\n   * Add callback: Raised prior to the access token expiring.\n   */\n  addAccessTokenExpiring(cb) {\n    return this._expiringTimer.addHandler(cb);\n  }\n  /**\n   * Remove callback: Raised prior to the access token expiring.\n   */\n  removeAccessTokenExpiring(cb) {\n    this._expiringTimer.removeHandler(cb);\n  }\n  /**\n   * Add callback: Raised after the access token has expired.\n   */\n  addAccessTokenExpired(cb) {\n    return this._expiredTimer.addHandler(cb);\n  }\n  /**\n   * Remove callback: Raised after the access token has expired.\n   */\n  removeAccessTokenExpired(cb) {\n    this._expiredTimer.removeHandler(cb);\n  }\n};\n\n// src/CheckSessionIFrame.ts\nvar CheckSessionIFrame = class {\n  constructor(_callback, _client_id, url, _intervalInSeconds, _stopOnError) {\n    this._callback = _callback;\n    this._client_id = _client_id;\n    this._intervalInSeconds = _intervalInSeconds;\n    this._stopOnError = _stopOnError;\n    this._logger = new Logger(\"CheckSessionIFrame\");\n    this._timer = null;\n    this._session_state = null;\n    this._message = e => {\n      if (e.origin === this._frame_origin && e.source === this._frame.contentWindow) {\n        if (e.data === \"error\") {\n          this._logger.error(\"error message from check session op iframe\");\n          if (this._stopOnError) {\n            this.stop();\n          }\n        } else if (e.data === \"changed\") {\n          this._logger.debug(\"changed message from check session op iframe\");\n          this.stop();\n          void this._callback();\n        } else {\n          this._logger.debug(e.data + \" message from check session op iframe\");\n        }\n      }\n    };\n    const parsedUrl = new URL(url);\n    this._frame_origin = parsedUrl.origin;\n    this._frame = window.document.createElement(\"iframe\");\n    this._frame.style.visibility = \"hidden\";\n    this._frame.style.position = \"fixed\";\n    this._frame.style.left = \"-1000px\";\n    this._frame.style.top = \"0\";\n    this._frame.width = \"0\";\n    this._frame.height = \"0\";\n    this._frame.src = parsedUrl.href;\n  }\n  load() {\n    return new Promise(resolve => {\n      this._frame.onload = () => {\n        resolve();\n      };\n      window.document.body.appendChild(this._frame);\n      window.addEventListener(\"message\", this._message, false);\n    });\n  }\n  start(session_state) {\n    if (this._session_state === session_state) {\n      return;\n    }\n    this._logger.create(\"start\");\n    this.stop();\n    this._session_state = session_state;\n    const send = () => {\n      if (!this._frame.contentWindow || !this._session_state) {\n        return;\n      }\n      this._frame.contentWindow.postMessage(this._client_id + \" \" + this._session_state, this._frame_origin);\n    };\n    send();\n    this._timer = setInterval(send, this._intervalInSeconds * 1e3);\n  }\n  stop() {\n    this._logger.create(\"stop\");\n    this._session_state = null;\n    if (this._timer) {\n      clearInterval(this._timer);\n      this._timer = null;\n    }\n  }\n};\n\n// src/InMemoryWebStorage.ts\nvar InMemoryWebStorage = class {\n  constructor() {\n    this._logger = new Logger(\"InMemoryWebStorage\");\n    this._data = {};\n  }\n  clear() {\n    this._logger.create(\"clear\");\n    this._data = {};\n  }\n  getItem(key) {\n    this._logger.create(`getItem('${key}')`);\n    return this._data[key];\n  }\n  setItem(key, value) {\n    this._logger.create(`setItem('${key}')`);\n    this._data[key] = value;\n  }\n  removeItem(key) {\n    this._logger.create(`removeItem('${key}')`);\n    delete this._data[key];\n  }\n  get length() {\n    return Object.getOwnPropertyNames(this._data).length;\n  }\n  key(index) {\n    return Object.getOwnPropertyNames(this._data)[index];\n  }\n};\n\n// src/errors/ErrorDPoPNonce.ts\nvar ErrorDPoPNonce = class extends Error {\n  constructor(nonce, message) {\n    super(message);\n    /** Marker to detect class: \"ErrorDPoPNonce\" */\n    this.name = \"ErrorDPoPNonce\";\n    this.nonce = nonce;\n  }\n};\n\n// src/JsonService.ts\nvar JsonService = class {\n  constructor(additionalContentTypes = [], _jwtHandler = null, _extraHeaders = {}) {\n    this._jwtHandler = _jwtHandler;\n    this._extraHeaders = _extraHeaders;\n    this._logger = new Logger(\"JsonService\");\n    this._contentTypes = [];\n    this._contentTypes.push(...additionalContentTypes, \"application/json\");\n    if (_jwtHandler) {\n      this._contentTypes.push(\"application/jwt\");\n    }\n  }\n  async fetchWithTimeout(input, init = {}) {\n    const {\n      timeoutInSeconds,\n      ...initFetch\n    } = init;\n    if (!timeoutInSeconds) {\n      return await fetch(input, initFetch);\n    }\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), timeoutInSeconds * 1e3);\n    try {\n      const response = await fetch(input, {\n        ...init,\n        signal: controller.signal\n      });\n      return response;\n    } catch (err) {\n      if (err instanceof DOMException && err.name === \"AbortError\") {\n        throw new ErrorTimeout(\"Network timed out\");\n      }\n      throw err;\n    } finally {\n      clearTimeout(timeoutId);\n    }\n  }\n  async getJson(url, {\n    token,\n    credentials,\n    timeoutInSeconds\n  } = {}) {\n    const logger2 = this._logger.create(\"getJson\");\n    const headers = {\n      \"Accept\": this._contentTypes.join(\", \")\n    };\n    if (token) {\n      logger2.debug(\"token passed, setting Authorization header\");\n      headers[\"Authorization\"] = \"Bearer \" + token;\n    }\n    this._appendExtraHeaders(headers);\n    let response;\n    try {\n      logger2.debug(\"url:\", url);\n      response = await this.fetchWithTimeout(url, {\n        method: \"GET\",\n        headers,\n        timeoutInSeconds,\n        credentials\n      });\n    } catch (err) {\n      logger2.error(\"Network Error\");\n      throw err;\n    }\n    logger2.debug(\"HTTP response received, status\", response.status);\n    const contentType = response.headers.get(\"Content-Type\");\n    if (contentType && !this._contentTypes.find(item => contentType.startsWith(item))) {\n      logger2.throw(new Error(`Invalid response Content-Type: ${contentType != null ? contentType : \"undefined\"}, from URL: ${url}`));\n    }\n    if (response.ok && this._jwtHandler && (contentType == null ? void 0 : contentType.startsWith(\"application/jwt\"))) {\n      return await this._jwtHandler(await response.text());\n    }\n    let json;\n    try {\n      json = await response.json();\n    } catch (err) {\n      logger2.error(\"Error parsing JSON response\", err);\n      if (response.ok) throw err;\n      throw new Error(`${response.statusText} (${response.status})`);\n    }\n    if (!response.ok) {\n      logger2.error(\"Error from server:\", json);\n      if (json.error) {\n        throw new ErrorResponse(json);\n      }\n      throw new Error(`${response.statusText} (${response.status}): ${JSON.stringify(json)}`);\n    }\n    return json;\n  }\n  async postForm(url, {\n    body,\n    basicAuth,\n    timeoutInSeconds,\n    initCredentials,\n    extraHeaders\n  }) {\n    const logger2 = this._logger.create(\"postForm\");\n    const headers = {\n      \"Accept\": this._contentTypes.join(\", \"),\n      \"Content-Type\": \"application/x-www-form-urlencoded\",\n      ...extraHeaders\n    };\n    if (basicAuth !== void 0) {\n      headers[\"Authorization\"] = \"Basic \" + basicAuth;\n    }\n    this._appendExtraHeaders(headers);\n    let response;\n    try {\n      logger2.debug(\"url:\", url);\n      response = await this.fetchWithTimeout(url, {\n        method: \"POST\",\n        headers,\n        body,\n        timeoutInSeconds,\n        credentials: initCredentials\n      });\n    } catch (err) {\n      logger2.error(\"Network error\");\n      throw err;\n    }\n    logger2.debug(\"HTTP response received, status\", response.status);\n    const contentType = response.headers.get(\"Content-Type\");\n    if (contentType && !this._contentTypes.find(item => contentType.startsWith(item))) {\n      throw new Error(`Invalid response Content-Type: ${contentType != null ? contentType : \"undefined\"}, from URL: ${url}`);\n    }\n    const responseText = await response.text();\n    let json = {};\n    if (responseText) {\n      try {\n        json = JSON.parse(responseText);\n      } catch (err) {\n        logger2.error(\"Error parsing JSON response\", err);\n        if (response.ok) throw err;\n        throw new Error(`${response.statusText} (${response.status})`);\n      }\n    }\n    if (!response.ok) {\n      logger2.error(\"Error from server:\", json);\n      if (response.headers.has(\"dpop-nonce\")) {\n        const nonce = response.headers.get(\"dpop-nonce\");\n        throw new ErrorDPoPNonce(nonce, `${JSON.stringify(json)}`);\n      }\n      if (json.error) {\n        throw new ErrorResponse(json, body);\n      }\n      throw new Error(`${response.statusText} (${response.status}): ${JSON.stringify(json)}`);\n    }\n    return json;\n  }\n  _appendExtraHeaders(headers) {\n    const logger2 = this._logger.create(\"appendExtraHeaders\");\n    const customKeys = Object.keys(this._extraHeaders);\n    const protectedHeaders = [\"accept\", \"content-type\"];\n    const preventOverride = [\"authorization\"];\n    if (customKeys.length === 0) {\n      return;\n    }\n    customKeys.forEach(headerName => {\n      if (protectedHeaders.includes(headerName.toLocaleLowerCase())) {\n        logger2.warn(\"Protected header could not be set\", headerName, protectedHeaders);\n        return;\n      }\n      if (preventOverride.includes(headerName.toLocaleLowerCase()) && Object.keys(headers).includes(headerName)) {\n        logger2.warn(\"Header could not be overridden\", headerName, preventOverride);\n        return;\n      }\n      const content = typeof this._extraHeaders[headerName] === \"function\" ? this._extraHeaders[headerName]() : this._extraHeaders[headerName];\n      if (content && content !== \"\") {\n        headers[headerName] = content;\n      }\n    });\n  }\n};\n\n// src/MetadataService.ts\nvar MetadataService = class {\n  constructor(_settings) {\n    this._settings = _settings;\n    this._logger = new Logger(\"MetadataService\");\n    this._signingKeys = null;\n    this._metadata = null;\n    this._metadataUrl = this._settings.metadataUrl;\n    this._jsonService = new JsonService([\"application/jwk-set+json\"], null, this._settings.extraHeaders);\n    if (this._settings.signingKeys) {\n      this._logger.debug(\"using signingKeys from settings\");\n      this._signingKeys = this._settings.signingKeys;\n    }\n    if (this._settings.metadata) {\n      this._logger.debug(\"using metadata from settings\");\n      this._metadata = this._settings.metadata;\n    }\n    if (this._settings.fetchRequestCredentials) {\n      this._logger.debug(\"using fetchRequestCredentials from settings\");\n      this._fetchRequestCredentials = this._settings.fetchRequestCredentials;\n    }\n  }\n  resetSigningKeys() {\n    this._signingKeys = null;\n  }\n  async getMetadata() {\n    const logger2 = this._logger.create(\"getMetadata\");\n    if (this._metadata) {\n      logger2.debug(\"using cached values\");\n      return this._metadata;\n    }\n    if (!this._metadataUrl) {\n      logger2.throw(new Error(\"No authority or metadataUrl configured on settings\"));\n      throw null;\n    }\n    logger2.debug(\"getting metadata from\", this._metadataUrl);\n    const metadata = await this._jsonService.getJson(this._metadataUrl, {\n      credentials: this._fetchRequestCredentials,\n      timeoutInSeconds: this._settings.requestTimeoutInSeconds\n    });\n    logger2.debug(\"merging remote JSON with seed metadata\");\n    this._metadata = Object.assign({}, metadata, this._settings.metadataSeed);\n    return this._metadata;\n  }\n  getIssuer() {\n    return this._getMetadataProperty(\"issuer\");\n  }\n  getAuthorizationEndpoint() {\n    return this._getMetadataProperty(\"authorization_endpoint\");\n  }\n  getUserInfoEndpoint() {\n    return this._getMetadataProperty(\"userinfo_endpoint\");\n  }\n  getTokenEndpoint(optional = true) {\n    return this._getMetadataProperty(\"token_endpoint\", optional);\n  }\n  getCheckSessionIframe() {\n    return this._getMetadataProperty(\"check_session_iframe\", true);\n  }\n  getEndSessionEndpoint() {\n    return this._getMetadataProperty(\"end_session_endpoint\", true);\n  }\n  getRevocationEndpoint(optional = true) {\n    return this._getMetadataProperty(\"revocation_endpoint\", optional);\n  }\n  getKeysEndpoint(optional = true) {\n    return this._getMetadataProperty(\"jwks_uri\", optional);\n  }\n  async _getMetadataProperty(name, optional = false) {\n    const logger2 = this._logger.create(`_getMetadataProperty('${name}')`);\n    const metadata = await this.getMetadata();\n    logger2.debug(\"resolved\");\n    if (metadata[name] === void 0) {\n      if (optional === true) {\n        logger2.warn(\"Metadata does not contain optional property\");\n        return void 0;\n      }\n      logger2.throw(new Error(\"Metadata does not contain property \" + name));\n    }\n    return metadata[name];\n  }\n  async getSigningKeys() {\n    const logger2 = this._logger.create(\"getSigningKeys\");\n    if (this._signingKeys) {\n      logger2.debug(\"returning signingKeys from cache\");\n      return this._signingKeys;\n    }\n    const jwks_uri = await this.getKeysEndpoint(false);\n    logger2.debug(\"got jwks_uri\", jwks_uri);\n    const keySet = await this._jsonService.getJson(jwks_uri, {\n      timeoutInSeconds: this._settings.requestTimeoutInSeconds\n    });\n    logger2.debug(\"got key set\", keySet);\n    if (!Array.isArray(keySet.keys)) {\n      logger2.throw(new Error(\"Missing keys on keyset\"));\n      throw null;\n    }\n    this._signingKeys = keySet.keys;\n    return this._signingKeys;\n  }\n};\n\n// src/WebStorageStateStore.ts\nvar WebStorageStateStore = class {\n  constructor({\n    prefix = \"oidc.\",\n    store = localStorage\n  } = {}) {\n    this._logger = new Logger(\"WebStorageStateStore\");\n    this._store = store;\n    this._prefix = prefix;\n  }\n  async set(key, value) {\n    this._logger.create(`set('${key}')`);\n    key = this._prefix + key;\n    await this._store.setItem(key, value);\n  }\n  async get(key) {\n    this._logger.create(`get('${key}')`);\n    key = this._prefix + key;\n    const item = await this._store.getItem(key);\n    return item;\n  }\n  async remove(key) {\n    this._logger.create(`remove('${key}')`);\n    key = this._prefix + key;\n    const item = await this._store.getItem(key);\n    await this._store.removeItem(key);\n    return item;\n  }\n  async getAllKeys() {\n    this._logger.create(\"getAllKeys\");\n    const len = await this._store.length;\n    const keys = [];\n    for (let index = 0; index < len; index++) {\n      const key = await this._store.key(index);\n      if (key && key.indexOf(this._prefix) === 0) {\n        keys.push(key.substr(this._prefix.length));\n      }\n    }\n    return keys;\n  }\n};\n\n// src/OidcClientSettings.ts\nvar DefaultResponseType = \"code\";\nvar DefaultScope = \"openid\";\nvar DefaultClientAuthentication = \"client_secret_post\";\nvar DefaultStaleStateAgeInSeconds = 60 * 15;\nvar OidcClientSettingsStore = class {\n  constructor({\n    // metadata related\n    authority,\n    metadataUrl,\n    metadata,\n    signingKeys,\n    metadataSeed,\n    // client related\n    client_id,\n    client_secret,\n    response_type = DefaultResponseType,\n    scope = DefaultScope,\n    redirect_uri,\n    post_logout_redirect_uri,\n    client_authentication = DefaultClientAuthentication,\n    // optional protocol\n    prompt,\n    display,\n    max_age,\n    ui_locales,\n    acr_values,\n    resource,\n    response_mode,\n    // behavior flags\n    filterProtocolClaims = true,\n    loadUserInfo = false,\n    requestTimeoutInSeconds,\n    staleStateAgeInSeconds = DefaultStaleStateAgeInSeconds,\n    mergeClaimsStrategy = {\n      array: \"replace\"\n    },\n    disablePKCE = false,\n    // other behavior\n    stateStore,\n    revokeTokenAdditionalContentTypes,\n    fetchRequestCredentials,\n    refreshTokenAllowedScope,\n    // extra\n    extraQueryParams = {},\n    extraTokenParams = {},\n    extraHeaders = {},\n    dpop,\n    omitScopeWhenRequesting = false\n  }) {\n    var _a;\n    this.authority = authority;\n    if (metadataUrl) {\n      this.metadataUrl = metadataUrl;\n    } else {\n      this.metadataUrl = authority;\n      if (authority) {\n        if (!this.metadataUrl.endsWith(\"/\")) {\n          this.metadataUrl += \"/\";\n        }\n        this.metadataUrl += \".well-known/openid-configuration\";\n      }\n    }\n    this.metadata = metadata;\n    this.metadataSeed = metadataSeed;\n    this.signingKeys = signingKeys;\n    this.client_id = client_id;\n    this.client_secret = client_secret;\n    this.response_type = response_type;\n    this.scope = scope;\n    this.redirect_uri = redirect_uri;\n    this.post_logout_redirect_uri = post_logout_redirect_uri;\n    this.client_authentication = client_authentication;\n    this.prompt = prompt;\n    this.display = display;\n    this.max_age = max_age;\n    this.ui_locales = ui_locales;\n    this.acr_values = acr_values;\n    this.resource = resource;\n    this.response_mode = response_mode;\n    this.filterProtocolClaims = filterProtocolClaims != null ? filterProtocolClaims : true;\n    this.loadUserInfo = !!loadUserInfo;\n    this.staleStateAgeInSeconds = staleStateAgeInSeconds;\n    this.mergeClaimsStrategy = mergeClaimsStrategy;\n    this.omitScopeWhenRequesting = omitScopeWhenRequesting;\n    this.disablePKCE = !!disablePKCE;\n    this.revokeTokenAdditionalContentTypes = revokeTokenAdditionalContentTypes;\n    this.fetchRequestCredentials = fetchRequestCredentials ? fetchRequestCredentials : \"same-origin\";\n    this.requestTimeoutInSeconds = requestTimeoutInSeconds;\n    if (stateStore) {\n      this.stateStore = stateStore;\n    } else {\n      const store = typeof window !== \"undefined\" ? window.localStorage : new InMemoryWebStorage();\n      this.stateStore = new WebStorageStateStore({\n        store\n      });\n    }\n    this.refreshTokenAllowedScope = refreshTokenAllowedScope;\n    this.extraQueryParams = extraQueryParams;\n    this.extraTokenParams = extraTokenParams;\n    this.extraHeaders = extraHeaders;\n    this.dpop = dpop;\n    if (this.dpop && !((_a = this.dpop) == null ? void 0 : _a.store)) {\n      throw new Error(\"A DPoPStore is required when dpop is enabled\");\n    }\n  }\n};\n\n// src/UserInfoService.ts\nvar UserInfoService = class {\n  constructor(_settings, _metadataService) {\n    this._settings = _settings;\n    this._metadataService = _metadataService;\n    this._logger = new Logger(\"UserInfoService\");\n    this._getClaimsFromJwt = async responseText => {\n      const logger2 = this._logger.create(\"_getClaimsFromJwt\");\n      try {\n        const payload = JwtUtils.decode(responseText);\n        logger2.debug(\"JWT decoding successful\");\n        return payload;\n      } catch (err) {\n        logger2.error(\"Error parsing JWT response\");\n        throw err;\n      }\n    };\n    this._jsonService = new JsonService(void 0, this._getClaimsFromJwt, this._settings.extraHeaders);\n  }\n  async getClaims(token) {\n    const logger2 = this._logger.create(\"getClaims\");\n    if (!token) {\n      this._logger.throw(new Error(\"No token passed\"));\n    }\n    const url = await this._metadataService.getUserInfoEndpoint();\n    logger2.debug(\"got userinfo url\", url);\n    const claims = await this._jsonService.getJson(url, {\n      token,\n      credentials: this._settings.fetchRequestCredentials,\n      timeoutInSeconds: this._settings.requestTimeoutInSeconds\n    });\n    logger2.debug(\"got claims\", claims);\n    return claims;\n  }\n};\n\n// src/TokenClient.ts\nvar TokenClient = class {\n  constructor(_settings, _metadataService) {\n    this._settings = _settings;\n    this._metadataService = _metadataService;\n    this._logger = new Logger(\"TokenClient\");\n    this._jsonService = new JsonService(this._settings.revokeTokenAdditionalContentTypes, null, this._settings.extraHeaders);\n  }\n  /**\n   * Exchange code.\n   *\n   * @see https://www.rfc-editor.org/rfc/rfc6749#section-4.1.3\n   */\n  async exchangeCode({\n    grant_type = \"authorization_code\",\n    redirect_uri = this._settings.redirect_uri,\n    client_id = this._settings.client_id,\n    client_secret = this._settings.client_secret,\n    extraHeaders,\n    ...args\n  }) {\n    const logger2 = this._logger.create(\"exchangeCode\");\n    if (!client_id) {\n      logger2.throw(new Error(\"A client_id is required\"));\n    }\n    if (!redirect_uri) {\n      logger2.throw(new Error(\"A redirect_uri is required\"));\n    }\n    if (!args.code) {\n      logger2.throw(new Error(\"A code is required\"));\n    }\n    const params = new URLSearchParams({\n      grant_type,\n      redirect_uri\n    });\n    for (const [key, value] of Object.entries(args)) {\n      if (value != null) {\n        params.set(key, value);\n      }\n    }\n    let basicAuth;\n    switch (this._settings.client_authentication) {\n      case \"client_secret_basic\":\n        if (client_secret === void 0 || client_secret === null) {\n          logger2.throw(new Error(\"A client_secret is required\"));\n          throw null;\n        }\n        basicAuth = CryptoUtils.generateBasicAuth(client_id, client_secret);\n        break;\n      case \"client_secret_post\":\n        params.append(\"client_id\", client_id);\n        if (client_secret) {\n          params.append(\"client_secret\", client_secret);\n        }\n        break;\n    }\n    const url = await this._metadataService.getTokenEndpoint(false);\n    logger2.debug(\"got token endpoint\");\n    const response = await this._jsonService.postForm(url, {\n      body: params,\n      basicAuth,\n      timeoutInSeconds: this._settings.requestTimeoutInSeconds,\n      initCredentials: this._settings.fetchRequestCredentials,\n      extraHeaders\n    });\n    logger2.debug(\"got response\");\n    return response;\n  }\n  /**\n   * Exchange credentials.\n   *\n   * @see https://www.rfc-editor.org/rfc/rfc6749#section-4.3.2\n   */\n  async exchangeCredentials({\n    grant_type = \"password\",\n    client_id = this._settings.client_id,\n    client_secret = this._settings.client_secret,\n    scope = this._settings.scope,\n    ...args\n  }) {\n    const logger2 = this._logger.create(\"exchangeCredentials\");\n    if (!client_id) {\n      logger2.throw(new Error(\"A client_id is required\"));\n    }\n    const params = new URLSearchParams({\n      grant_type\n    });\n    if (!this._settings.omitScopeWhenRequesting) {\n      params.set(\"scope\", scope);\n    }\n    for (const [key, value] of Object.entries(args)) {\n      if (value != null) {\n        params.set(key, value);\n      }\n    }\n    let basicAuth;\n    switch (this._settings.client_authentication) {\n      case \"client_secret_basic\":\n        if (client_secret === void 0 || client_secret === null) {\n          logger2.throw(new Error(\"A client_secret is required\"));\n          throw null;\n        }\n        basicAuth = CryptoUtils.generateBasicAuth(client_id, client_secret);\n        break;\n      case \"client_secret_post\":\n        params.append(\"client_id\", client_id);\n        if (client_secret) {\n          params.append(\"client_secret\", client_secret);\n        }\n        break;\n    }\n    const url = await this._metadataService.getTokenEndpoint(false);\n    logger2.debug(\"got token endpoint\");\n    const response = await this._jsonService.postForm(url, {\n      body: params,\n      basicAuth,\n      timeoutInSeconds: this._settings.requestTimeoutInSeconds,\n      initCredentials: this._settings.fetchRequestCredentials\n    });\n    logger2.debug(\"got response\");\n    return response;\n  }\n  /**\n   * Exchange a refresh token.\n   *\n   * @see https://www.rfc-editor.org/rfc/rfc6749#section-6\n   */\n  async exchangeRefreshToken({\n    grant_type = \"refresh_token\",\n    client_id = this._settings.client_id,\n    client_secret = this._settings.client_secret,\n    timeoutInSeconds,\n    extraHeaders,\n    ...args\n  }) {\n    const logger2 = this._logger.create(\"exchangeRefreshToken\");\n    if (!client_id) {\n      logger2.throw(new Error(\"A client_id is required\"));\n    }\n    if (!args.refresh_token) {\n      logger2.throw(new Error(\"A refresh_token is required\"));\n    }\n    const params = new URLSearchParams({\n      grant_type\n    });\n    for (const [key, value] of Object.entries(args)) {\n      if (Array.isArray(value)) {\n        value.forEach(param => params.append(key, param));\n      } else if (value != null) {\n        params.set(key, value);\n      }\n    }\n    let basicAuth;\n    switch (this._settings.client_authentication) {\n      case \"client_secret_basic\":\n        if (client_secret === void 0 || client_secret === null) {\n          logger2.throw(new Error(\"A client_secret is required\"));\n          throw null;\n        }\n        basicAuth = CryptoUtils.generateBasicAuth(client_id, client_secret);\n        break;\n      case \"client_secret_post\":\n        params.append(\"client_id\", client_id);\n        if (client_secret) {\n          params.append(\"client_secret\", client_secret);\n        }\n        break;\n    }\n    const url = await this._metadataService.getTokenEndpoint(false);\n    logger2.debug(\"got token endpoint\");\n    const response = await this._jsonService.postForm(url, {\n      body: params,\n      basicAuth,\n      timeoutInSeconds,\n      initCredentials: this._settings.fetchRequestCredentials,\n      extraHeaders\n    });\n    logger2.debug(\"got response\");\n    return response;\n  }\n  /**\n   * Revoke an access or refresh token.\n   *\n   * @see https://datatracker.ietf.org/doc/html/rfc7009#section-2.1\n   */\n  async revoke(args) {\n    var _a;\n    const logger2 = this._logger.create(\"revoke\");\n    if (!args.token) {\n      logger2.throw(new Error(\"A token is required\"));\n    }\n    const url = await this._metadataService.getRevocationEndpoint(false);\n    logger2.debug(`got revocation endpoint, revoking ${(_a = args.token_type_hint) != null ? _a : \"default token type\"}`);\n    const params = new URLSearchParams();\n    for (const [key, value] of Object.entries(args)) {\n      if (value != null) {\n        params.set(key, value);\n      }\n    }\n    params.set(\"client_id\", this._settings.client_id);\n    if (this._settings.client_secret) {\n      params.set(\"client_secret\", this._settings.client_secret);\n    }\n    await this._jsonService.postForm(url, {\n      body: params,\n      timeoutInSeconds: this._settings.requestTimeoutInSeconds\n    });\n    logger2.debug(\"got response\");\n  }\n};\n\n// src/ResponseValidator.ts\nvar ResponseValidator = class {\n  constructor(_settings, _metadataService, _claimsService) {\n    this._settings = _settings;\n    this._metadataService = _metadataService;\n    this._claimsService = _claimsService;\n    this._logger = new Logger(\"ResponseValidator\");\n    this._userInfoService = new UserInfoService(this._settings, this._metadataService);\n    this._tokenClient = new TokenClient(this._settings, this._metadataService);\n  }\n  async validateSigninResponse(response, state, extraHeaders) {\n    const logger2 = this._logger.create(\"validateSigninResponse\");\n    this._processSigninState(response, state);\n    logger2.debug(\"state processed\");\n    await this._processCode(response, state, extraHeaders);\n    logger2.debug(\"code processed\");\n    if (response.isOpenId) {\n      this._validateIdTokenAttributes(response);\n    }\n    logger2.debug(\"tokens validated\");\n    await this._processClaims(response, state == null ? void 0 : state.skipUserInfo, response.isOpenId);\n    logger2.debug(\"claims processed\");\n  }\n  async validateCredentialsResponse(response, skipUserInfo) {\n    const logger2 = this._logger.create(\"validateCredentialsResponse\");\n    if (response.isOpenId && !!response.id_token) {\n      this._validateIdTokenAttributes(response);\n    }\n    logger2.debug(\"tokens validated\");\n    await this._processClaims(response, skipUserInfo, response.isOpenId);\n    logger2.debug(\"claims processed\");\n  }\n  async validateRefreshResponse(response, state) {\n    var _a, _b;\n    const logger2 = this._logger.create(\"validateRefreshResponse\");\n    response.userState = state.data;\n    (_a = response.session_state) != null ? _a : response.session_state = state.session_state;\n    (_b = response.scope) != null ? _b : response.scope = state.scope;\n    if (response.isOpenId && !!response.id_token) {\n      this._validateIdTokenAttributes(response, state.id_token);\n      logger2.debug(\"ID Token validated\");\n    }\n    if (!response.id_token) {\n      response.id_token = state.id_token;\n      response.profile = state.profile;\n    }\n    const hasIdToken = response.isOpenId && !!response.id_token;\n    await this._processClaims(response, false, hasIdToken);\n    logger2.debug(\"claims processed\");\n  }\n  validateSignoutResponse(response, state) {\n    const logger2 = this._logger.create(\"validateSignoutResponse\");\n    if (state.id !== response.state) {\n      logger2.throw(new Error(\"State does not match\"));\n    }\n    logger2.debug(\"state validated\");\n    response.userState = state.data;\n    if (response.error) {\n      logger2.warn(\"Response was error\", response.error);\n      throw new ErrorResponse(response);\n    }\n  }\n  _processSigninState(response, state) {\n    var _a;\n    const logger2 = this._logger.create(\"_processSigninState\");\n    if (state.id !== response.state) {\n      logger2.throw(new Error(\"State does not match\"));\n    }\n    if (!state.client_id) {\n      logger2.throw(new Error(\"No client_id on state\"));\n    }\n    if (!state.authority) {\n      logger2.throw(new Error(\"No authority on state\"));\n    }\n    if (this._settings.authority !== state.authority) {\n      logger2.throw(new Error(\"authority mismatch on settings vs. signin state\"));\n    }\n    if (this._settings.client_id && this._settings.client_id !== state.client_id) {\n      logger2.throw(new Error(\"client_id mismatch on settings vs. signin state\"));\n    }\n    logger2.debug(\"state validated\");\n    response.userState = state.data;\n    response.url_state = state.url_state;\n    (_a = response.scope) != null ? _a : response.scope = state.scope;\n    if (response.error) {\n      logger2.warn(\"Response was error\", response.error);\n      throw new ErrorResponse(response);\n    }\n    if (state.code_verifier && !response.code) {\n      logger2.throw(new Error(\"Expected code in response\"));\n    }\n  }\n  async _processClaims(response, skipUserInfo = false, validateSub = true) {\n    const logger2 = this._logger.create(\"_processClaims\");\n    response.profile = this._claimsService.filterProtocolClaims(response.profile);\n    if (skipUserInfo || !this._settings.loadUserInfo || !response.access_token) {\n      logger2.debug(\"not loading user info\");\n      return;\n    }\n    logger2.debug(\"loading user info\");\n    const claims = await this._userInfoService.getClaims(response.access_token);\n    logger2.debug(\"user info claims received from user info endpoint\");\n    if (validateSub && claims.sub !== response.profile.sub) {\n      logger2.throw(new Error(\"subject from UserInfo response does not match subject in ID Token\"));\n    }\n    response.profile = this._claimsService.mergeClaims(response.profile, this._claimsService.filterProtocolClaims(claims));\n    logger2.debug(\"user info claims received, updated profile:\", response.profile);\n  }\n  async _processCode(response, state, extraHeaders) {\n    const logger2 = this._logger.create(\"_processCode\");\n    if (response.code) {\n      logger2.debug(\"Validating code\");\n      const tokenResponse = await this._tokenClient.exchangeCode({\n        client_id: state.client_id,\n        client_secret: state.client_secret,\n        code: response.code,\n        redirect_uri: state.redirect_uri,\n        code_verifier: state.code_verifier,\n        extraHeaders,\n        ...state.extraTokenParams\n      });\n      Object.assign(response, tokenResponse);\n    } else {\n      logger2.debug(\"No code to process\");\n    }\n  }\n  _validateIdTokenAttributes(response, existingToken) {\n    var _a;\n    const logger2 = this._logger.create(\"_validateIdTokenAttributes\");\n    logger2.debug(\"decoding ID Token JWT\");\n    const incoming = JwtUtils.decode((_a = response.id_token) != null ? _a : \"\");\n    if (!incoming.sub) {\n      logger2.throw(new Error(\"ID Token is missing a subject claim\"));\n    }\n    if (existingToken) {\n      const existing = JwtUtils.decode(existingToken);\n      if (incoming.sub !== existing.sub) {\n        logger2.throw(new Error(\"sub in id_token does not match current sub\"));\n      }\n      if (incoming.auth_time && incoming.auth_time !== existing.auth_time) {\n        logger2.throw(new Error(\"auth_time in id_token does not match original auth_time\"));\n      }\n      if (incoming.azp && incoming.azp !== existing.azp) {\n        logger2.throw(new Error(\"azp in id_token does not match original azp\"));\n      }\n      if (!incoming.azp && existing.azp) {\n        logger2.throw(new Error(\"azp not in id_token, but present in original id_token\"));\n      }\n    }\n    response.profile = incoming;\n  }\n};\n\n// src/State.ts\nvar State = class _State {\n  constructor(args) {\n    this.id = args.id || CryptoUtils.generateUUIDv4();\n    this.data = args.data;\n    if (args.created && args.created > 0) {\n      this.created = args.created;\n    } else {\n      this.created = Timer.getEpochTime();\n    }\n    this.request_type = args.request_type;\n    this.url_state = args.url_state;\n  }\n  toStorageString() {\n    new Logger(\"State\").create(\"toStorageString\");\n    return JSON.stringify({\n      id: this.id,\n      data: this.data,\n      created: this.created,\n      request_type: this.request_type,\n      url_state: this.url_state\n    });\n  }\n  static fromStorageString(storageString) {\n    Logger.createStatic(\"State\", \"fromStorageString\");\n    return Promise.resolve(new _State(JSON.parse(storageString)));\n  }\n  static async clearStaleState(storage, age) {\n    const logger2 = Logger.createStatic(\"State\", \"clearStaleState\");\n    const cutoff = Timer.getEpochTime() - age;\n    const keys = await storage.getAllKeys();\n    logger2.debug(\"got keys\", keys);\n    for (let i = 0; i < keys.length; i++) {\n      const key = keys[i];\n      const item = await storage.get(key);\n      let remove = false;\n      if (item) {\n        try {\n          const state = await _State.fromStorageString(item);\n          logger2.debug(\"got item from key:\", key, state.created);\n          if (state.created <= cutoff) {\n            remove = true;\n          }\n        } catch (err) {\n          logger2.error(\"Error parsing state for key:\", key, err);\n          remove = true;\n        }\n      } else {\n        logger2.debug(\"no item in storage for key:\", key);\n        remove = true;\n      }\n      if (remove) {\n        logger2.debug(\"removed item for key:\", key);\n        void storage.remove(key);\n      }\n    }\n  }\n};\n\n// src/SigninState.ts\nvar SigninState = class _SigninState extends State {\n  constructor(args) {\n    super(args);\n    this.code_verifier = args.code_verifier;\n    this.code_challenge = args.code_challenge;\n    this.authority = args.authority;\n    this.client_id = args.client_id;\n    this.redirect_uri = args.redirect_uri;\n    this.scope = args.scope;\n    this.client_secret = args.client_secret;\n    this.extraTokenParams = args.extraTokenParams;\n    this.response_mode = args.response_mode;\n    this.skipUserInfo = args.skipUserInfo;\n  }\n  static async create(args) {\n    const code_verifier = args.code_verifier === true ? CryptoUtils.generateCodeVerifier() : args.code_verifier || void 0;\n    const code_challenge = code_verifier ? await CryptoUtils.generateCodeChallenge(code_verifier) : void 0;\n    return new _SigninState({\n      ...args,\n      code_verifier,\n      code_challenge\n    });\n  }\n  toStorageString() {\n    new Logger(\"SigninState\").create(\"toStorageString\");\n    return JSON.stringify({\n      id: this.id,\n      data: this.data,\n      created: this.created,\n      request_type: this.request_type,\n      url_state: this.url_state,\n      code_verifier: this.code_verifier,\n      authority: this.authority,\n      client_id: this.client_id,\n      redirect_uri: this.redirect_uri,\n      scope: this.scope,\n      client_secret: this.client_secret,\n      extraTokenParams: this.extraTokenParams,\n      response_mode: this.response_mode,\n      skipUserInfo: this.skipUserInfo\n    });\n  }\n  static fromStorageString(storageString) {\n    Logger.createStatic(\"SigninState\", \"fromStorageString\");\n    const data = JSON.parse(storageString);\n    return _SigninState.create(data);\n  }\n};\n\n// src/SigninRequest.ts\nvar _SigninRequest = class _SigninRequest {\n  constructor(args) {\n    this.url = args.url;\n    this.state = args.state;\n  }\n  static async create({\n    // mandatory\n    url,\n    authority,\n    client_id,\n    redirect_uri,\n    response_type,\n    scope,\n    // optional\n    state_data,\n    response_mode,\n    request_type,\n    client_secret,\n    nonce,\n    url_state,\n    resource,\n    skipUserInfo,\n    extraQueryParams,\n    extraTokenParams,\n    disablePKCE,\n    dpopJkt,\n    omitScopeWhenRequesting,\n    ...optionalParams\n  }) {\n    if (!url) {\n      this._logger.error(\"create: No url passed\");\n      throw new Error(\"url\");\n    }\n    if (!client_id) {\n      this._logger.error(\"create: No client_id passed\");\n      throw new Error(\"client_id\");\n    }\n    if (!redirect_uri) {\n      this._logger.error(\"create: No redirect_uri passed\");\n      throw new Error(\"redirect_uri\");\n    }\n    if (!response_type) {\n      this._logger.error(\"create: No response_type passed\");\n      throw new Error(\"response_type\");\n    }\n    if (!scope) {\n      this._logger.error(\"create: No scope passed\");\n      throw new Error(\"scope\");\n    }\n    if (!authority) {\n      this._logger.error(\"create: No authority passed\");\n      throw new Error(\"authority\");\n    }\n    const state = await SigninState.create({\n      data: state_data,\n      request_type,\n      url_state,\n      code_verifier: !disablePKCE,\n      client_id,\n      authority,\n      redirect_uri,\n      response_mode,\n      client_secret,\n      scope,\n      extraTokenParams,\n      skipUserInfo\n    });\n    const parsedUrl = new URL(url);\n    parsedUrl.searchParams.append(\"client_id\", client_id);\n    parsedUrl.searchParams.append(\"redirect_uri\", redirect_uri);\n    parsedUrl.searchParams.append(\"response_type\", response_type);\n    if (!omitScopeWhenRequesting) {\n      parsedUrl.searchParams.append(\"scope\", scope);\n    }\n    if (nonce) {\n      parsedUrl.searchParams.append(\"nonce\", nonce);\n    }\n    if (dpopJkt) {\n      parsedUrl.searchParams.append(\"dpop_jkt\", dpopJkt);\n    }\n    let stateParam = state.id;\n    if (url_state) {\n      stateParam = `${stateParam}${URL_STATE_DELIMITER}${url_state}`;\n    }\n    parsedUrl.searchParams.append(\"state\", stateParam);\n    if (state.code_challenge) {\n      parsedUrl.searchParams.append(\"code_challenge\", state.code_challenge);\n      parsedUrl.searchParams.append(\"code_challenge_method\", \"S256\");\n    }\n    if (resource) {\n      const resources = Array.isArray(resource) ? resource : [resource];\n      resources.forEach(r => parsedUrl.searchParams.append(\"resource\", r));\n    }\n    for (const [key, value] of Object.entries({\n      response_mode,\n      ...optionalParams,\n      ...extraQueryParams\n    })) {\n      if (value != null) {\n        parsedUrl.searchParams.append(key, value.toString());\n      }\n    }\n    return new _SigninRequest({\n      url: parsedUrl.href,\n      state\n    });\n  }\n};\n_SigninRequest._logger = new Logger(\"SigninRequest\");\nvar SigninRequest = _SigninRequest;\n\n// src/SigninResponse.ts\nvar OidcScope = \"openid\";\nvar SigninResponse = class {\n  constructor(params) {\n    /** @see {@link User.access_token} */\n    this.access_token = \"\";\n    /** @see {@link User.token_type} */\n    this.token_type = \"\";\n    /** @see {@link User.profile} */\n    this.profile = {};\n    this.state = params.get(\"state\");\n    this.session_state = params.get(\"session_state\");\n    if (this.state) {\n      const splitState = decodeURIComponent(this.state).split(URL_STATE_DELIMITER);\n      this.state = splitState[0];\n      if (splitState.length > 1) {\n        this.url_state = splitState.slice(1).join(URL_STATE_DELIMITER);\n      }\n    }\n    this.error = params.get(\"error\");\n    this.error_description = params.get(\"error_description\");\n    this.error_uri = params.get(\"error_uri\");\n    this.code = params.get(\"code\");\n  }\n  get expires_in() {\n    if (this.expires_at === void 0) {\n      return void 0;\n    }\n    return this.expires_at - Timer.getEpochTime();\n  }\n  set expires_in(value) {\n    if (typeof value === \"string\") value = Number(value);\n    if (value !== void 0 && value >= 0) {\n      this.expires_at = Math.floor(value) + Timer.getEpochTime();\n    }\n  }\n  get isOpenId() {\n    var _a;\n    return ((_a = this.scope) == null ? void 0 : _a.split(\" \").includes(OidcScope)) || !!this.id_token;\n  }\n};\n\n// src/SignoutRequest.ts\nvar SignoutRequest = class {\n  constructor({\n    url,\n    state_data,\n    id_token_hint,\n    post_logout_redirect_uri,\n    extraQueryParams,\n    request_type,\n    client_id,\n    url_state\n  }) {\n    this._logger = new Logger(\"SignoutRequest\");\n    if (!url) {\n      this._logger.error(\"ctor: No url passed\");\n      throw new Error(\"url\");\n    }\n    const parsedUrl = new URL(url);\n    if (id_token_hint) {\n      parsedUrl.searchParams.append(\"id_token_hint\", id_token_hint);\n    }\n    if (client_id) {\n      parsedUrl.searchParams.append(\"client_id\", client_id);\n    }\n    if (post_logout_redirect_uri) {\n      parsedUrl.searchParams.append(\"post_logout_redirect_uri\", post_logout_redirect_uri);\n      if (state_data || url_state) {\n        this.state = new State({\n          data: state_data,\n          request_type,\n          url_state\n        });\n        let stateParam = this.state.id;\n        if (url_state) {\n          stateParam = `${stateParam}${URL_STATE_DELIMITER}${url_state}`;\n        }\n        parsedUrl.searchParams.append(\"state\", stateParam);\n      }\n    }\n    for (const [key, value] of Object.entries({\n      ...extraQueryParams\n    })) {\n      if (value != null) {\n        parsedUrl.searchParams.append(key, value.toString());\n      }\n    }\n    this.url = parsedUrl.href;\n  }\n};\n\n// src/SignoutResponse.ts\nvar SignoutResponse = class {\n  constructor(params) {\n    this.state = params.get(\"state\");\n    if (this.state) {\n      const splitState = decodeURIComponent(this.state).split(URL_STATE_DELIMITER);\n      this.state = splitState[0];\n      if (splitState.length > 1) {\n        this.url_state = splitState.slice(1).join(URL_STATE_DELIMITER);\n      }\n    }\n    this.error = params.get(\"error\");\n    this.error_description = params.get(\"error_description\");\n    this.error_uri = params.get(\"error_uri\");\n  }\n};\n\n// src/ClaimsService.ts\nvar DefaultProtocolClaims = [\"nbf\", \"jti\", \"auth_time\", \"nonce\", \"acr\", \"amr\", \"azp\", \"at_hash\"\n// https://openid.net/specs/openid-connect-core-1_0.html#CodeIDToken\n];\nvar InternalRequiredProtocolClaims = [\"sub\", \"iss\", \"aud\", \"exp\", \"iat\"];\nvar ClaimsService = class {\n  constructor(_settings) {\n    this._settings = _settings;\n    this._logger = new Logger(\"ClaimsService\");\n  }\n  filterProtocolClaims(claims) {\n    const result = {\n      ...claims\n    };\n    if (this._settings.filterProtocolClaims) {\n      let protocolClaims;\n      if (Array.isArray(this._settings.filterProtocolClaims)) {\n        protocolClaims = this._settings.filterProtocolClaims;\n      } else {\n        protocolClaims = DefaultProtocolClaims;\n      }\n      for (const claim of protocolClaims) {\n        if (!InternalRequiredProtocolClaims.includes(claim)) {\n          delete result[claim];\n        }\n      }\n    }\n    return result;\n  }\n  mergeClaims(claims1, claims2) {\n    const result = {\n      ...claims1\n    };\n    for (const [claim, values] of Object.entries(claims2)) {\n      if (result[claim] !== values) {\n        if (Array.isArray(result[claim]) || Array.isArray(values)) {\n          if (this._settings.mergeClaimsStrategy.array == \"replace\") {\n            result[claim] = values;\n          } else {\n            const mergedValues = Array.isArray(result[claim]) ? result[claim] : [result[claim]];\n            for (const value of Array.isArray(values) ? values : [values]) {\n              if (!mergedValues.includes(value)) {\n                mergedValues.push(value);\n              }\n            }\n            result[claim] = mergedValues;\n          }\n        } else if (typeof result[claim] === \"object\" && typeof values === \"object\") {\n          result[claim] = this.mergeClaims(result[claim], values);\n        } else {\n          result[claim] = values;\n        }\n      }\n    }\n    return result;\n  }\n};\n\n// src/DPoPStore.ts\nvar DPoPState = class {\n  constructor(keys, nonce) {\n    this.keys = keys;\n    this.nonce = nonce;\n  }\n};\n\n// src/OidcClient.ts\nvar OidcClient = class {\n  constructor(settings, metadataService) {\n    this._logger = new Logger(\"OidcClient\");\n    this.settings = settings instanceof OidcClientSettingsStore ? settings : new OidcClientSettingsStore(settings);\n    this.metadataService = metadataService != null ? metadataService : new MetadataService(this.settings);\n    this._claimsService = new ClaimsService(this.settings);\n    this._validator = new ResponseValidator(this.settings, this.metadataService, this._claimsService);\n    this._tokenClient = new TokenClient(this.settings, this.metadataService);\n  }\n  async createSigninRequest({\n    state,\n    request,\n    request_uri,\n    request_type,\n    id_token_hint,\n    login_hint,\n    skipUserInfo,\n    nonce,\n    url_state,\n    response_type = this.settings.response_type,\n    scope = this.settings.scope,\n    redirect_uri = this.settings.redirect_uri,\n    prompt = this.settings.prompt,\n    display = this.settings.display,\n    max_age = this.settings.max_age,\n    ui_locales = this.settings.ui_locales,\n    acr_values = this.settings.acr_values,\n    resource = this.settings.resource,\n    response_mode = this.settings.response_mode,\n    extraQueryParams = this.settings.extraQueryParams,\n    extraTokenParams = this.settings.extraTokenParams,\n    dpopJkt,\n    omitScopeWhenRequesting = this.settings.omitScopeWhenRequesting\n  }) {\n    const logger2 = this._logger.create(\"createSigninRequest\");\n    if (response_type !== \"code\") {\n      throw new Error(\"Only the Authorization Code flow (with PKCE) is supported\");\n    }\n    const url = await this.metadataService.getAuthorizationEndpoint();\n    logger2.debug(\"Received authorization endpoint\", url);\n    const signinRequest = await SigninRequest.create({\n      url,\n      authority: this.settings.authority,\n      client_id: this.settings.client_id,\n      redirect_uri,\n      response_type,\n      scope,\n      state_data: state,\n      url_state,\n      prompt,\n      display,\n      max_age,\n      ui_locales,\n      id_token_hint,\n      login_hint,\n      acr_values,\n      dpopJkt,\n      resource,\n      request,\n      request_uri,\n      extraQueryParams,\n      extraTokenParams,\n      request_type,\n      response_mode,\n      client_secret: this.settings.client_secret,\n      skipUserInfo,\n      nonce,\n      disablePKCE: this.settings.disablePKCE,\n      omitScopeWhenRequesting\n    });\n    await this.clearStaleState();\n    const signinState = signinRequest.state;\n    await this.settings.stateStore.set(signinState.id, signinState.toStorageString());\n    return signinRequest;\n  }\n  async readSigninResponseState(url, removeState = false) {\n    const logger2 = this._logger.create(\"readSigninResponseState\");\n    const response = new SigninResponse(UrlUtils.readParams(url, this.settings.response_mode));\n    if (!response.state) {\n      logger2.throw(new Error(\"No state in response\"));\n      throw null;\n    }\n    const storedStateString = await this.settings.stateStore[removeState ? \"remove\" : \"get\"](response.state);\n    if (!storedStateString) {\n      logger2.throw(new Error(\"No matching state found in storage\"));\n      throw null;\n    }\n    const state = await SigninState.fromStorageString(storedStateString);\n    return {\n      state,\n      response\n    };\n  }\n  async processSigninResponse(url, extraHeaders, removeState = true) {\n    const logger2 = this._logger.create(\"processSigninResponse\");\n    const {\n      state,\n      response\n    } = await this.readSigninResponseState(url, removeState);\n    logger2.debug(\"received state from storage; validating response\");\n    if (this.settings.dpop && this.settings.dpop.store) {\n      const dpopProof = await this.getDpopProof(this.settings.dpop.store);\n      extraHeaders = {\n        ...extraHeaders,\n        \"DPoP\": dpopProof\n      };\n    }\n    try {\n      await this._validator.validateSigninResponse(response, state, extraHeaders);\n    } catch (err) {\n      if (err instanceof ErrorDPoPNonce && this.settings.dpop) {\n        const dpopProof = await this.getDpopProof(this.settings.dpop.store, err.nonce);\n        extraHeaders[\"DPoP\"] = dpopProof;\n        await this._validator.validateSigninResponse(response, state, extraHeaders);\n      } else {\n        throw err;\n      }\n    }\n    return response;\n  }\n  async getDpopProof(dpopStore, nonce) {\n    let keyPair;\n    let dpopState;\n    if (!(await dpopStore.getAllKeys()).includes(this.settings.client_id)) {\n      keyPair = await CryptoUtils.generateDPoPKeys();\n      dpopState = new DPoPState(keyPair, nonce);\n      await dpopStore.set(this.settings.client_id, dpopState);\n    } else {\n      dpopState = await dpopStore.get(this.settings.client_id);\n      if (dpopState.nonce !== nonce && nonce) {\n        dpopState.nonce = nonce;\n        await dpopStore.set(this.settings.client_id, dpopState);\n      }\n    }\n    return await CryptoUtils.generateDPoPProof({\n      url: await this.metadataService.getTokenEndpoint(false),\n      httpMethod: \"POST\",\n      keyPair: dpopState.keys,\n      nonce: dpopState.nonce\n    });\n  }\n  async processResourceOwnerPasswordCredentials({\n    username,\n    password,\n    skipUserInfo = false,\n    extraTokenParams = {}\n  }) {\n    const tokenResponse = await this._tokenClient.exchangeCredentials({\n      username,\n      password,\n      ...extraTokenParams\n    });\n    const signinResponse = new SigninResponse(new URLSearchParams());\n    Object.assign(signinResponse, tokenResponse);\n    await this._validator.validateCredentialsResponse(signinResponse, skipUserInfo);\n    return signinResponse;\n  }\n  async useRefreshToken({\n    state,\n    redirect_uri,\n    resource,\n    timeoutInSeconds,\n    extraHeaders,\n    extraTokenParams\n  }) {\n    var _a;\n    const logger2 = this._logger.create(\"useRefreshToken\");\n    let scope;\n    if (this.settings.refreshTokenAllowedScope === void 0) {\n      scope = state.scope;\n    } else {\n      const allowableScopes = this.settings.refreshTokenAllowedScope.split(\" \");\n      const providedScopes = ((_a = state.scope) == null ? void 0 : _a.split(\" \")) || [];\n      scope = providedScopes.filter(s => allowableScopes.includes(s)).join(\" \");\n    }\n    if (this.settings.dpop && this.settings.dpop.store) {\n      const dpopProof = await this.getDpopProof(this.settings.dpop.store);\n      extraHeaders = {\n        ...extraHeaders,\n        \"DPoP\": dpopProof\n      };\n    }\n    let result;\n    try {\n      result = await this._tokenClient.exchangeRefreshToken({\n        refresh_token: state.refresh_token,\n        // provide the (possible filtered) scope list\n        scope,\n        redirect_uri,\n        resource,\n        timeoutInSeconds,\n        extraHeaders,\n        ...extraTokenParams\n      });\n    } catch (err) {\n      if (err instanceof ErrorDPoPNonce && this.settings.dpop) {\n        extraHeaders[\"DPoP\"] = await this.getDpopProof(this.settings.dpop.store, err.nonce);\n        result = await this._tokenClient.exchangeRefreshToken({\n          refresh_token: state.refresh_token,\n          // provide the (possible filtered) scope list\n          scope,\n          redirect_uri,\n          resource,\n          timeoutInSeconds,\n          extraHeaders,\n          ...extraTokenParams\n        });\n      } else {\n        throw err;\n      }\n    }\n    const response = new SigninResponse(new URLSearchParams());\n    Object.assign(response, result);\n    logger2.debug(\"validating response\", response);\n    await this._validator.validateRefreshResponse(response, {\n      ...state,\n      // override the scope in the state handed over to the validator\n      // so it can set the granted scope to the requested scope in case none is included in the response\n      scope\n    });\n    return response;\n  }\n  async createSignoutRequest({\n    state,\n    id_token_hint,\n    client_id,\n    request_type,\n    url_state,\n    post_logout_redirect_uri = this.settings.post_logout_redirect_uri,\n    extraQueryParams = this.settings.extraQueryParams\n  } = {}) {\n    const logger2 = this._logger.create(\"createSignoutRequest\");\n    const url = await this.metadataService.getEndSessionEndpoint();\n    if (!url) {\n      logger2.throw(new Error(\"No end session endpoint\"));\n      throw null;\n    }\n    logger2.debug(\"Received end session endpoint\", url);\n    if (!client_id && post_logout_redirect_uri && !id_token_hint) {\n      client_id = this.settings.client_id;\n    }\n    const request = new SignoutRequest({\n      url,\n      id_token_hint,\n      client_id,\n      post_logout_redirect_uri,\n      state_data: state,\n      extraQueryParams,\n      request_type,\n      url_state\n    });\n    await this.clearStaleState();\n    const signoutState = request.state;\n    if (signoutState) {\n      logger2.debug(\"Signout request has state to persist\");\n      await this.settings.stateStore.set(signoutState.id, signoutState.toStorageString());\n    }\n    return request;\n  }\n  async readSignoutResponseState(url, removeState = false) {\n    const logger2 = this._logger.create(\"readSignoutResponseState\");\n    const response = new SignoutResponse(UrlUtils.readParams(url, this.settings.response_mode));\n    if (!response.state) {\n      logger2.debug(\"No state in response\");\n      if (response.error) {\n        logger2.warn(\"Response was error:\", response.error);\n        throw new ErrorResponse(response);\n      }\n      return {\n        state: void 0,\n        response\n      };\n    }\n    const storedStateString = await this.settings.stateStore[removeState ? \"remove\" : \"get\"](response.state);\n    if (!storedStateString) {\n      logger2.throw(new Error(\"No matching state found in storage\"));\n      throw null;\n    }\n    const state = await State.fromStorageString(storedStateString);\n    return {\n      state,\n      response\n    };\n  }\n  async processSignoutResponse(url) {\n    const logger2 = this._logger.create(\"processSignoutResponse\");\n    const {\n      state,\n      response\n    } = await this.readSignoutResponseState(url, true);\n    if (state) {\n      logger2.debug(\"Received state from storage; validating response\");\n      this._validator.validateSignoutResponse(response, state);\n    } else {\n      logger2.debug(\"No state from storage; skipping response validation\");\n    }\n    return response;\n  }\n  clearStaleState() {\n    this._logger.create(\"clearStaleState\");\n    return State.clearStaleState(this.settings.stateStore, this.settings.staleStateAgeInSeconds);\n  }\n  async revokeToken(token, type) {\n    this._logger.create(\"revokeToken\");\n    return await this._tokenClient.revoke({\n      token,\n      token_type_hint: type\n    });\n  }\n};\n\n// src/SessionMonitor.ts\nvar SessionMonitor = class {\n  constructor(_userManager) {\n    this._userManager = _userManager;\n    this._logger = new Logger(\"SessionMonitor\");\n    this._start = async user => {\n      const session_state = user.session_state;\n      if (!session_state) {\n        return;\n      }\n      const logger2 = this._logger.create(\"_start\");\n      if (user.profile) {\n        this._sub = user.profile.sub;\n        logger2.debug(\"session_state\", session_state, \", sub\", this._sub);\n      } else {\n        this._sub = void 0;\n        logger2.debug(\"session_state\", session_state, \", anonymous user\");\n      }\n      if (this._checkSessionIFrame) {\n        this._checkSessionIFrame.start(session_state);\n        return;\n      }\n      try {\n        const url = await this._userManager.metadataService.getCheckSessionIframe();\n        if (url) {\n          logger2.debug(\"initializing check session iframe\");\n          const client_id = this._userManager.settings.client_id;\n          const intervalInSeconds = this._userManager.settings.checkSessionIntervalInSeconds;\n          const stopOnError = this._userManager.settings.stopCheckSessionOnError;\n          const checkSessionIFrame = new CheckSessionIFrame(this._callback, client_id, url, intervalInSeconds, stopOnError);\n          await checkSessionIFrame.load();\n          this._checkSessionIFrame = checkSessionIFrame;\n          checkSessionIFrame.start(session_state);\n        } else {\n          logger2.warn(\"no check session iframe found in the metadata\");\n        }\n      } catch (err) {\n        logger2.error(\"Error from getCheckSessionIframe:\", err instanceof Error ? err.message : err);\n      }\n    };\n    this._stop = () => {\n      const logger2 = this._logger.create(\"_stop\");\n      this._sub = void 0;\n      if (this._checkSessionIFrame) {\n        this._checkSessionIFrame.stop();\n      }\n      if (this._userManager.settings.monitorAnonymousSession) {\n        const timerHandle = setInterval(async () => {\n          clearInterval(timerHandle);\n          try {\n            const session = await this._userManager.querySessionStatus();\n            if (session) {\n              const tmpUser = {\n                session_state: session.session_state,\n                profile: session.sub ? {\n                  sub: session.sub\n                } : null\n              };\n              void this._start(tmpUser);\n            }\n          } catch (err) {\n            logger2.error(\"error from querySessionStatus\", err instanceof Error ? err.message : err);\n          }\n        }, 1e3);\n      }\n    };\n    this._callback = async () => {\n      const logger2 = this._logger.create(\"_callback\");\n      try {\n        const session = await this._userManager.querySessionStatus();\n        let raiseEvent = true;\n        if (session && this._checkSessionIFrame) {\n          if (session.sub === this._sub) {\n            raiseEvent = false;\n            this._checkSessionIFrame.start(session.session_state);\n            logger2.debug(\"same sub still logged in at OP, session state has changed, restarting check session iframe; session_state\", session.session_state);\n            await this._userManager.events._raiseUserSessionChanged();\n          } else {\n            logger2.debug(\"different subject signed into OP\", session.sub);\n          }\n        } else {\n          logger2.debug(\"subject no longer signed into OP\");\n        }\n        if (raiseEvent) {\n          if (this._sub) {\n            await this._userManager.events._raiseUserSignedOut();\n          } else {\n            await this._userManager.events._raiseUserSignedIn();\n          }\n        } else {\n          logger2.debug(\"no change in session detected, no event to raise\");\n        }\n      } catch (err) {\n        if (this._sub) {\n          logger2.debug(\"Error calling queryCurrentSigninSession; raising signed out event\", err);\n          await this._userManager.events._raiseUserSignedOut();\n        }\n      }\n    };\n    if (!_userManager) {\n      this._logger.throw(new Error(\"No user manager passed\"));\n    }\n    this._userManager.events.addUserLoaded(this._start);\n    this._userManager.events.addUserUnloaded(this._stop);\n    this._init().catch(err => {\n      this._logger.error(err);\n    });\n  }\n  async _init() {\n    this._logger.create(\"_init\");\n    const user = await this._userManager.getUser();\n    if (user) {\n      void this._start(user);\n    } else if (this._userManager.settings.monitorAnonymousSession) {\n      const session = await this._userManager.querySessionStatus();\n      if (session) {\n        const tmpUser = {\n          session_state: session.session_state,\n          profile: session.sub ? {\n            sub: session.sub\n          } : null\n        };\n        void this._start(tmpUser);\n      }\n    }\n  }\n};\n\n// src/User.ts\nvar User = class _User {\n  constructor(args) {\n    var _a;\n    this.id_token = args.id_token;\n    this.session_state = (_a = args.session_state) != null ? _a : null;\n    this.access_token = args.access_token;\n    this.refresh_token = args.refresh_token;\n    this.token_type = args.token_type;\n    this.scope = args.scope;\n    this.profile = args.profile;\n    this.expires_at = args.expires_at;\n    this.state = args.userState;\n    this.url_state = args.url_state;\n  }\n  /** Computed number of seconds the access token has remaining. */\n  get expires_in() {\n    if (this.expires_at === void 0) {\n      return void 0;\n    }\n    return this.expires_at - Timer.getEpochTime();\n  }\n  set expires_in(value) {\n    if (value !== void 0) {\n      this.expires_at = Math.floor(value) + Timer.getEpochTime();\n    }\n  }\n  /** Computed value indicating if the access token is expired. */\n  get expired() {\n    const expires_in = this.expires_in;\n    if (expires_in === void 0) {\n      return void 0;\n    }\n    return expires_in <= 0;\n  }\n  /** Array representing the parsed values from the `scope`. */\n  get scopes() {\n    var _a, _b;\n    return (_b = (_a = this.scope) == null ? void 0 : _a.split(\" \")) != null ? _b : [];\n  }\n  toStorageString() {\n    new Logger(\"User\").create(\"toStorageString\");\n    return JSON.stringify({\n      id_token: this.id_token,\n      session_state: this.session_state,\n      access_token: this.access_token,\n      refresh_token: this.refresh_token,\n      token_type: this.token_type,\n      scope: this.scope,\n      profile: this.profile,\n      expires_at: this.expires_at\n    });\n  }\n  static fromStorageString(storageString) {\n    Logger.createStatic(\"User\", \"fromStorageString\");\n    return new _User(JSON.parse(storageString));\n  }\n};\n\n// src/navigators/AbstractChildWindow.ts\nvar messageSource = \"oidc-client\";\nvar AbstractChildWindow = class {\n  constructor() {\n    this._abort = new Event(\"Window navigation aborted\");\n    this._disposeHandlers = /* @__PURE__ */new Set();\n    this._window = null;\n  }\n  async navigate(params) {\n    const logger2 = this._logger.create(\"navigate\");\n    if (!this._window) {\n      throw new Error(\"Attempted to navigate on a disposed window\");\n    }\n    logger2.debug(\"setting URL in window\");\n    this._window.location.replace(params.url);\n    const {\n      url,\n      keepOpen\n    } = await new Promise((resolve, reject) => {\n      const listener = e => {\n        var _a;\n        const data = e.data;\n        const origin = (_a = params.scriptOrigin) != null ? _a : window.location.origin;\n        if (e.origin !== origin || (data == null ? void 0 : data.source) !== messageSource) {\n          return;\n        }\n        try {\n          const state = UrlUtils.readParams(data.url, params.response_mode).get(\"state\");\n          if (!state) {\n            logger2.warn(\"no state found in response url\");\n          }\n          if (e.source !== this._window && state !== params.state) {\n            return;\n          }\n        } catch {\n          this._dispose();\n          reject(new Error(\"Invalid response from window\"));\n        }\n        resolve(data);\n      };\n      window.addEventListener(\"message\", listener, false);\n      this._disposeHandlers.add(() => window.removeEventListener(\"message\", listener, false));\n      this._disposeHandlers.add(this._abort.addHandler(reason => {\n        this._dispose();\n        reject(reason);\n      }));\n    });\n    logger2.debug(\"got response from window\");\n    this._dispose();\n    if (!keepOpen) {\n      this.close();\n    }\n    return {\n      url\n    };\n  }\n  _dispose() {\n    this._logger.create(\"_dispose\");\n    for (const dispose of this._disposeHandlers) {\n      dispose();\n    }\n    this._disposeHandlers.clear();\n  }\n  static _notifyParent(parent, url, keepOpen = false, targetOrigin = window.location.origin) {\n    parent.postMessage({\n      source: messageSource,\n      url,\n      keepOpen\n    }, targetOrigin);\n  }\n};\n\n// src/UserManagerSettings.ts\nvar DefaultPopupWindowFeatures = {\n  location: false,\n  toolbar: false,\n  height: 640,\n  closePopupWindowAfterInSeconds: -1\n};\nvar DefaultPopupTarget = \"_blank\";\nvar DefaultAccessTokenExpiringNotificationTimeInSeconds = 60;\nvar DefaultCheckSessionIntervalInSeconds = 2;\nvar DefaultSilentRequestTimeoutInSeconds = 10;\nvar UserManagerSettingsStore = class extends OidcClientSettingsStore {\n  constructor(args) {\n    const {\n      popup_redirect_uri = args.redirect_uri,\n      popup_post_logout_redirect_uri = args.post_logout_redirect_uri,\n      popupWindowFeatures = DefaultPopupWindowFeatures,\n      popupWindowTarget = DefaultPopupTarget,\n      redirectMethod = \"assign\",\n      redirectTarget = \"self\",\n      iframeNotifyParentOrigin = args.iframeNotifyParentOrigin,\n      iframeScriptOrigin = args.iframeScriptOrigin,\n      requestTimeoutInSeconds,\n      silent_redirect_uri = args.redirect_uri,\n      silentRequestTimeoutInSeconds,\n      automaticSilentRenew = true,\n      validateSubOnSilentRenew = true,\n      includeIdTokenInSilentRenew = false,\n      monitorSession = false,\n      monitorAnonymousSession = false,\n      checkSessionIntervalInSeconds = DefaultCheckSessionIntervalInSeconds,\n      query_status_response_type = \"code\",\n      stopCheckSessionOnError = true,\n      revokeTokenTypes = [\"access_token\", \"refresh_token\"],\n      revokeTokensOnSignout = false,\n      includeIdTokenInSilentSignout = false,\n      accessTokenExpiringNotificationTimeInSeconds = DefaultAccessTokenExpiringNotificationTimeInSeconds,\n      userStore\n    } = args;\n    super(args);\n    this.popup_redirect_uri = popup_redirect_uri;\n    this.popup_post_logout_redirect_uri = popup_post_logout_redirect_uri;\n    this.popupWindowFeatures = popupWindowFeatures;\n    this.popupWindowTarget = popupWindowTarget;\n    this.redirectMethod = redirectMethod;\n    this.redirectTarget = redirectTarget;\n    this.iframeNotifyParentOrigin = iframeNotifyParentOrigin;\n    this.iframeScriptOrigin = iframeScriptOrigin;\n    this.silent_redirect_uri = silent_redirect_uri;\n    this.silentRequestTimeoutInSeconds = silentRequestTimeoutInSeconds || requestTimeoutInSeconds || DefaultSilentRequestTimeoutInSeconds;\n    this.automaticSilentRenew = automaticSilentRenew;\n    this.validateSubOnSilentRenew = validateSubOnSilentRenew;\n    this.includeIdTokenInSilentRenew = includeIdTokenInSilentRenew;\n    this.monitorSession = monitorSession;\n    this.monitorAnonymousSession = monitorAnonymousSession;\n    this.checkSessionIntervalInSeconds = checkSessionIntervalInSeconds;\n    this.stopCheckSessionOnError = stopCheckSessionOnError;\n    this.query_status_response_type = query_status_response_type;\n    this.revokeTokenTypes = revokeTokenTypes;\n    this.revokeTokensOnSignout = revokeTokensOnSignout;\n    this.includeIdTokenInSilentSignout = includeIdTokenInSilentSignout;\n    this.accessTokenExpiringNotificationTimeInSeconds = accessTokenExpiringNotificationTimeInSeconds;\n    if (userStore) {\n      this.userStore = userStore;\n    } else {\n      const store = typeof window !== \"undefined\" ? window.sessionStorage : new InMemoryWebStorage();\n      this.userStore = new WebStorageStateStore({\n        store\n      });\n    }\n  }\n};\n\n// src/navigators/IFrameWindow.ts\nvar IFrameWindow = class _IFrameWindow extends AbstractChildWindow {\n  constructor({\n    silentRequestTimeoutInSeconds = DefaultSilentRequestTimeoutInSeconds\n  }) {\n    super();\n    this._logger = new Logger(\"IFrameWindow\");\n    this._timeoutInSeconds = silentRequestTimeoutInSeconds;\n    this._frame = _IFrameWindow.createHiddenIframe();\n    this._window = this._frame.contentWindow;\n  }\n  static createHiddenIframe() {\n    const iframe = window.document.createElement(\"iframe\");\n    iframe.style.visibility = \"hidden\";\n    iframe.style.position = \"fixed\";\n    iframe.style.left = \"-1000px\";\n    iframe.style.top = \"0\";\n    iframe.width = \"0\";\n    iframe.height = \"0\";\n    window.document.body.appendChild(iframe);\n    return iframe;\n  }\n  async navigate(params) {\n    this._logger.debug(\"navigate: Using timeout of:\", this._timeoutInSeconds);\n    const timer = setTimeout(() => void this._abort.raise(new ErrorTimeout(\"IFrame timed out without a response\")), this._timeoutInSeconds * 1e3);\n    this._disposeHandlers.add(() => clearTimeout(timer));\n    return await super.navigate(params);\n  }\n  close() {\n    var _a;\n    if (this._frame) {\n      if (this._frame.parentNode) {\n        this._frame.addEventListener(\"load\", ev => {\n          var _a2;\n          const frame = ev.target;\n          (_a2 = frame.parentNode) == null ? void 0 : _a2.removeChild(frame);\n          void this._abort.raise(new Error(\"IFrame removed from DOM\"));\n        }, true);\n        (_a = this._frame.contentWindow) == null ? void 0 : _a.location.replace(\"about:blank\");\n      }\n      this._frame = null;\n    }\n    this._window = null;\n  }\n  static notifyParent(url, targetOrigin) {\n    return super._notifyParent(window.parent, url, false, targetOrigin);\n  }\n};\n\n// src/navigators/IFrameNavigator.ts\nvar IFrameNavigator = class {\n  constructor(_settings) {\n    this._settings = _settings;\n    this._logger = new Logger(\"IFrameNavigator\");\n  }\n  async prepare({\n    silentRequestTimeoutInSeconds = this._settings.silentRequestTimeoutInSeconds\n  }) {\n    return new IFrameWindow({\n      silentRequestTimeoutInSeconds\n    });\n  }\n  async callback(url) {\n    this._logger.create(\"callback\");\n    IFrameWindow.notifyParent(url, this._settings.iframeNotifyParentOrigin);\n  }\n};\n\n// src/navigators/PopupWindow.ts\nvar checkForPopupClosedInterval = 500;\nvar second = 1e3;\nvar PopupWindow = class extends AbstractChildWindow {\n  constructor({\n    popupWindowTarget = DefaultPopupTarget,\n    popupWindowFeatures = {},\n    popupSignal\n  }) {\n    super();\n    this._logger = new Logger(\"PopupWindow\");\n    const centeredPopup = PopupUtils.center({\n      ...DefaultPopupWindowFeatures,\n      ...popupWindowFeatures\n    });\n    this._window = window.open(void 0, popupWindowTarget, PopupUtils.serialize(centeredPopup));\n    if (popupSignal) {\n      popupSignal.addEventListener(\"abort\", () => {\n        var _a;\n        void this._abort.raise(new Error((_a = popupSignal.reason) != null ? _a : \"Popup aborted\"));\n      });\n    }\n    if (popupWindowFeatures.closePopupWindowAfterInSeconds && popupWindowFeatures.closePopupWindowAfterInSeconds > 0) {\n      setTimeout(() => {\n        if (!this._window || typeof this._window.closed !== \"boolean\" || this._window.closed) {\n          void this._abort.raise(new Error(\"Popup blocked by user\"));\n          return;\n        }\n        this.close();\n      }, popupWindowFeatures.closePopupWindowAfterInSeconds * second);\n    }\n  }\n  async navigate(params) {\n    var _a;\n    (_a = this._window) == null ? void 0 : _a.focus();\n    const popupClosedInterval = setInterval(() => {\n      if (!this._window || this._window.closed) {\n        void this._abort.raise(new Error(\"Popup closed by user\"));\n      }\n    }, checkForPopupClosedInterval);\n    this._disposeHandlers.add(() => clearInterval(popupClosedInterval));\n    return await super.navigate(params);\n  }\n  close() {\n    if (this._window) {\n      if (!this._window.closed) {\n        this._window.close();\n        void this._abort.raise(new Error(\"Popup closed\"));\n      }\n    }\n    this._window = null;\n  }\n  static notifyOpener(url, keepOpen) {\n    if (!window.opener) {\n      throw new Error(\"No window.opener. Can't complete notification.\");\n    }\n    return super._notifyParent(window.opener, url, keepOpen);\n  }\n};\n\n// src/navigators/PopupNavigator.ts\nvar PopupNavigator = class {\n  constructor(_settings) {\n    this._settings = _settings;\n    this._logger = new Logger(\"PopupNavigator\");\n  }\n  async prepare({\n    popupWindowFeatures = this._settings.popupWindowFeatures,\n    popupWindowTarget = this._settings.popupWindowTarget,\n    popupSignal\n  }) {\n    return new PopupWindow({\n      popupWindowFeatures,\n      popupWindowTarget,\n      popupSignal\n    });\n  }\n  async callback(url, {\n    keepOpen = false\n  }) {\n    this._logger.create(\"callback\");\n    PopupWindow.notifyOpener(url, keepOpen);\n  }\n};\n\n// src/navigators/RedirectNavigator.ts\nvar RedirectNavigator = class {\n  constructor(_settings) {\n    this._settings = _settings;\n    this._logger = new Logger(\"RedirectNavigator\");\n  }\n  async prepare({\n    redirectMethod = this._settings.redirectMethod,\n    redirectTarget = this._settings.redirectTarget\n  }) {\n    var _a;\n    this._logger.create(\"prepare\");\n    let targetWindow = window.self;\n    if (redirectTarget === \"top\") {\n      targetWindow = (_a = window.top) != null ? _a : window.self;\n    }\n    const redirect = targetWindow.location[redirectMethod].bind(targetWindow.location);\n    let abort;\n    return {\n      navigate: async params => {\n        this._logger.create(\"navigate\");\n        const promise = new Promise((resolve, reject) => {\n          abort = reject;\n        });\n        redirect(params.url);\n        return await promise;\n      },\n      close: () => {\n        this._logger.create(\"close\");\n        abort == null ? void 0 : abort(new Error(\"Redirect aborted\"));\n        targetWindow.stop();\n      }\n    };\n  }\n  async callback() {\n    return;\n  }\n};\n\n// src/UserManagerEvents.ts\nvar UserManagerEvents = class extends AccessTokenEvents {\n  constructor(settings) {\n    super({\n      expiringNotificationTimeInSeconds: settings.accessTokenExpiringNotificationTimeInSeconds\n    });\n    this._logger = new Logger(\"UserManagerEvents\");\n    this._userLoaded = new Event(\"User loaded\");\n    this._userUnloaded = new Event(\"User unloaded\");\n    this._silentRenewError = new Event(\"Silent renew error\");\n    this._userSignedIn = new Event(\"User signed in\");\n    this._userSignedOut = new Event(\"User signed out\");\n    this._userSessionChanged = new Event(\"User session changed\");\n  }\n  async load(user, raiseEvent = true) {\n    await super.load(user);\n    if (raiseEvent) {\n      await this._userLoaded.raise(user);\n    }\n  }\n  async unload() {\n    await super.unload();\n    await this._userUnloaded.raise();\n  }\n  /**\n   * Add callback: Raised when a user session has been established (or re-established).\n   */\n  addUserLoaded(cb) {\n    return this._userLoaded.addHandler(cb);\n  }\n  /**\n   * Remove callback: Raised when a user session has been established (or re-established).\n   */\n  removeUserLoaded(cb) {\n    return this._userLoaded.removeHandler(cb);\n  }\n  /**\n   * Add callback: Raised when a user session has been terminated.\n   */\n  addUserUnloaded(cb) {\n    return this._userUnloaded.addHandler(cb);\n  }\n  /**\n   * Remove callback: Raised when a user session has been terminated.\n   */\n  removeUserUnloaded(cb) {\n    return this._userUnloaded.removeHandler(cb);\n  }\n  /**\n   * Add callback: Raised when the automatic silent renew has failed.\n   */\n  addSilentRenewError(cb) {\n    return this._silentRenewError.addHandler(cb);\n  }\n  /**\n   * Remove callback: Raised when the automatic silent renew has failed.\n   */\n  removeSilentRenewError(cb) {\n    return this._silentRenewError.removeHandler(cb);\n  }\n  /**\n   * @internal\n   */\n  async _raiseSilentRenewError(e) {\n    await this._silentRenewError.raise(e);\n  }\n  /**\n   * Add callback: Raised when the user is signed in (when `monitorSession` is set).\n   * @see {@link UserManagerSettings.monitorSession}\n   */\n  addUserSignedIn(cb) {\n    return this._userSignedIn.addHandler(cb);\n  }\n  /**\n   * Remove callback: Raised when the user is signed in (when `monitorSession` is set).\n   */\n  removeUserSignedIn(cb) {\n    this._userSignedIn.removeHandler(cb);\n  }\n  /**\n   * @internal\n   */\n  async _raiseUserSignedIn() {\n    await this._userSignedIn.raise();\n  }\n  /**\n   * Add callback: Raised when the user's sign-in status at the OP has changed (when `monitorSession` is set).\n   * @see {@link UserManagerSettings.monitorSession}\n   */\n  addUserSignedOut(cb) {\n    return this._userSignedOut.addHandler(cb);\n  }\n  /**\n   * Remove callback: Raised when the user's sign-in status at the OP has changed (when `monitorSession` is set).\n   */\n  removeUserSignedOut(cb) {\n    this._userSignedOut.removeHandler(cb);\n  }\n  /**\n   * @internal\n   */\n  async _raiseUserSignedOut() {\n    await this._userSignedOut.raise();\n  }\n  /**\n   * Add callback: Raised when the user session changed (when `monitorSession` is set).\n   * @see {@link UserManagerSettings.monitorSession}\n   */\n  addUserSessionChanged(cb) {\n    return this._userSessionChanged.addHandler(cb);\n  }\n  /**\n   * Remove callback: Raised when the user session changed (when `monitorSession` is set).\n   */\n  removeUserSessionChanged(cb) {\n    this._userSessionChanged.removeHandler(cb);\n  }\n  /**\n   * @internal\n   */\n  async _raiseUserSessionChanged() {\n    await this._userSessionChanged.raise();\n  }\n};\n\n// src/SilentRenewService.ts\nvar SilentRenewService = class {\n  constructor(_userManager) {\n    this._userManager = _userManager;\n    this._logger = new Logger(\"SilentRenewService\");\n    this._isStarted = false;\n    this._retryTimer = new Timer(\"Retry Silent Renew\");\n    this._tokenExpiring = async () => {\n      const logger2 = this._logger.create(\"_tokenExpiring\");\n      try {\n        await this._userManager.signinSilent();\n        logger2.debug(\"silent token renewal successful\");\n      } catch (err) {\n        if (err instanceof ErrorTimeout) {\n          logger2.warn(\"ErrorTimeout from signinSilent:\", err, \"retry in 5s\");\n          this._retryTimer.init(5);\n          return;\n        }\n        logger2.error(\"Error from signinSilent:\", err);\n        await this._userManager.events._raiseSilentRenewError(err);\n      }\n    };\n  }\n  async start() {\n    const logger2 = this._logger.create(\"start\");\n    if (!this._isStarted) {\n      this._isStarted = true;\n      this._userManager.events.addAccessTokenExpiring(this._tokenExpiring);\n      this._retryTimer.addHandler(this._tokenExpiring);\n      try {\n        await this._userManager.getUser();\n      } catch (err) {\n        logger2.error(\"getUser error\", err);\n      }\n    }\n  }\n  stop() {\n    if (this._isStarted) {\n      this._retryTimer.cancel();\n      this._retryTimer.removeHandler(this._tokenExpiring);\n      this._userManager.events.removeAccessTokenExpiring(this._tokenExpiring);\n      this._isStarted = false;\n    }\n  }\n};\n\n// src/RefreshState.ts\nvar RefreshState = class {\n  constructor(args) {\n    this.refresh_token = args.refresh_token;\n    this.id_token = args.id_token;\n    this.session_state = args.session_state;\n    this.scope = args.scope;\n    this.profile = args.profile;\n    this.data = args.state;\n  }\n};\n\n// src/UserManager.ts\nvar UserManager = class {\n  constructor(settings, redirectNavigator, popupNavigator, iframeNavigator) {\n    this._logger = new Logger(\"UserManager\");\n    this.settings = new UserManagerSettingsStore(settings);\n    this._client = new OidcClient(settings);\n    this._redirectNavigator = redirectNavigator != null ? redirectNavigator : new RedirectNavigator(this.settings);\n    this._popupNavigator = popupNavigator != null ? popupNavigator : new PopupNavigator(this.settings);\n    this._iframeNavigator = iframeNavigator != null ? iframeNavigator : new IFrameNavigator(this.settings);\n    this._events = new UserManagerEvents(this.settings);\n    this._silentRenewService = new SilentRenewService(this);\n    if (this.settings.automaticSilentRenew) {\n      this.startSilentRenew();\n    }\n    this._sessionMonitor = null;\n    if (this.settings.monitorSession) {\n      this._sessionMonitor = new SessionMonitor(this);\n    }\n  }\n  /**\n   * Get object used to register for events raised by the `UserManager`.\n   */\n  get events() {\n    return this._events;\n  }\n  /**\n   * Get object used to access the metadata configuration of the identity provider.\n   */\n  get metadataService() {\n    return this._client.metadataService;\n  }\n  /**\n   * Load the `User` object for the currently authenticated user.\n   *\n   * @param raiseEvent - If `true`, the `UserLoaded` event will be raised. Defaults to false.\n   * @returns A promise\n   */\n  async getUser(raiseEvent = false) {\n    const logger2 = this._logger.create(\"getUser\");\n    const user = await this._loadUser();\n    if (user) {\n      logger2.info(\"user loaded\");\n      await this._events.load(user, raiseEvent);\n      return user;\n    }\n    logger2.info(\"user not found in storage\");\n    return null;\n  }\n  /**\n   * Remove from any storage the currently authenticated user.\n   *\n   * @returns A promise\n   */\n  async removeUser() {\n    const logger2 = this._logger.create(\"removeUser\");\n    await this.storeUser(null);\n    logger2.info(\"user removed from storage\");\n    await this._events.unload();\n  }\n  /**\n   * Trigger a redirect of the current window to the authorization endpoint.\n   *\n   * @returns A promise\n   *\n   * @throws `Error` In cases of wrong authentication.\n   */\n  async signinRedirect(args = {}) {\n    var _a;\n    this._logger.create(\"signinRedirect\");\n    const {\n      redirectMethod,\n      ...requestArgs\n    } = args;\n    let dpopJkt;\n    if ((_a = this.settings.dpop) == null ? void 0 : _a.bind_authorization_code) {\n      dpopJkt = await this.generateDPoPJkt(this.settings.dpop);\n    }\n    const handle = await this._redirectNavigator.prepare({\n      redirectMethod\n    });\n    await this._signinStart({\n      request_type: \"si:r\",\n      dpopJkt,\n      ...requestArgs\n    }, handle);\n  }\n  /**\n   * Process the response (callback) from the authorization endpoint.\n   * It is recommended to use {@link UserManager.signinCallback} instead.\n   *\n   * @returns A promise containing the authenticated `User`.\n   *\n   * @see {@link UserManager.signinCallback}\n   */\n  async signinRedirectCallback(url = window.location.href) {\n    const logger2 = this._logger.create(\"signinRedirectCallback\");\n    const user = await this._signinEnd(url);\n    if (user.profile && user.profile.sub) {\n      logger2.info(\"success, signed in subject\", user.profile.sub);\n    } else {\n      logger2.info(\"no subject\");\n    }\n    return user;\n  }\n  /**\n   * Trigger the signin with user/password.\n   *\n   * @returns A promise containing the authenticated `User`.\n   * @throws {@link ErrorResponse} In cases of wrong authentication.\n   */\n  async signinResourceOwnerCredentials({\n    username,\n    password,\n    skipUserInfo = false\n  }) {\n    const logger2 = this._logger.create(\"signinResourceOwnerCredential\");\n    const signinResponse = await this._client.processResourceOwnerPasswordCredentials({\n      username,\n      password,\n      skipUserInfo,\n      extraTokenParams: this.settings.extraTokenParams\n    });\n    logger2.debug(\"got signin response\");\n    const user = await this._buildUser(signinResponse);\n    if (user.profile && user.profile.sub) {\n      logger2.info(\"success, signed in subject\", user.profile.sub);\n    } else {\n      logger2.info(\"no subject\");\n    }\n    return user;\n  }\n  /**\n   * Trigger a request (via a popup window) to the authorization endpoint.\n   *\n   * @returns A promise containing the authenticated `User`.\n   * @throws `Error` In cases of wrong authentication.\n   */\n  async signinPopup(args = {}) {\n    var _a;\n    const logger2 = this._logger.create(\"signinPopup\");\n    let dpopJkt;\n    if ((_a = this.settings.dpop) == null ? void 0 : _a.bind_authorization_code) {\n      dpopJkt = await this.generateDPoPJkt(this.settings.dpop);\n    }\n    const {\n      popupWindowFeatures,\n      popupWindowTarget,\n      popupSignal,\n      ...requestArgs\n    } = args;\n    const url = this.settings.popup_redirect_uri;\n    if (!url) {\n      logger2.throw(new Error(\"No popup_redirect_uri configured\"));\n    }\n    const handle = await this._popupNavigator.prepare({\n      popupWindowFeatures,\n      popupWindowTarget,\n      popupSignal\n    });\n    const user = await this._signin({\n      request_type: \"si:p\",\n      redirect_uri: url,\n      display: \"popup\",\n      dpopJkt,\n      ...requestArgs\n    }, handle);\n    if (user) {\n      if (user.profile && user.profile.sub) {\n        logger2.info(\"success, signed in subject\", user.profile.sub);\n      } else {\n        logger2.info(\"no subject\");\n      }\n    }\n    return user;\n  }\n  /**\n   * Notify the opening window of response (callback) from the authorization endpoint.\n   * It is recommended to use {@link UserManager.signinCallback} instead.\n   *\n   * @returns A promise\n   *\n   * @see {@link UserManager.signinCallback}\n   */\n  async signinPopupCallback(url = window.location.href, keepOpen = false) {\n    const logger2 = this._logger.create(\"signinPopupCallback\");\n    await this._popupNavigator.callback(url, {\n      keepOpen\n    });\n    logger2.info(\"success\");\n  }\n  /**\n   * Trigger a silent request (via refresh token or an iframe) to the authorization endpoint.\n   *\n   * @returns A promise that contains the authenticated `User`.\n   */\n  async signinSilent(args = {}) {\n    var _a, _b;\n    const logger2 = this._logger.create(\"signinSilent\");\n    const {\n      silentRequestTimeoutInSeconds,\n      ...requestArgs\n    } = args;\n    let user = await this._loadUser();\n    if (user == null ? void 0 : user.refresh_token) {\n      logger2.debug(\"using refresh token\");\n      const state = new RefreshState(user);\n      return await this._useRefreshToken({\n        state,\n        redirect_uri: requestArgs.redirect_uri,\n        resource: requestArgs.resource,\n        extraTokenParams: requestArgs.extraTokenParams,\n        timeoutInSeconds: silentRequestTimeoutInSeconds\n      });\n    }\n    let dpopJkt;\n    if ((_a = this.settings.dpop) == null ? void 0 : _a.bind_authorization_code) {\n      dpopJkt = await this.generateDPoPJkt(this.settings.dpop);\n    }\n    const url = this.settings.silent_redirect_uri;\n    if (!url) {\n      logger2.throw(new Error(\"No silent_redirect_uri configured\"));\n    }\n    let verifySub;\n    if (user && this.settings.validateSubOnSilentRenew) {\n      logger2.debug(\"subject prior to silent renew:\", user.profile.sub);\n      verifySub = user.profile.sub;\n    }\n    const handle = await this._iframeNavigator.prepare({\n      silentRequestTimeoutInSeconds\n    });\n    user = await this._signin({\n      request_type: \"si:s\",\n      redirect_uri: url,\n      prompt: \"none\",\n      id_token_hint: this.settings.includeIdTokenInSilentRenew ? user == null ? void 0 : user.id_token : void 0,\n      dpopJkt,\n      ...requestArgs\n    }, handle, verifySub);\n    if (user) {\n      if ((_b = user.profile) == null ? void 0 : _b.sub) {\n        logger2.info(\"success, signed in subject\", user.profile.sub);\n      } else {\n        logger2.info(\"no subject\");\n      }\n    }\n    return user;\n  }\n  async _useRefreshToken(args) {\n    const response = await this._client.useRefreshToken({\n      timeoutInSeconds: this.settings.silentRequestTimeoutInSeconds,\n      ...args\n    });\n    const user = new User({\n      ...args.state,\n      ...response\n    });\n    await this.storeUser(user);\n    await this._events.load(user);\n    return user;\n  }\n  /**\n   *\n   * Notify the parent window of response (callback) from the authorization endpoint.\n   * It is recommended to use {@link UserManager.signinCallback} instead.\n   *\n   * @returns A promise\n   *\n   * @see {@link UserManager.signinCallback}\n   */\n  async signinSilentCallback(url = window.location.href) {\n    const logger2 = this._logger.create(\"signinSilentCallback\");\n    await this._iframeNavigator.callback(url);\n    logger2.info(\"success\");\n  }\n  /**\n   * Process any response (callback) from the authorization endpoint, by dispatching the request_type\n   * and executing one of the following functions:\n   * - {@link UserManager.signinRedirectCallback}\n   * - {@link UserManager.signinPopupCallback}\n   * - {@link UserManager.signinSilentCallback}\n   *\n   * @throws `Error` If request_type is unknown or signin cannot be processed.\n   */\n  async signinCallback(url = window.location.href) {\n    const {\n      state\n    } = await this._client.readSigninResponseState(url);\n    switch (state.request_type) {\n      case \"si:r\":\n        return await this.signinRedirectCallback(url);\n      case \"si:p\":\n        await this.signinPopupCallback(url);\n        break;\n      case \"si:s\":\n        await this.signinSilentCallback(url);\n        break;\n      default:\n        throw new Error(\"invalid response_type in state\");\n    }\n    return void 0;\n  }\n  /**\n   * Process any response (callback) from the end session endpoint, by dispatching the request_type\n   * and executing one of the following functions:\n   * - {@link UserManager.signoutRedirectCallback}\n   * - {@link UserManager.signoutPopupCallback}\n   * - {@link UserManager.signoutSilentCallback}\n   *\n   * @throws `Error` If request_type is unknown or signout cannot be processed.\n   */\n  async signoutCallback(url = window.location.href, keepOpen = false) {\n    const {\n      state\n    } = await this._client.readSignoutResponseState(url);\n    if (!state) {\n      return void 0;\n    }\n    switch (state.request_type) {\n      case \"so:r\":\n        return await this.signoutRedirectCallback(url);\n      case \"so:p\":\n        await this.signoutPopupCallback(url, keepOpen);\n        break;\n      case \"so:s\":\n        await this.signoutSilentCallback(url);\n        break;\n      default:\n        throw new Error(\"invalid response_type in state\");\n    }\n    return void 0;\n  }\n  /**\n   * Query OP for user's current signin status.\n   *\n   * @returns A promise object with session_state and subject identifier.\n   */\n  async querySessionStatus(args = {}) {\n    const logger2 = this._logger.create(\"querySessionStatus\");\n    const {\n      silentRequestTimeoutInSeconds,\n      ...requestArgs\n    } = args;\n    const url = this.settings.silent_redirect_uri;\n    if (!url) {\n      logger2.throw(new Error(\"No silent_redirect_uri configured\"));\n    }\n    const user = await this._loadUser();\n    const handle = await this._iframeNavigator.prepare({\n      silentRequestTimeoutInSeconds\n    });\n    const navResponse = await this._signinStart({\n      request_type: \"si:s\",\n      // this acts like a signin silent\n      redirect_uri: url,\n      prompt: \"none\",\n      id_token_hint: this.settings.includeIdTokenInSilentRenew ? user == null ? void 0 : user.id_token : void 0,\n      response_type: this.settings.query_status_response_type,\n      scope: \"openid\",\n      skipUserInfo: true,\n      ...requestArgs\n    }, handle);\n    try {\n      const extraHeaders = {};\n      const signinResponse = await this._client.processSigninResponse(navResponse.url, extraHeaders);\n      logger2.debug(\"got signin response\");\n      if (signinResponse.session_state && signinResponse.profile.sub) {\n        logger2.info(\"success for subject\", signinResponse.profile.sub);\n        return {\n          session_state: signinResponse.session_state,\n          sub: signinResponse.profile.sub\n        };\n      }\n      logger2.info(\"success, user not authenticated\");\n      return null;\n    } catch (err) {\n      if (this.settings.monitorAnonymousSession && err instanceof ErrorResponse) {\n        switch (err.error) {\n          case \"login_required\":\n          case \"consent_required\":\n          case \"interaction_required\":\n          case \"account_selection_required\":\n            logger2.info(\"success for anonymous user\");\n            return {\n              session_state: err.session_state\n            };\n        }\n      }\n      throw err;\n    }\n  }\n  async _signin(args, handle, verifySub) {\n    const navResponse = await this._signinStart(args, handle);\n    return await this._signinEnd(navResponse.url, verifySub);\n  }\n  async _signinStart(args, handle) {\n    const logger2 = this._logger.create(\"_signinStart\");\n    try {\n      const signinRequest = await this._client.createSigninRequest(args);\n      logger2.debug(\"got signin request\");\n      return await handle.navigate({\n        url: signinRequest.url,\n        state: signinRequest.state.id,\n        response_mode: signinRequest.state.response_mode,\n        scriptOrigin: this.settings.iframeScriptOrigin\n      });\n    } catch (err) {\n      logger2.debug(\"error after preparing navigator, closing navigator window\");\n      handle.close();\n      throw err;\n    }\n  }\n  async _signinEnd(url, verifySub) {\n    const logger2 = this._logger.create(\"_signinEnd\");\n    const extraHeaders = {};\n    const signinResponse = await this._client.processSigninResponse(url, extraHeaders);\n    logger2.debug(\"got signin response\");\n    const user = await this._buildUser(signinResponse, verifySub);\n    return user;\n  }\n  async _buildUser(signinResponse, verifySub) {\n    const logger2 = this._logger.create(\"_buildUser\");\n    const user = new User(signinResponse);\n    if (verifySub) {\n      if (verifySub !== user.profile.sub) {\n        logger2.debug(\"current user does not match user returned from signin. sub from signin:\", user.profile.sub);\n        throw new ErrorResponse({\n          ...signinResponse,\n          error: \"login_required\"\n        });\n      }\n      logger2.debug(\"current user matches user returned from signin\");\n    }\n    await this.storeUser(user);\n    logger2.debug(\"user stored\");\n    await this._events.load(user);\n    return user;\n  }\n  /**\n   * Trigger a redirect of the current window to the end session endpoint.\n   *\n   * @returns A promise\n   */\n  async signoutRedirect(args = {}) {\n    const logger2 = this._logger.create(\"signoutRedirect\");\n    const {\n      redirectMethod,\n      ...requestArgs\n    } = args;\n    const handle = await this._redirectNavigator.prepare({\n      redirectMethod\n    });\n    await this._signoutStart({\n      request_type: \"so:r\",\n      post_logout_redirect_uri: this.settings.post_logout_redirect_uri,\n      ...requestArgs\n    }, handle);\n    logger2.info(\"success\");\n  }\n  /**\n   * Process response (callback) from the end session endpoint.\n   * It is recommended to use {@link UserManager.signoutCallback} instead.\n   *\n   * @returns A promise containing signout response\n   *\n   * @see {@link UserManager.signoutCallback}\n   */\n  async signoutRedirectCallback(url = window.location.href) {\n    const logger2 = this._logger.create(\"signoutRedirectCallback\");\n    const response = await this._signoutEnd(url);\n    logger2.info(\"success\");\n    return response;\n  }\n  /**\n   * Trigger a redirect of a popup window to the end session endpoint.\n   *\n   * @returns A promise\n   */\n  async signoutPopup(args = {}) {\n    const logger2 = this._logger.create(\"signoutPopup\");\n    const {\n      popupWindowFeatures,\n      popupWindowTarget,\n      popupSignal,\n      ...requestArgs\n    } = args;\n    const url = this.settings.popup_post_logout_redirect_uri;\n    const handle = await this._popupNavigator.prepare({\n      popupWindowFeatures,\n      popupWindowTarget,\n      popupSignal\n    });\n    await this._signout({\n      request_type: \"so:p\",\n      post_logout_redirect_uri: url,\n      // we're putting a dummy entry in here because we\n      // need a unique id from the state for notification\n      // to the parent window, which is necessary if we\n      // plan to return back to the client after signout\n      // and so we can close the popup after signout\n      state: url == null ? void 0 : {},\n      ...requestArgs\n    }, handle);\n    logger2.info(\"success\");\n  }\n  /**\n   * Process response (callback) from the end session endpoint from a popup window.\n   * It is recommended to use {@link UserManager.signoutCallback} instead.\n   *\n   * @returns A promise\n   *\n   * @see {@link UserManager.signoutCallback}\n   */\n  async signoutPopupCallback(url = window.location.href, keepOpen = false) {\n    const logger2 = this._logger.create(\"signoutPopupCallback\");\n    await this._popupNavigator.callback(url, {\n      keepOpen\n    });\n    logger2.info(\"success\");\n  }\n  async _signout(args, handle) {\n    const navResponse = await this._signoutStart(args, handle);\n    return await this._signoutEnd(navResponse.url);\n  }\n  async _signoutStart(args = {}, handle) {\n    var _a;\n    const logger2 = this._logger.create(\"_signoutStart\");\n    try {\n      const user = await this._loadUser();\n      logger2.debug(\"loaded current user from storage\");\n      if (this.settings.revokeTokensOnSignout) {\n        await this._revokeInternal(user);\n      }\n      const id_token = args.id_token_hint || user && user.id_token;\n      if (id_token) {\n        logger2.debug(\"setting id_token_hint in signout request\");\n        args.id_token_hint = id_token;\n      }\n      await this.removeUser();\n      logger2.debug(\"user removed, creating signout request\");\n      const signoutRequest = await this._client.createSignoutRequest(args);\n      logger2.debug(\"got signout request\");\n      return await handle.navigate({\n        url: signoutRequest.url,\n        state: (_a = signoutRequest.state) == null ? void 0 : _a.id,\n        scriptOrigin: this.settings.iframeScriptOrigin\n      });\n    } catch (err) {\n      logger2.debug(\"error after preparing navigator, closing navigator window\");\n      handle.close();\n      throw err;\n    }\n  }\n  async _signoutEnd(url) {\n    const logger2 = this._logger.create(\"_signoutEnd\");\n    const signoutResponse = await this._client.processSignoutResponse(url);\n    logger2.debug(\"got signout response\");\n    return signoutResponse;\n  }\n  /**\n   * Trigger a silent request (via an iframe) to the end session endpoint.\n   *\n   * @returns A promise\n   */\n  async signoutSilent(args = {}) {\n    var _a;\n    const logger2 = this._logger.create(\"signoutSilent\");\n    const {\n      silentRequestTimeoutInSeconds,\n      ...requestArgs\n    } = args;\n    const id_token_hint = this.settings.includeIdTokenInSilentSignout ? (_a = await this._loadUser()) == null ? void 0 : _a.id_token : void 0;\n    const url = this.settings.popup_post_logout_redirect_uri;\n    const handle = await this._iframeNavigator.prepare({\n      silentRequestTimeoutInSeconds\n    });\n    await this._signout({\n      request_type: \"so:s\",\n      post_logout_redirect_uri: url,\n      id_token_hint,\n      ...requestArgs\n    }, handle);\n    logger2.info(\"success\");\n  }\n  /**\n   * Notify the parent window of response (callback) from the end session endpoint.\n   * It is recommended to use {@link UserManager.signoutCallback} instead.\n   *\n   * @returns A promise\n   *\n   * @see {@link UserManager.signoutCallback}\n   */\n  async signoutSilentCallback(url = window.location.href) {\n    const logger2 = this._logger.create(\"signoutSilentCallback\");\n    await this._iframeNavigator.callback(url);\n    logger2.info(\"success\");\n  }\n  async revokeTokens(types) {\n    const user = await this._loadUser();\n    await this._revokeInternal(user, types);\n  }\n  async _revokeInternal(user, types = this.settings.revokeTokenTypes) {\n    const logger2 = this._logger.create(\"_revokeInternal\");\n    if (!user) return;\n    const typesPresent = types.filter(type => typeof user[type] === \"string\");\n    if (!typesPresent.length) {\n      logger2.debug(\"no need to revoke due to no token(s)\");\n      return;\n    }\n    for (const type of typesPresent) {\n      await this._client.revokeToken(user[type], type);\n      logger2.info(`${type} revoked successfully`);\n      if (type !== \"access_token\") {\n        user[type] = null;\n      }\n    }\n    await this.storeUser(user);\n    logger2.debug(\"user stored\");\n    await this._events.load(user);\n  }\n  /**\n   * Enables silent renew for the `UserManager`.\n   */\n  startSilentRenew() {\n    this._logger.create(\"startSilentRenew\");\n    void this._silentRenewService.start();\n  }\n  /**\n   * Disables silent renew for the `UserManager`.\n   */\n  stopSilentRenew() {\n    this._silentRenewService.stop();\n  }\n  get _userStoreKey() {\n    return `user:${this.settings.authority}:${this.settings.client_id}`;\n  }\n  async _loadUser() {\n    const logger2 = this._logger.create(\"_loadUser\");\n    const storageString = await this.settings.userStore.get(this._userStoreKey);\n    if (storageString) {\n      logger2.debug(\"user storageString loaded\");\n      return User.fromStorageString(storageString);\n    }\n    logger2.debug(\"no user storageString\");\n    return null;\n  }\n  async storeUser(user) {\n    const logger2 = this._logger.create(\"storeUser\");\n    if (user) {\n      logger2.debug(\"storing user\");\n      const storageString = user.toStorageString();\n      await this.settings.userStore.set(this._userStoreKey, storageString);\n    } else {\n      this._logger.debug(\"removing user\");\n      await this.settings.userStore.remove(this._userStoreKey);\n      if (this.settings.dpop) {\n        await this.settings.dpop.store.remove(this.settings.client_id);\n      }\n    }\n  }\n  /**\n   * Removes stale state entries in storage for incomplete authorize requests.\n   */\n  async clearStaleState() {\n    await this._client.clearStaleState();\n  }\n  /**\n   * Dynamically generates a DPoP proof for a given user, URL and optional Http method.\n   * This method is useful when you need to make a request to a resource server\n   * with fetch or similar, and you need to include a DPoP proof in a DPoP header.\n   * @param url - The URL to generate the DPoP proof for\n   * @param user - The user to generate the DPoP proof for\n   * @param httpMethod - Optional, defaults to \"GET\"\n   * @param nonce - Optional nonce provided by the resource server\n   *\n   * @returns A promise containing the DPoP proof or undefined if DPoP is not enabled/no user is found.\n   */\n  async dpopProof(url, user, httpMethod, nonce) {\n    var _a, _b;\n    const dpopState = await ((_b = (_a = this.settings.dpop) == null ? void 0 : _a.store) == null ? void 0 : _b.get(this.settings.client_id));\n    if (dpopState) {\n      return await CryptoUtils.generateDPoPProof({\n        url,\n        accessToken: user == null ? void 0 : user.access_token,\n        httpMethod,\n        keyPair: dpopState.keys,\n        nonce\n      });\n    }\n    return void 0;\n  }\n  async generateDPoPJkt(dpopSettings) {\n    let dpopState = await dpopSettings.store.get(this.settings.client_id);\n    if (!dpopState) {\n      const dpopKeys = await CryptoUtils.generateDPoPKeys();\n      dpopState = new DPoPState(dpopKeys);\n      await dpopSettings.store.set(this.settings.client_id, dpopState);\n    }\n    return await CryptoUtils.generateDPoPJkt(dpopState.keys);\n  }\n};\n\n// package.json\nvar version = \"3.2.0\";\n\n// src/Version.ts\nvar Version = version;\n\n// src/IndexedDbDPoPStore.ts\nvar IndexedDbDPoPStore = class {\n  constructor() {\n    this._dbName = \"oidc\";\n    this._storeName = \"dpop\";\n  }\n  async set(key, value) {\n    const store = await this.createStore(this._dbName, this._storeName);\n    await store(\"readwrite\", str => {\n      str.put(value, key);\n      return this.promisifyRequest(str.transaction);\n    });\n  }\n  async get(key) {\n    const store = await this.createStore(this._dbName, this._storeName);\n    return await store(\"readonly\", str => {\n      return this.promisifyRequest(str.get(key));\n    });\n  }\n  async remove(key) {\n    const item = await this.get(key);\n    const store = await this.createStore(this._dbName, this._storeName);\n    await store(\"readwrite\", str => {\n      return this.promisifyRequest(str.delete(key));\n    });\n    return item;\n  }\n  async getAllKeys() {\n    const store = await this.createStore(this._dbName, this._storeName);\n    return await store(\"readonly\", str => {\n      return this.promisifyRequest(str.getAllKeys());\n    });\n  }\n  promisifyRequest(request) {\n    return new Promise((resolve, reject) => {\n      request.oncomplete = request.onsuccess = () => resolve(request.result);\n      request.onabort = request.onerror = () => reject(request.error);\n    });\n  }\n  async createStore(dbName, storeName) {\n    const request = indexedDB.open(dbName);\n    request.onupgradeneeded = () => request.result.createObjectStore(storeName);\n    const db = await this.promisifyRequest(request);\n    return async (txMode, callback) => {\n      const tx = db.transaction(storeName, txMode);\n      const store = tx.objectStore(storeName);\n      return await callback(store);\n    };\n  }\n};\nexport { AccessTokenEvents, CheckSessionIFrame, DPoPState, ErrorResponse, ErrorTimeout, InMemoryWebStorage, IndexedDbDPoPStore, Log, Logger, MetadataService, OidcClient, OidcClientSettingsStore, SessionMonitor, SigninResponse, SigninState, SignoutResponse, State, User, UserManager, UserManagerSettingsStore, Version, WebStorageStateStore };", "map": {"version": 3, "names": ["nop<PERSON>ogger", "debug", "info", "warn", "error", "level", "logger", "Log", "Log2", "reset", "setLevel", "value", "Error", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "_<PERSON>gger", "constructor", "_name", "args", "_format", "_method", "throw", "err", "create", "method", "methodLogger", "Object", "createStatic", "name", "staticMethod", "staticLogger", "prefix", "jwtDecode", "JwtUtils", "decode", "token", "generateSignedJwt", "header", "payload", "privateKey", "encodedHeader", "CryptoUtils", "encodeBase64Url", "TextEncoder", "encode", "JSON", "stringify", "encodedPayload", "encodedToken", "signature", "window", "crypto", "subtle", "sign", "hash", "encodedSignature", "Uint8Array", "UUID_V4_TEMPLATE", "toBase64", "val", "btoa", "map", "chr", "String", "fromCharCode", "join", "_CryptoUtils", "_randomWord", "arr", "Uint32Array", "getRandomValues", "generateUUIDv4", "uuid", "replace", "c", "toString", "generateCodeVerifier", "generateCodeChallenge", "code_verifier", "encoder", "data", "hashed", "digest", "generateBasicAuth", "client_id", "client_secret", "alg", "message", "msgUint8", "hash<PERSON><PERSON><PERSON>", "customCalculateJwkThumbprint", "jwk", "jsonObject", "kty", "e", "n", "crv", "x", "y", "k", "utf8encodedAndHashed", "generateDPoPProof", "url", "accessToken", "httpMethod", "keyPair", "nonce", "hashedToken", "encodedHash", "randomUUID", "Math", "floor", "Date", "now", "ath", "publicJwk", "exportKey", "public<PERSON>ey", "TypeError", "generateDPoPJkt", "generateDPoPKeys", "<PERSON><PERSON>ey", "namedCurve", "input", "Event", "_callbacks", "_logger", "add<PERSON><PERSON><PERSON>", "cb", "push", "<PERSON><PERSON><PERSON><PERSON>", "idx", "lastIndexOf", "splice", "raise", "ev", "<PERSON>up<PERSON><PERSON><PERSON>", "center", "features", "_a", "_b", "_c", "width", "find", "outerWidth", "left", "max", "round", "screenX", "height", "top", "screenY", "outerHeight", "serialize", "entries", "filter", "key", "Timer", "_Timer", "arguments", "_timer<PERSON><PERSON><PERSON>", "_expiration", "_callback", "diff", "getEpochTime", "cancel", "init", "durationInSeconds", "logger2", "expiration", "timerDurationInSeconds", "min", "setInterval", "clearInterval", "<PERSON>rl<PERSON><PERSON><PERSON>", "readParams", "responseMode", "parsedUrl", "URL", "params", "URLSearchParams", "slice", "URL_STATE_DELIMITER", "ErrorResponse", "form", "error_description", "error_uri", "state", "userState", "session_state", "url_state", "ErrorTimeout", "AccessTokenEvents", "_expiringTimer", "_expiredTimer", "_expiringNotificationTimeInSeconds", "expiringNotificationTimeInSeconds", "load", "container", "access_token", "expires_in", "duration", "expiring", "expired", "unload", "addAccessTokenExpiring", "removeAccessTokenExpiring", "addAccessTokenExpired", "removeAccessTokenExpired", "CheckSessionIFrame", "_client_id", "_intervalInSeconds", "_stopOnError", "_timer", "_session_state", "_message", "origin", "_frame_origin", "source", "_frame", "contentWindow", "stop", "document", "createElement", "style", "visibility", "position", "src", "href", "Promise", "resolve", "onload", "body", "append<PERSON><PERSON><PERSON>", "addEventListener", "start", "send", "postMessage", "InMemoryWebStorage", "_data", "clear", "getItem", "setItem", "removeItem", "length", "getOwnPropertyNames", "index", "ErrorDPoPNonce", "JsonService", "additionalContentTypes", "_jwt<PERSON><PERSON><PERSON>", "_extraHeaders", "_contentTypes", "fetchWithTimeout", "timeoutInSeconds", "initFetch", "fetch", "controller", "AbortController", "timeoutId", "setTimeout", "abort", "response", "signal", "DOMException", "clearTimeout", "get<PERSON>son", "credentials", "headers", "_appendExtraHeaders", "status", "contentType", "get", "item", "startsWith", "ok", "text", "json", "statusText", "postForm", "basicAuth", "initCredentials", "extraHeaders", "responseText", "parse", "has", "customKeys", "keys", "protectedHeaders", "preventOverride", "for<PERSON>ach", "headerName", "includes", "toLocaleLowerCase", "content", "MetadataService", "_settings", "_signing<PERSON><PERSON><PERSON>", "_metadata", "_metadataUrl", "metadataUrl", "_jsonService", "<PERSON><PERSON><PERSON><PERSON>", "metadata", "fetchRequestCredentials", "_fetchRequestCredentials", "resetSigningKeys", "getMetadata", "requestTimeoutInSeconds", "assign", "metadataSeed", "get<PERSON><PERSON><PERSON>", "_getMetadataProperty", "getAuthorizationEndpoint", "getUserInfoEndpoint", "getTokenEndpoint", "optional", "getCheckSessionIframe", "getEndSessionEndpoint", "getRevocationEndpoint", "getKeysEndpoint", "getSigningKeys", "jwks_uri", "keySet", "Array", "isArray", "WebStorageStateStore", "store", "localStorage", "_store", "_prefix", "set", "remove", "getAllKeys", "len", "indexOf", "substr", "DefaultResponseType", "DefaultScope", "DefaultClientAuthentication", "DefaultStaleStateAgeInSeconds", "OidcClientSettingsStore", "authority", "response_type", "scope", "redirect_uri", "post_logout_redirect_uri", "client_authentication", "prompt", "display", "max_age", "ui_locales", "acr_values", "resource", "response_mode", "filterProtocolClaims", "loadUserInfo", "staleStateAgeInSeconds", "mergeClaimsStrategy", "array", "disablePKCE", "stateStore", "revokeTokenAdditionalContentTypes", "refreshTokenAllowedScope", "extraQueryParams", "extraTokenParams", "dpop", "omitScopeWhenRequesting", "endsWith", "UserInfoService", "_metadataService", "_getClaimsFromJwt", "getClaims", "claims", "TokenClient", "exchangeCode", "grant_type", "code", "append", "exchangeCredentials", "exchangeRefreshToken", "refresh_token", "param", "revoke", "token_type_hint", "ResponseValidator", "_claimsService", "_userInfoService", "_tokenClient", "validateSigninResponse", "_processSigninState", "_processCode", "isOpenId", "_validateIdTokenAttributes", "_processClaims", "skipUserInfo", "validateCredentialsResponse", "id_token", "validateRefreshResponse", "profile", "hasIdToken", "validateSignoutResponse", "id", "validateSub", "sub", "mergeClaims", "tokenResponse", "existingToken", "incoming", "existing", "auth_time", "azp", "State", "_State", "created", "request_type", "toStorageString", "fromStorageString", "storageString", "clearStaleState", "storage", "age", "cutoff", "i", "SigninState", "_SigninState", "code_challenge", "_SigninRequest", "state_data", "dpopJkt", "optionalParams", "searchParams", "stateParam", "resources", "r", "SigninRequest", "OidcScope", "SigninResponse", "token_type", "splitState", "decodeURIComponent", "split", "expires_at", "Number", "SignoutRequest", "id_token_hint", "SignoutResponse", "DefaultProtocolClaims", "InternalRequiredProtocolClaims", "ClaimsService", "result", "protocolClaims", "claim", "claims1", "claims2", "values", "mergedValues", "DPoPState", "OidcClient", "settings", "metadataService", "_validator", "createSigninRequest", "request", "request_uri", "login_hint", "signinRequest", "signinState", "readSigninResponseState", "removeState", "storedStateString", "processSigninResponse", "dpopProof", "getDpopProof", "dpopStore", "dpopState", "processResourceOwnerPasswordCredentials", "username", "password", "signinResponse", "useRefreshToken", "allowableScopes", "providedScopes", "s", "createSignoutRequest", "signoutState", "readSignoutResponseState", "processSignoutResponse", "revokeToken", "type", "SessionMonitor", "_userManager", "_start", "user", "_sub", "_checkSessionIFrame", "intervalInSeconds", "checkSessionIntervalInSeconds", "stopOnError", "stopCheckSessionOnError", "checkSessionIFrame", "_stop", "monitorAnonymousSession", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "session", "querySessionStatus", "tmpUser", "raiseEvent", "events", "_raiseUserSessionChanged", "_raiseUserSignedOut", "_raiseUserSignedIn", "addUserLoaded", "addUserUnloaded", "_init", "catch", "getUser", "User", "_User", "scopes", "messageSource", "AbstractChildWindow", "_abort", "_disposeHandlers", "Set", "_window", "navigate", "location", "keep<PERSON>pen", "reject", "listener", "<PERSON><PERSON><PERSON><PERSON>", "_dispose", "add", "removeEventListener", "reason", "close", "dispose", "_notifyParent", "parent", "target<PERSON>rigin", "DefaultPopupWindowFeatures", "toolbar", "closePopupWindowAfterInSeconds", "DefaultPopupTarget", "DefaultAccessTokenExpiringNotificationTimeInSeconds", "DefaultCheckSessionIntervalInSeconds", "DefaultSilentRequestTimeoutInSeconds", "UserManagerSettingsStore", "popup_redirect_uri", "popup_post_logout_redirect_uri", "popupWindowFeatures", "popupWindowTarget", "redirectMethod", "redirectTarget", "iframeNotifyParentOrigin", "iframeScriptOrigin", "silent_redirect_uri", "silentRequestTimeoutInSeconds", "automaticSilentRenew", "validateSubOnSilentRenew", "includeIdTokenInSilentRenew", "monitorSession", "query_status_response_type", "revokeTokenTypes", "revokeTokensOnSignout", "includeIdTokenInSilentSignout", "accessTokenExpiringNotificationTimeInSeconds", "userStore", "sessionStorage", "IFrameWindow", "_IFrameWindow", "_timeoutInSeconds", "createHiddenIframe", "iframe", "timer", "parentNode", "_a2", "frame", "target", "<PERSON><PERSON><PERSON><PERSON>", "notify<PERSON>arent", "IFrameNavigator", "prepare", "callback", "checkForPopupClosedInterval", "second", "PopupWindow", "popupSignal", "centeredPopup", "open", "closed", "focus", "popupClosedInterval", "notify<PERSON><PERSON><PERSON>", "opener", "PopupNavigator", "RedirectNavigator", "targetWindow", "self", "redirect", "bind", "promise", "UserManagerEvents", "_userLoaded", "_userUnloaded", "_silent<PERSON><PERSON><PERSON><PERSON><PERSON>r", "_userSignedIn", "_userSignedOut", "_userSessionChanged", "removeUserLoaded", "removeUserUnloaded", "addSilentRenewError", "removeSilentRenewError", "_raiseSilentRenewError", "addUserSignedIn", "removeUserSignedIn", "addUserSignedOut", "removeUserSignedOut", "addUserSessionChanged", "removeUserSessionChanged", "SilentRenewService", "_isStarted", "_retryTimer", "_tokenExpiring", "signinSilent", "RefreshState", "UserManager", "redirectNavigator", "popupNavigator", "iframeNavigator", "_client", "_redirectNavigator", "_popupNavigator", "_iframeNavigator", "_events", "_silentRenewService", "startSilentRenew", "_sessionMonitor", "_loadUser", "removeUser", "storeUser", "signinRedirect", "requestArgs", "bind_authorization_code", "handle", "_signinStart", "signinRedirectCallback", "_signinEnd", "signinResourceOwnerCredentials", "_buildUser", "signinPopup", "_signin", "signin<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_useRefreshToken", "verifySub", "signinSilentCallback", "signin<PERSON><PERSON><PERSON>", "signout<PERSON><PERSON><PERSON>", "signoutRedirectCallback", "signoutPopupCallback", "signoutSilentCallback", "navResponse", "signoutRedirect", "_signoutStart", "_signoutEnd", "signoutPopup", "_signout", "_revokeInternal", "signoutRequest", "signoutResponse", "signoutSilent", "revokeTokens", "types", "typesPresent", "stopSilentRenew", "_userStoreKey", "dpopSettings", "dpopKeys", "version", "Version", "IndexedDbDPoPStore", "_dbName", "_storeName", "createStore", "str", "put", "promisifyRequest", "transaction", "delete", "oncomplete", "onsuccess", "<PERSON>ab<PERSON>", "onerror", "dbN<PERSON>", "storeName", "indexedDB", "onupgradeneeded", "createObjectStore", "db", "txMode", "tx", "objectStore"], "sources": ["D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\utils\\Logger.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\utils\\JwtUtils.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\utils\\CryptoUtils.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\utils\\Event.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\utils\\PopupUtils.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\utils\\Timer.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\utils\\UrlUtils.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\errors\\ErrorResponse.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\errors\\ErrorTimeout.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\AccessTokenEvents.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\CheckSessionIFrame.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\InMemoryWebStorage.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\errors\\ErrorDPoPNonce.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\JsonService.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\MetadataService.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\WebStorageStateStore.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\OidcClientSettings.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\UserInfoService.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\TokenClient.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\ResponseValidator.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\State.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\SigninState.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\SigninRequest.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\SigninResponse.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\SignoutRequest.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\SignoutResponse.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\ClaimsService.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\DPoPStore.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\OidcClient.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\SessionMonitor.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\User.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\navigators\\AbstractChildWindow.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\UserManagerSettings.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\navigators\\IFrameWindow.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\navigators\\IFrameNavigator.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\navigators\\PopupWindow.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\navigators\\PopupNavigator.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\navigators\\RedirectNavigator.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\UserManagerEvents.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\SilentRenewService.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\RefreshState.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\UserManager.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\package.json", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\Version.ts", "D:\\Technology\\00_SpringSecurity_SpringBoot_2025\\react-sso-project\\react-spa\\node_modules\\oidc-client-ts\\src\\IndexedDbDPoPStore.ts"], "sourcesContent": ["// Copyright (c) Brock <PERSON> & <PERSON>. All rights reserved.\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\n/**\n * Native interface\n *\n * @public\n */\nexport interface ILogger {\n    debug(...args: unknown[]): void;\n    info(...args: unknown[]): void;\n    warn(...args: unknown[]): void;\n    error(...args: unknown[]): void;\n}\n\nconst nopLogger: ILogger = {\n    debug: () => undefined,\n    info: () => undefined,\n    warn: () => undefined,\n    error: () => undefined,\n};\n\nlet level: number;\nlet logger: ILogger;\n\n/**\n * Log levels\n *\n * @public\n */\nexport enum Log {\n    NONE,\n    ERROR,\n    WARN,\n    INFO,\n    DEBUG\n}\n\n/**\n * Log manager\n *\n * @public\n */\nexport namespace Log { // eslint-disable-line @typescript-eslint/no-namespace\n    export function reset(): void {\n        level = Log.INFO;\n        logger = nopLogger;\n    }\n\n    export function setLevel(value: Log): void {\n        if (!(Log.NONE <= value && value <= Log.DEBUG)) {\n            throw new Error(\"Invalid log level\");\n        }\n        level = value;\n    }\n\n    export function setLogger(value: ILogger): void {\n        logger = value;\n    }\n}\n\n/**\n * Internal logger instance\n *\n * @public\n */\nexport class Logger {\n    private _method?: string;\n    public constructor(private _name: string) {}\n\n    /* eslint-disable @typescript-eslint/no-unsafe-enum-comparison */\n    public debug(...args: unknown[]): void {\n        if (level >= Log.DEBUG) {\n            logger.debug(Logger._format(this._name, this._method), ...args);\n        }\n    }\n    public info(...args: unknown[]): void {\n        if (level >= Log.INFO) {\n            logger.info(Logger._format(this._name, this._method), ...args);\n        }\n    }\n    public warn(...args: unknown[]): void {\n        if (level >= Log.WARN) {\n            logger.warn(Logger._format(this._name, this._method), ...args);\n        }\n    }\n    public error(...args: unknown[]): void {\n        if (level >= Log.ERROR) {\n            logger.error(Logger._format(this._name, this._method), ...args);\n        }\n    }\n    /* eslint-enable @typescript-eslint/no-unsafe-enum-comparison */\n\n    public throw(err: Error): never {\n        this.error(err);\n        throw err;\n    }\n\n    public create(method: string): Logger {\n        const methodLogger: Logger = Object.create(this);\n        methodLogger._method = method;\n        methodLogger.debug(\"begin\");\n        return methodLogger;\n    }\n\n    public static createStatic(name: string, staticMethod: string): Logger {\n        const staticLogger = new Logger(`${name}.${staticMethod}`);\n        staticLogger.debug(\"begin\");\n        return staticLogger;\n    }\n\n    private static _format(name: string, method?: string) {\n        const prefix = `[${name}]`;\n        return method ? `${prefix} ${method}:` : prefix;\n    }\n\n    /* eslint-disable @typescript-eslint/no-unsafe-enum-comparison */\n    // helpers for static class methods\n    public static debug(name: string, ...args: unknown[]): void {\n        if (level >= Log.DEBUG) {\n            logger.debug(Logger._format(name), ...args);\n        }\n    }\n    public static info(name: string, ...args: unknown[]): void {\n        if (level >= Log.INFO) {\n            logger.info(Logger._format(name), ...args);\n        }\n    }\n    public static warn(name: string, ...args: unknown[]): void {\n        if (level >= Log.WARN) {\n            logger.warn(Logger._format(name), ...args);\n        }\n    }\n    public static error(name: string, ...args: unknown[]): void {\n        if (level >= Log.ERROR) {\n            logger.error(Logger._format(name), ...args);\n        }\n    }\n    /* eslint-enable @typescript-eslint/no-unsafe-enum-comparison */\n}\n\nLog.reset();\n", "import { jwtDecode } from \"jwt-decode\";\n\nimport { <PERSON><PERSON> } from \"./Logger\";\nimport type { JwtClaims } from \"../Claims\";\nimport { CryptoUtils } from \"./CryptoUtils\";\n\n/**\n * @internal\n */\nexport class JwtUtils {\n    // IMPORTANT: doesn't validate the token\n    public static decode(token: string): JwtClaims {\n        try {\n            return jwtDecode<JwtClaims>(token);\n        }\n        catch (err) {\n            Logger.error(\"JwtUtils.decode\", err);\n            throw err;\n        }\n    }\n\n    public static async generateSignedJwt(header: object, payload: object, privateKey: CryptoKey) : Promise<string> {\n        const encodedHeader = CryptoUtils.encodeBase64Url(new TextEncoder().encode(JSON.stringify(header)));\n        const encodedPayload = CryptoUtils.encodeBase64Url(new TextEncoder().encode(JSON.stringify(payload)));\n        const encodedToken = `${encodedHeader}.${encodedPayload}`;\n\n        const signature = await window.crypto.subtle.sign(\n            {\n                name: \"ECDS<PERSON>\",\n                hash: { name: \"SHA-256\" },\n            },\n            privateKey,\n            new TextEncoder().encode(encodedToken),\n        );\n\n        const encodedSignature = CryptoUtils.encodeBase64Url(new Uint8Array(signature));\n        return `${encodedToken}.${encodedSignature}`;\n    }\n}\n", "import { Logger } from \"./Logger\";\nimport { JwtUtils } from \"./JwtUtils\";\n\nexport interface GenerateDPoPProofOpts {\n    url: string;\n    accessToken?: string;\n    httpMethod?: string;\n    keyPair: CryptoKeyPair;\n    nonce?: string;\n}\n\nconst UUID_V4_TEMPLATE = \"10000000-1000-4000-8000-100000000000\";\n\nconst toBase64 = (val: ArrayBuffer): string =>\n    btoa([...new Uint8Array(val)]\n        .map((chr) => String.fromCharCode(chr))\n        .join(\"\"));\n\n/**\n * @internal\n */\nexport class CryptoUtils {\n    private static _randomWord(): number {\n        const arr = new Uint32Array(1);\n        crypto.getRandomValues(arr);\n        return arr[0];\n    }\n\n    /**\n     * Generates RFC4122 version 4 guid\n     */\n    public static generateUUIDv4(): string {\n        const uuid = UUID_V4_TEMPLATE.replace(/[018]/g, c =>\n            (+c ^ CryptoUtils._randomWord() & 15 >> +c / 4).toString(16),\n        );\n        return uuid.replace(/-/g, \"\");\n    }\n\n    /**\n     * PKCE: Generate a code verifier\n     */\n    public static generateCodeVerifier(): string {\n        return CryptoUtils.generateUUIDv4() + CryptoUtils.generateUUIDv4() + CryptoUtils.generateUUIDv4();\n    }\n\n    /**\n     * PKCE: Generate a code challenge\n     */\n    public static async generateCodeChallenge(code_verifier: string): Promise<string> {\n        if (!crypto.subtle) {\n            throw new Error(\"Crypto.subtle is available only in secure contexts (HTTPS).\");\n        }\n\n        try {\n            const encoder = new TextEncoder();\n            const data = encoder.encode(code_verifier);\n            const hashed = await crypto.subtle.digest(\"SHA-256\", data);\n            return toBase64(hashed).replace(/\\+/g, \"-\").replace(/\\//g, \"_\").replace(/=+$/, \"\");\n        }\n        catch (err) {\n            Logger.error(\"CryptoUtils.generateCodeChallenge\", err);\n            throw err;\n        }\n    }\n\n    /**\n     * Generates a base64-encoded string for a basic auth header\n     */\n    public static generateBasicAuth(client_id: string, client_secret: string): string {\n        const encoder = new TextEncoder();\n        const data = encoder.encode([client_id, client_secret].join(\":\"));\n        return toBase64(data);\n    }\n\n    /**\n     * Generates a hash of a string using a given algorithm\n     * @param alg\n     * @param message\n     */\n    public static async hash(alg: string, message: string) : Promise<Uint8Array> {\n        const msgUint8 = new TextEncoder().encode(message);\n        const hashBuffer = await crypto.subtle.digest(alg, msgUint8);\n        return new Uint8Array(hashBuffer);\n    }\n\n    /**\n     * Generates a base64url encoded string\n     */\n    public static encodeBase64Url = (input: Uint8Array) => {\n        return toBase64(input).replace(/=/g, \"\").replace(/\\+/g, \"-\").replace(/\\//g, \"_\");\n    };\n\n    /**\n     * Generates a rfc7638 compliant jwk thumbprint\n     * @param jwk\n     */\n    public static async customCalculateJwkThumbprint(jwk: JsonWebKey): Promise<string> {\n        let jsonObject: object;\n        switch (jwk.kty) {\n            case \"RSA\":\n                jsonObject = {\n                    \"e\": jwk.e,\n                    \"kty\": jwk.kty,\n                    \"n\": jwk.n,\n                };\n                break;\n            case \"EC\":\n                jsonObject = {\n                    \"crv\": jwk.crv,\n                    \"kty\": jwk.kty,\n                    \"x\": jwk.x,\n                    \"y\": jwk.y,\n                };\n                break;\n            case \"OKP\":\n                jsonObject = {\n                    \"crv\": jwk.crv,\n                    \"kty\": jwk.kty,\n                    \"x\": jwk.x,\n                };\n                break;\n            case \"oct\":\n                jsonObject = {\n                    \"crv\": jwk.k,\n                    \"kty\": jwk.kty,\n                };\n                break;\n            default:\n                throw new Error(\"Unknown jwk type\");\n        }\n        const utf8encodedAndHashed = await CryptoUtils.hash(\"SHA-256\", JSON.stringify(jsonObject));\n        return CryptoUtils.encodeBase64Url(utf8encodedAndHashed);\n    }\n\n    public static async generateDPoPProof({\n        url,\n        accessToken,\n        httpMethod,\n        keyPair,\n        nonce,\n    }: GenerateDPoPProofOpts): Promise<string> {\n        let hashedToken: Uint8Array;\n        let encodedHash: string;\n\n        const payload: Record<string, string | number> = {\n            \"jti\": window.crypto.randomUUID(),\n            \"htm\": httpMethod ?? \"GET\",\n            \"htu\": url,\n            \"iat\": Math.floor(Date.now() / 1000),\n        };\n\n        if (accessToken) {\n            hashedToken = await CryptoUtils.hash(\"SHA-256\", accessToken);\n            encodedHash = CryptoUtils.encodeBase64Url(hashedToken);\n            payload.ath = encodedHash;\n        }\n\n        if (nonce) {\n            payload.nonce = nonce;\n        }\n\n        try {\n            const publicJwk = await crypto.subtle.exportKey(\"jwk\", keyPair.publicKey);\n            const header = {\n                \"alg\": \"ES256\",\n                \"typ\": \"dpop+jwt\",\n                \"jwk\": {\n                    \"crv\": publicJwk.crv,\n                    \"kty\": publicJwk.kty,\n                    \"x\": publicJwk.x,\n                    \"y\": publicJwk.y,\n                },\n            };\n            return await JwtUtils.generateSignedJwt(header, payload, keyPair.privateKey);\n        } catch (err) {\n            if (err instanceof TypeError) {\n                throw new Error(`Error exporting dpop public key: ${err.message}`);\n            } else {\n                throw err;\n            }\n        }\n    }\n\n    public static async generateDPoPJkt(keyPair: CryptoKeyPair) : Promise<string> {\n        try {\n            const publicJwk = await crypto.subtle.exportKey(\"jwk\", keyPair.publicKey);\n            return await CryptoUtils.customCalculateJwkThumbprint(publicJwk);\n        } catch (err) {\n            if (err instanceof TypeError) {\n                throw new Error(`Could not retrieve dpop keys from storage: ${err.message}`);\n            } else {\n                throw err;\n            }\n        }\n    }\n\n    public static async generateDPoPKeys() : Promise<CryptoKeyPair> {\n        return await window.crypto.subtle.generateKey(\n            {\n                name: \"ECDSA\",\n                namedCurve: \"P-256\",\n            },\n            false,\n            [\"sign\", \"verify\"],\n        );\n    }\n}\n", "// Copyright (c) <PERSON> & <PERSON>. All rights reserved.\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\nimport { Logger } from \"./Logger\";\n\n/**\n * @internal\n */\nexport type Callback<EventType extends unknown[]> = (...ev: EventType) => (Promise<void> | void);\n\n/**\n * @internal\n */\nexport class Event<EventType extends unknown[]> {\n    protected readonly _logger: Logger;\n\n    private readonly _callbacks: Array<Callback<EventType>> = [];\n\n    public constructor(protected readonly _name: string) {\n        this._logger = new Logger(`Event('${this._name}')`);\n    }\n\n    public addHandler(cb: Callback<EventType>): () => void {\n        this._callbacks.push(cb);\n        return () => this.removeHandler(cb);\n    }\n\n    public removeHandler(cb: Callback<EventType>): void {\n        const idx = this._callbacks.lastIndexOf(cb);\n        if (idx >= 0) {\n            this._callbacks.splice(idx, 1);\n        }\n    }\n\n    public async raise(...ev: EventType): Promise<void> {\n        this._logger.debug(\"raise:\", ...ev);\n        for (const cb of this._callbacks) {\n            await cb(...ev);\n        }\n    }\n}\n", "/**\n *\n * @public\n * @see https://developer.mozilla.org/en-US/docs/Web/API/Window/open#window_features\n */\nexport interface PopupWindowFeatures {\n    left?: number;\n    top?: number;\n    width?: number;\n    height?: number;\n    menubar?: boolean | string;\n    toolbar?: boolean | string;\n    location?: boolean | string;\n    status?: boolean | string;\n    resizable?: boolean | string;\n    scrollbars?: boolean | string;\n    /** Close popup window after time in seconds, by default it is -1. To enable this feature, set value greater than 0. */\n    closePopupWindowAfterInSeconds?: number;\n\n    [k: string]: boolean | string | number | undefined;\n}\n\nexport class PopupUtils {\n    /**\n     * Populates a map of window features with a placement centered in front of\n     * the current window. If no explicit width is given, a default value is\n     * binned into [800, 720, 600, 480, 360] based on the current window's width.\n     */\n    static center({ ...features }: PopupWindowFeatures): PopupWindowFeatures {\n        if (features.width == null)\n            features.width = [800, 720, 600, 480].find(width => width <= window.outerWidth / 1.618) ?? 360;\n        features.left ??= Math.max(0, Math.round(window.screenX + (window.outerWidth - features.width) / 2));\n        if (features.height != null)\n            features.top ??= Math.max(0, Math.round(window.screenY + (window.outerHeight - features.height) / 2));\n        return features;\n    }\n\n    static serialize(features: PopupWindowFeatures): string {\n        return Object.entries(features)\n            .filter(([, value]) => value != null)\n            .map(([key, value]) => `${key}=${typeof value !== \"boolean\" ? value as string : value ? \"yes\" : \"no\"}`)\n            .join(\",\");\n    }\n}\n", "// Copyright (c) Brock <PERSON> & <PERSON>. All rights reserved.\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\nimport { Event } from \"./Event\";\nimport { Logger } from \"./Logger\";\n\n/**\n * @internal\n */\nexport class Timer extends Event<[void]> {\n    protected readonly _logger = new Logger(`Timer('${this._name}')`);\n    private _timerHandle: ReturnType<typeof setInterval> | null = null;\n    private _expiration = 0;\n\n    // get the time\n    public static getEpochTime(): number {\n        return Math.floor(Date.now() / 1000);\n    }\n\n    public init(durationInSeconds: number): void {\n        const logger = this._logger.create(\"init\");\n        durationInSeconds = Math.max(Math.floor(durationInSeconds), 1);\n        const expiration = Timer.getEpochTime() + durationInSeconds;\n        if (this.expiration === expiration && this._timerHandle) {\n            // no need to reinitialize to same expiration, so bail out\n            logger.debug(\"skipping since already initialized for expiration at\", this.expiration);\n            return;\n        }\n\n        this.cancel();\n\n        logger.debug(\"using duration\", durationInSeconds);\n        this._expiration = expiration;\n\n        // we're using a fairly short timer and then checking the expiration in the\n        // callback to handle scenarios where the browser device sleeps, and then\n        // the timers end up getting delayed.\n        const timerDurationInSeconds = Math.min(durationInSeconds, 5);\n        this._timerHandle = setInterval(this._callback, timerDurationInSeconds * 1000);\n    }\n\n    public get expiration(): number {\n        return this._expiration;\n    }\n\n    public cancel(): void {\n        this._logger.create(\"cancel\");\n        if (this._timerHandle) {\n            clearInterval(this._timerHandle);\n            this._timerHandle = null;\n        }\n    }\n\n    protected _callback = (): void => {\n        const diff = this._expiration - Timer.getEpochTime();\n        this._logger.debug(\"timer completes in\", diff);\n\n        if (this._expiration <= Timer.getEpochTime()) {\n            this.cancel();\n            void super.raise();\n        }\n    };\n}\n", "// Copyright (c) <PERSON> & <PERSON>. All rights reserved.\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\n/**\n * @internal\n */\nexport class UrlUtils {\n    public static readParams(url: string, responseMode: \"query\" | \"fragment\" = \"query\"): URLSearchParams {\n        if (!url) throw new TypeError(\"Invalid URL\");\n        // the base URL is irrelevant, it's just here to support relative url arguments\n        const parsedUrl = new URL(url, \"http://127.0.0.1\");\n        const params = parsedUrl[responseMode === \"fragment\" ? \"hash\" : \"search\"];\n        return new URLSearchParams(params.slice(1));\n    }\n}\n\n/**\n * @internal\n */\nexport const URL_STATE_DELIMITER = \";\";", "// Copyright (c) <PERSON> & <PERSON>. All rights reserved.\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\nimport { Logger } from \"../utils\";\n\n/**\n * Error class thrown in case of an authentication error.\n *\n * @public\n * @see https://openid.net/specs/openid-connect-core-1_0.html#AuthError\n */\nexport class ErrorResponse extends Error {\n    /** Marker to detect class: \"ErrorResponse\" */\n    public readonly name: string = \"ErrorResponse\";\n\n    /** An error code string that can be used to classify the types of errors that occur and to respond to errors. */\n    public readonly error: string | null;\n    /** additional information that can help a developer identify the cause of the error.*/\n    public readonly error_description: string | null;\n    /**\n     * URI identifying a human-readable web page with information about the error, used to provide the client\n       developer with additional information about the error.\n    */\n    public readonly error_uri: string | null;\n\n    /** custom state data set during the initial signin request */\n    public state?: unknown;\n\n    public readonly session_state: string | null;\n\n    public url_state?: string;\n\n    public constructor(\n        args: {\n            error?: string | null; error_description?: string | null; error_uri?: string | null;\n            userState?: unknown; session_state?: string | null; url_state?: string;\n        },\n        /** The x-www-form-urlencoded request body sent to the authority server */\n        public readonly form?: URLSearchParams,\n    ) {\n        super(args.error_description || args.error || \"\");\n\n        if (!args.error) {\n            Logger.error(\"ErrorResponse\", \"No error passed\");\n            throw new Error(\"No error passed\");\n        }\n\n        this.error = args.error;\n        this.error_description = args.error_description ?? null;\n        this.error_uri = args.error_uri ?? null;\n\n        this.state = args.userState;\n        this.session_state = args.session_state ?? null;\n        this.url_state = args.url_state;\n    }\n}\n", "// Copyright (C) 2021 AuthTS Contributors\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\n/**\n * Error class thrown in case of network timeouts (e.g IFrame time out).\n *\n * @public\n */\nexport class ErrorTimeout extends Error {\n    /** Marker to detect class: \"ErrorTimeout\" */\n    public readonly name: string = \"ErrorTimeout\";\n\n    public constructor(message?: string) {\n        super(message);\n    }\n}\n", "// Copyright (c) Brock <PERSON> & <PERSON>. All rights reserved.\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\nimport { Logger, Timer } from \"./utils\";\nimport type { User } from \"./User\";\n\n/**\n * @public\n */\nexport type AccessTokenCallback = (...ev: unknown[]) => (Promise<void> | void);\n\n/**\n * @public\n */\nexport class AccessTokenEvents {\n    protected readonly _logger = new Logger(\"AccessTokenEvents\");\n\n    private readonly _expiringTimer = new Timer(\"Access token expiring\");\n    private readonly _expiredTimer = new Timer(\"Access token expired\");\n    private readonly _expiringNotificationTimeInSeconds: number;\n\n    public constructor(args: { expiringNotificationTimeInSeconds: number }) {\n        this._expiringNotificationTimeInSeconds = args.expiringNotificationTimeInSeconds;\n    }\n\n    public async load(container: User): Promise<void> {\n        const logger = this._logger.create(\"load\");\n        // only register events if there's an access token and it has an expiration\n        if (container.access_token && container.expires_in !== undefined) {\n            const duration = container.expires_in;\n            logger.debug(\"access token present, remaining duration:\", duration);\n\n            if (duration > 0) {\n                // only register expiring if we still have time\n                let expiring = duration - this._expiringNotificationTimeInSeconds;\n                if (expiring <= 0) {\n                    expiring = 1;\n                }\n\n                logger.debug(\"registering expiring timer, raising in\", expiring, \"seconds\");\n                this._expiringTimer.init(expiring);\n            }\n            else {\n                logger.debug(\"canceling existing expiring timer because we're past expiration.\");\n                this._expiringTimer.cancel();\n            }\n\n            // if it's negative, it will still fire\n            const expired = duration + 1;\n            logger.debug(\"registering expired timer, raising in\", expired, \"seconds\");\n            this._expiredTimer.init(expired);\n        }\n        else {\n            this._expiringTimer.cancel();\n            this._expiredTimer.cancel();\n        }\n    }\n\n    public async unload(): Promise<void> {\n        this._logger.debug(\"unload: canceling existing access token timers\");\n        this._expiringTimer.cancel();\n        this._expiredTimer.cancel();\n    }\n\n    /**\n     * Add callback: Raised prior to the access token expiring.\n     */\n    public addAccessTokenExpiring(cb: AccessTokenCallback): () => void {\n        return this._expiringTimer.addHandler(cb);\n    }\n    /**\n     * Remove callback: Raised prior to the access token expiring.\n     */\n    public removeAccessTokenExpiring(cb: AccessTokenCallback): void {\n        this._expiringTimer.removeHandler(cb);\n    }\n\n    /**\n     * Add callback: Raised after the access token has expired.\n     */\n    public addAccessTokenExpired(cb: AccessTokenCallback): () => void {\n        return this._expiredTimer.addHandler(cb);\n    }\n    /**\n     * Remove callback: Raised after the access token has expired.\n     */\n    public removeAccessTokenExpired(cb: AccessTokenCallback): void {\n        this._expiredTimer.removeHandler(cb);\n    }\n}\n", "// Copyright (c) <PERSON> & <PERSON>. All rights reserved.\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\nimport { Logger } from \"./utils\";\n\n/**\n * @internal\n */\nexport class CheckSessionIFrame {\n    private readonly _logger = new Logger(\"CheckSessionIFrame\");\n    private _frame_origin: string;\n    private _frame: HTMLIFrameElement;\n    private _timer: ReturnType<typeof setInterval> | null = null;\n    private _session_state: string | null = null;\n\n    public constructor(\n        private _callback: () => Promise<void>,\n        private _client_id: string,\n        url: string,\n        private _intervalInSeconds: number,\n        private _stopOnError: boolean,\n    ) {\n        const parsedUrl = new URL(url);\n        this._frame_origin = parsedUrl.origin;\n\n        this._frame = window.document.createElement(\"iframe\");\n\n        // shotgun approach\n        this._frame.style.visibility = \"hidden\";\n        this._frame.style.position = \"fixed\";\n        this._frame.style.left = \"-1000px\";\n        this._frame.style.top = \"0\";\n        this._frame.width = \"0\";\n        this._frame.height = \"0\";\n        this._frame.src = parsedUrl.href;\n    }\n\n    public load(): Promise<void> {\n        return new Promise<void>((resolve) => {\n            this._frame.onload = () => {\n                resolve();\n            };\n\n            window.document.body.appendChild(this._frame);\n            window.addEventListener(\"message\", this._message, false);\n        });\n    }\n\n    private _message = (e: MessageEvent<string>): void => {\n        if (e.origin === this._frame_origin &&\n            e.source === this._frame.contentWindow\n        ) {\n            if (e.data === \"error\") {\n                this._logger.error(\"error message from check session op iframe\");\n                if (this._stopOnError) {\n                    this.stop();\n                }\n            }\n            else if (e.data === \"changed\") {\n                this._logger.debug(\"changed message from check session op iframe\");\n                this.stop();\n                void this._callback();\n            }\n            else {\n                this._logger.debug(e.data + \" message from check session op iframe\");\n            }\n        }\n    };\n\n    public start(session_state: string): void {\n        if (this._session_state === session_state) {\n            return;\n        }\n\n        this._logger.create(\"start\");\n\n        this.stop();\n\n        this._session_state = session_state;\n\n        const send = () => {\n            if (!this._frame.contentWindow || !this._session_state) {\n                return;\n            }\n\n            this._frame.contentWindow.postMessage(this._client_id + \" \" + this._session_state, this._frame_origin);\n        };\n\n        // trigger now\n        send();\n\n        // and setup timer\n        this._timer = setInterval(send, this._intervalInSeconds * 1000);\n    }\n\n    public stop(): void {\n        this._logger.create(\"stop\");\n        this._session_state = null;\n\n        if (this._timer) {\n\n            clearInterval(this._timer);\n            this._timer = null;\n        }\n    }\n}\n", "// Copyright (c) <PERSON> & <PERSON>. All rights reserved.\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\nimport { Logger } from \"./utils\";\n\n/**\n * @public\n */\nexport class InMemoryWebStorage implements Storage {\n    private readonly _logger = new Logger(\"InMemoryWebStorage\");\n    private _data: Record<string, string> = {};\n\n    public clear(): void {\n        this._logger.create(\"clear\");\n        this._data = {};\n    }\n\n    public getItem(key: string): string {\n        this._logger.create(`getItem('${key}')`);\n        return this._data[key];\n    }\n\n    public setItem(key: string, value: string): void {\n        this._logger.create(`setItem('${key}')`);\n        this._data[key] = value;\n    }\n\n    public removeItem(key: string): void {\n        this._logger.create(`removeItem('${key}')`);\n        delete this._data[key];\n    }\n\n    public get length(): number {\n        return Object.getOwnPropertyNames(this._data).length;\n    }\n\n    public key(index: number): string {\n        return Object.getOwnPropertyNames(this._data)[index];\n    }\n}\n", "export class ErrorDPoPNonce extends Error {\n    /** Marker to detect class: \"ErrorDPoPNonce\" */\n    public readonly name: string = \"ErrorDPoPNonce\";\n    public readonly nonce: string;\n\n    public constructor(nonce: string, message?: string) {\n        super(message);\n        this.nonce = nonce;\n    }\n}\n", "// Copyright (c) <PERSON> & <PERSON>. All rights reserved.\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\nimport { ErrorResponse, ErrorTimeout } from \"./errors\";\nimport type { ExtraHeader } from \"./OidcClientSettings\";\nimport { Logger } from \"./utils\";\nimport { ErrorDPoPNonce } from \"./errors/ErrorDPoPNonce\";\n\n/**\n * @internal\n */\nexport type JwtHandler = (text: string) => Promise<Record<string, unknown>>;\n\n/**\n * @internal\n */\nexport interface GetJsonOpts {\n    token?: string;\n    credentials?: RequestCredentials;\n    timeoutInSeconds?: number;\n}\n\n/**\n * @internal\n */\nexport interface PostFormOpts {\n    body: URLSearchParams;\n    basicAuth?: string;\n    timeoutInSeconds?: number;\n    initCredentials?: \"same-origin\" | \"include\" | \"omit\";\n    extraHeaders?: Record<string, ExtraHeader>;\n}\n\n/**\n * @internal\n */\nexport class JsonService {\n    private readonly _logger = new Logger(\"JsonService\");\n\n    private _contentTypes: string[] = [];\n\n    public constructor(\n        additionalContentTypes: string[] = [],\n        private _jwtHandler: JwtHandler | null = null,\n        private _extraHeaders: Record<string, ExtraHeader> = {},\n    ) {\n        this._contentTypes.push(...additionalContentTypes, \"application/json\");\n        if (_jwtHandler) {\n            this._contentTypes.push(\"application/jwt\");\n        }\n    }\n\n    protected async fetchWithTimeout(input: RequestInfo, init: RequestInit & { timeoutInSeconds?: number } = {}) {\n        const { timeoutInSeconds, ...initFetch } = init;\n        if (!timeoutInSeconds) {\n            return await fetch(input, initFetch);\n        }\n\n        const controller = new AbortController();\n        const timeoutId = setTimeout(() => controller.abort(), timeoutInSeconds * 1000);\n\n        try {\n            const response = await fetch(input, {\n                ...init,\n                signal: controller.signal,\n            });\n            return response;\n        }\n        catch (err) {\n            if (err instanceof DOMException && err.name === \"AbortError\") {\n                throw new ErrorTimeout(\"Network timed out\");\n            }\n            throw err;\n        }\n        finally {\n            clearTimeout(timeoutId);\n        }\n    }\n\n    public async getJson(url: string, {\n        token,\n        credentials,\n        timeoutInSeconds,\n    }: GetJsonOpts = {}): Promise<Record<string, unknown>> {\n        const logger = this._logger.create(\"getJson\");\n        const headers: HeadersInit = {\n            \"Accept\": this._contentTypes.join(\", \"),\n        };\n        if (token) {\n            logger.debug(\"token passed, setting Authorization header\");\n            headers[\"Authorization\"] = \"Bearer \" + token;\n        }\n\n        this._appendExtraHeaders(headers);\n\n        let response: Response;\n        try {\n            logger.debug(\"url:\", url);\n            response = await this.fetchWithTimeout(url, { method: \"GET\", headers, timeoutInSeconds, credentials });\n        }\n        catch (err) {\n            logger.error(\"Network Error\");\n            throw err;\n        }\n\n        logger.debug(\"HTTP response received, status\", response.status);\n        const contentType = response.headers.get(\"Content-Type\");\n        if (contentType && !this._contentTypes.find(item => contentType.startsWith(item))) {\n            logger.throw(new Error(`Invalid response Content-Type: ${(contentType ?? \"undefined\")}, from URL: ${url}`));\n        }\n        if (response.ok && this._jwtHandler && contentType?.startsWith(\"application/jwt\")) {\n            return await this._jwtHandler(await response.text());\n        }\n        let json: Record<string, unknown>;\n        try {\n            json = await response.json();\n        }\n        catch (err) {\n            logger.error(\"Error parsing JSON response\", err);\n            if (response.ok) throw err;\n            throw new Error(`${response.statusText} (${response.status})`);\n        }\n        if (!response.ok) {\n            logger.error(\"Error from server:\", json);\n            if (json.error) {\n                throw new ErrorResponse(json);\n            }\n            throw new Error(`${response.statusText} (${response.status}): ${JSON.stringify(json)}`);\n        }\n        return json;\n    }\n\n    public async postForm(url: string, {\n        body,\n        basicAuth,\n        timeoutInSeconds,\n        initCredentials,\n        extraHeaders,\n    }: PostFormOpts): Promise<Record<string, unknown>> {\n        const logger = this._logger.create(\"postForm\");\n        const headers: HeadersInit = {\n            \"Accept\": this._contentTypes.join(\", \"),\n            \"Content-Type\": \"application/x-www-form-urlencoded\",\n            ...extraHeaders,\n        };\n        if (basicAuth !== undefined) {\n            headers[\"Authorization\"] = \"Basic \" + basicAuth;\n        }\n\n        this._appendExtraHeaders(headers);\n\n        let response: Response;\n        try {\n            logger.debug(\"url:\", url);\n            response = await this.fetchWithTimeout(url, { method: \"POST\", headers, body, timeoutInSeconds, credentials: initCredentials });\n        }\n        catch (err) {\n            logger.error(\"Network error\");\n            throw err;\n        }\n\n        logger.debug(\"HTTP response received, status\", response.status);\n        const contentType = response.headers.get(\"Content-Type\");\n        if (contentType && !this._contentTypes.find(item => contentType.startsWith(item))) {\n            throw new Error(`Invalid response Content-Type: ${(contentType ?? \"undefined\")}, from URL: ${url}`);\n        }\n\n        const responseText = await response.text();\n\n        let json: Record<string, unknown> = {};\n        if (responseText) {\n            try {\n                json = JSON.parse(responseText);\n            }\n            catch (err) {\n                logger.error(\"Error parsing JSON response\", err);\n                if (response.ok) throw err;\n                throw new Error(`${response.statusText} (${response.status})`);\n            }\n        }\n\n        if (!response.ok) {\n            logger.error(\"Error from server:\", json);\n            if (response.headers.has(\"dpop-nonce\")) {\n                const nonce = response.headers.get(\"dpop-nonce\") as string;\n                throw new ErrorDPoPNonce(nonce, `${JSON.stringify(json)}`);\n            }\n            if (json.error) {\n                throw new ErrorResponse(json, body);\n            }\n            throw new Error(`${response.statusText} (${response.status}): ${JSON.stringify(json)}`);\n        }\n\n        return json;\n    }\n\n    private _appendExtraHeaders(\n        headers: Record<string, string>,\n    ): void {\n        const logger = this._logger.create(\"appendExtraHeaders\");\n        const customKeys = Object.keys(this._extraHeaders);\n        const protectedHeaders = [\n            \"accept\",\n            \"content-type\",\n        ];\n        const preventOverride = [\n            \"authorization\",\n        ];\n        if (customKeys.length === 0) {\n            return;\n        }\n        customKeys.forEach((headerName) => {\n            if (protectedHeaders.includes(headerName.toLocaleLowerCase())) {\n                logger.warn(\"Protected header could not be set\", headerName, protectedHeaders);\n                return;\n            }\n            if (preventOverride.includes(headerName.toLocaleLowerCase()) &&\n                Object.keys(headers).includes(headerName)) {\n                logger.warn(\"Header could not be overridden\", headerName, preventOverride);\n                return;\n            }\n            const content = (typeof this._extraHeaders[headerName] === \"function\") ?\n                (this._extraHeaders[headerName] as ()=>string)() :\n                this._extraHeaders[headerName];\n            if (content && content !== \"\") {\n                headers[headerName] = content;\n            }\n        });\n    }\n}\n", "// Copyright (c) <PERSON> & <PERSON>. All rights reserved.\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\nimport { Logger } from \"./utils\";\nimport { JsonService } from \"./JsonService\";\nimport type { OidcClientSettingsStore, SigningKey } from \"./OidcClientSettings\";\nimport type { OidcMetadata } from \"./OidcMetadata\";\n\n/**\n * @public\n * @see https://openid.net/specs/openid-connect-discovery-1_0.html#ProviderMetadata\n */\nexport class MetadataService {\n    private readonly _logger = new Logger(\"MetadataService\");\n    private readonly _jsonService;\n\n    // cache\n    private _metadataUrl: string;\n    private _signingKeys: SigningKey[] | null = null;\n    private _metadata: Partial<OidcMetadata> | null = null;\n    private _fetchRequestCredentials: RequestCredentials | undefined;\n\n    public constructor(private readonly _settings: OidcClientSettingsStore) {\n        this._metadataUrl = this._settings.metadataUrl;\n        this._jsonService = new JsonService(\n            [\"application/jwk-set+json\"],\n            null,\n            this._settings.extraHeaders,\n        );\n        if (this._settings.signingKeys) {\n            this._logger.debug(\"using signingKeys from settings\");\n            this._signingKeys = this._settings.signingKeys;\n        }\n\n        if (this._settings.metadata) {\n            this._logger.debug(\"using metadata from settings\");\n            this._metadata = this._settings.metadata;\n        }\n\n        if (this._settings.fetchRequestCredentials) {\n            this._logger.debug(\"using fetchRequestCredentials from settings\");\n            this._fetchRequestCredentials = this._settings.fetchRequestCredentials;\n        }\n    }\n\n    public resetSigningKeys(): void {\n        this._signingKeys = null;\n    }\n\n    public async getMetadata(): Promise<Partial<OidcMetadata>> {\n        const logger = this._logger.create(\"getMetadata\");\n        if (this._metadata) {\n            logger.debug(\"using cached values\");\n            return this._metadata;\n        }\n\n        if (!this._metadataUrl) {\n            logger.throw(new Error(\"No authority or metadataUrl configured on settings\"));\n            // eslint-disable-next-line @typescript-eslint/only-throw-error\n            throw null; // https://github.com/microsoft/TypeScript/issues/46972\n        }\n\n        logger.debug(\"getting metadata from\", this._metadataUrl);\n        const metadata = await this._jsonService.getJson(this._metadataUrl, { credentials: this._fetchRequestCredentials, timeoutInSeconds: this._settings.requestTimeoutInSeconds });\n\n        logger.debug(\"merging remote JSON with seed metadata\");\n        this._metadata = Object.assign({}, metadata, this._settings.metadataSeed);\n        return this._metadata;\n    }\n\n    public getIssuer(): Promise<string> {\n        return this._getMetadataProperty(\"issuer\") as Promise<string>;\n    }\n\n    public getAuthorizationEndpoint(): Promise<string> {\n        return this._getMetadataProperty(\"authorization_endpoint\") as Promise<string>;\n    }\n\n    public getUserInfoEndpoint(): Promise<string> {\n        return this._getMetadataProperty(\"userinfo_endpoint\") as Promise<string>;\n    }\n\n    public getTokenEndpoint(optional: false): Promise<string>;\n    public getTokenEndpoint(optional?: true): Promise<string | undefined>;\n    public getTokenEndpoint(optional = true): Promise<string | undefined> {\n        return this._getMetadataProperty(\"token_endpoint\", optional) as Promise<string | undefined>;\n    }\n\n    public getCheckSessionIframe(): Promise<string | undefined> {\n        return this._getMetadataProperty(\"check_session_iframe\", true) as Promise<string | undefined>;\n    }\n\n    public getEndSessionEndpoint(): Promise<string | undefined> {\n        return this._getMetadataProperty(\"end_session_endpoint\", true) as Promise<string | undefined>;\n    }\n\n    public getRevocationEndpoint(optional: false): Promise<string>;\n    public getRevocationEndpoint(optional?: true): Promise<string | undefined>;\n    public getRevocationEndpoint(optional = true): Promise<string | undefined> {\n        return this._getMetadataProperty(\"revocation_endpoint\", optional) as Promise<string | undefined>;\n    }\n\n    public getKeysEndpoint(optional: false): Promise<string>;\n    public getKeysEndpoint(optional?: true): Promise<string | undefined>;\n    public getKeysEndpoint(optional = true): Promise<string | undefined> {\n        return this._getMetadataProperty(\"jwks_uri\", optional) as Promise<string | undefined>;\n    }\n\n    protected async _getMetadataProperty(name: keyof OidcMetadata, optional=false): Promise<string | boolean | string[] | undefined> {\n        const logger = this._logger.create(`_getMetadataProperty('${name}')`);\n\n        const metadata = await this.getMetadata();\n        logger.debug(\"resolved\");\n\n        if (metadata[name] === undefined) {\n            if (optional === true) {\n                logger.warn(\"Metadata does not contain optional property\");\n                return undefined;\n            }\n\n            logger.throw(new Error(\"Metadata does not contain property \" + name));\n        }\n\n        return metadata[name];\n    }\n\n    public async getSigningKeys(): Promise<SigningKey[] | null> {\n        const logger = this._logger.create(\"getSigningKeys\");\n        if (this._signingKeys) {\n            logger.debug(\"returning signingKeys from cache\");\n            return this._signingKeys;\n        }\n\n        const jwks_uri = await this.getKeysEndpoint(false);\n        logger.debug(\"got jwks_uri\", jwks_uri);\n\n        const keySet = await this._jsonService.getJson(jwks_uri, { timeoutInSeconds: this._settings.requestTimeoutInSeconds });\n        logger.debug(\"got key set\", keySet);\n\n        if (!Array.isArray(keySet.keys)) {\n            logger.throw(new Error(\"Missing keys on keyset\"));\n            // eslint-disable-next-line @typescript-eslint/only-throw-error\n            throw null; // https://github.com/microsoft/TypeScript/issues/46972\n        }\n\n        this._signingKeys = keySet.keys;\n        return this._signingKeys;\n    }\n}\n", "// Copyright (c) <PERSON> & <PERSON>. All rights reserved.\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\nimport { Logger } from \"./utils\";\nimport type { StateStore } from \"./StateStore\";\nimport type { AsyncStorage } from \"./AsyncStorage\";\n\n/**\n * @public\n */\nexport class WebStorageStateStore implements StateStore {\n    private readonly _logger = new Logger(\"WebStorageStateStore\");\n\n    private readonly _store: AsyncStorage | Storage;\n    private readonly _prefix: string;\n\n    public constructor({\n        prefix = \"oidc.\",\n        store = localStorage,\n    }: { prefix?: string; store?: AsyncStorage | Storage } = {}) {\n        this._store = store;\n        this._prefix = prefix;\n    }\n\n    public async set(key: string, value: string): Promise<void> {\n        this._logger.create(`set('${key}')`);\n\n        key = this._prefix + key;\n        await this._store.setItem(key, value);\n    }\n\n    public async get(key: string): Promise<string | null> {\n        this._logger.create(`get('${key}')`);\n\n        key = this._prefix + key;\n        const item = await this._store.getItem(key);\n        return item;\n    }\n\n    public async remove(key: string): Promise<string | null> {\n        this._logger.create(`remove('${key}')`);\n\n        key = this._prefix + key;\n        const item = await this._store.getItem(key);\n        await this._store.removeItem(key);\n        return item;\n    }\n\n    public async getAllKeys(): Promise<string[]> {\n        this._logger.create(\"getAllKeys\");\n        const len = await this._store.length;\n\n        const keys = [];\n        for (let index = 0; index < len; index++) {\n            const key = await this._store.key(index);\n            if (key && key.indexOf(this._prefix) === 0) {\n                keys.push(key.substr(this._prefix.length));\n            }\n        }\n        return keys;\n    }\n}\n", "// Copyright (c) <PERSON> & <PERSON>. All rights reserved.\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\nimport { WebStorageStateStore } from \"./WebStorageStateStore\";\nimport type { OidcMetadata } from \"./OidcMetadata\";\nimport type { StateStore } from \"./StateStore\";\nimport { InMemoryWebStorage } from \"./InMemoryWebStorage\";\nimport type { DPoPStore } from \"./DPoPStore\";\n\nconst DefaultResponseType = \"code\";\nconst DefaultScope = \"openid\";\nconst DefaultClientAuthentication = \"client_secret_post\";\nconst DefaultStaleStateAgeInSeconds = 60 * 15;\n\n/**\n * @public\n */\nexport type SigningKey = Record<string, string | string[]>;\n\n/**\n * @public\n */\nexport type ExtraHeader = string | (() => string);\n\n/**\n * Optional DPoP settings\n * @public\n */\nexport interface DPoPSettings {\n    bind_authorization_code?: boolean;\n    store: DPoPStore;\n}\n\n/**\n * The settings used to configure the {@link OidcClient}.\n *\n * @public\n */\nexport interface OidcClientSettings {\n    /** The URL of the OIDC/OAuth2 provider */\n    authority: string;\n    metadataUrl?: string;\n    /** Provide metadata when authority server does not allow CORS on the metadata endpoint */\n    metadata?: Partial<OidcMetadata>;\n    /** Can be used to seed or add additional values to the results of the discovery request */\n    metadataSeed?: Partial<OidcMetadata>;\n    /** Provide signingKeys when authority server does not allow CORS on the jwks uri */\n    signingKeys?: SigningKey[];\n\n    /** Your client application's identifier as registered with the OIDC/OAuth2 */\n    client_id: string;\n    client_secret?: string;\n    /** The type of response desired from the OIDC/OAuth2 provider (default: \"code\") */\n    response_type?: string;\n    /** The scope being requested from the OIDC/OAuth2 provider (default: \"openid\") */\n    scope?: string;\n    /** The redirect URI of your client application to receive a response from the OIDC/OAuth2 provider */\n    redirect_uri: string;\n    /** The OIDC/OAuth2 post-logout redirect URI */\n    post_logout_redirect_uri?: string;\n\n    /**\n     * Client authentication method that is used to authenticate when using the token endpoint (default: \"client_secret_post\")\n     * - \"client_secret_basic\": using the HTTP Basic authentication scheme\n     * - \"client_secret_post\": including the client credentials in the request body\n     *\n     * See https://openid.net/specs/openid-connect-core-1_0.html#ClientAuthentication\n     */\n    client_authentication?: \"client_secret_basic\" | \"client_secret_post\";\n\n    /** optional protocol param */\n    prompt?: string;\n    /** optional protocol param */\n    display?: string;\n    /** optional protocol param */\n    max_age?: number;\n    /** optional protocol param */\n    ui_locales?: string;\n    /** optional protocol param */\n    acr_values?: string;\n    /** optional protocol param */\n    resource?: string | string[];\n\n    /**\n     * Optional protocol param\n     * The response mode used by the authority server is defined by the response_type unless explicitly specified:\n     * - Response mode for the OAuth 2.0 response type \"code\" is the \"query\" encoding\n     * - Response mode for the OAuth 2.0 response type \"token\" is the \"fragment\" encoding\n     *\n     * @see https://openid.net/specs/oauth-v2-multiple-response-types-1_0.html#ResponseModes\n     */\n    response_mode?: \"query\" | \"fragment\";\n\n    /**\n     * Should optional OIDC protocol claims be removed from profile or specify the ones to be removed (default: true)\n     * When true, the following claims are removed by default: [\"nbf\", \"jti\", \"auth_time\", \"nonce\", \"acr\", \"amr\", \"azp\", \"at_hash\"]\n     * When specifying claims, the following claims are not allowed: [\"sub\", \"iss\", \"aud\", \"exp\", \"iat\"]\n    */\n    filterProtocolClaims?: boolean | string[];\n    /** Flag to control if additional identity data is loaded from the user info endpoint in order to populate the user's profile (default: false) */\n    loadUserInfo?: boolean;\n    /** Number (in seconds) indicating the age of state entries in storage for authorize requests that are considered abandoned and thus can be cleaned up (default: 900) */\n    staleStateAgeInSeconds?: number;\n\n    /**\n     * Indicates how objects returned from the user info endpoint as claims (e.g. `address`) are merged into the claims from the\n     * id token as a single object.  (default: `{ array: \"replace\" }`)\n     * - array: \"replace\": natives (string, int, float) and arrays are replaced, objects are merged as distinct objects\n     * - array: \"merge\": natives (string, int, float) are replaced, arrays and objects are merged as distinct objects\n     */\n    mergeClaimsStrategy?: { array: \"replace\" | \"merge\" };\n\n    /**\n     * Storage object used to persist interaction state (default: window.localStorage, InMemoryWebStorage iff no window).\n     * E.g. `stateStore: new WebStorageStateStore({ store: window.localStorage })`\n     */\n    stateStore?: StateStore;\n\n    /**\n     * An object containing additional query string parameters to be including in the authorization request.\n     * E.g, when using Azure AD to obtain an access token an additional resource parameter is required. extraQueryParams: `{resource:\"some_identifier\"}`\n     */\n    extraQueryParams?: Record<string, string | number | boolean>;\n\n    extraTokenParams?: Record<string, unknown>;\n\n    /**\n     * An object containing additional header to be including in request.\n     */\n    extraHeaders?: Record<string, ExtraHeader>;\n\n    /**\n     * DPoP enabled or disabled\n     */\n    dpop?: DPoPSettings | undefined;\n\n    /**\n     * Will check the content type header of the response of the revocation endpoint to match these passed values (default: [])\n     */\n    revokeTokenAdditionalContentTypes?: string[];\n    /**\n     * Will disable PKCE validation, changing to true will not append to sign in request code_challenge and code_challenge_method. (default: false)\n     */\n    disablePKCE?: boolean;\n    /**\n     * Sets the credentials for fetch requests. (default: \"same-origin\")\n     * Use this if you need to send cookies to the OIDC/OAuth2 provider or if you are using a proxy that requires cookies\n     */\n    fetchRequestCredentials?: RequestCredentials;\n\n    /**\n     * Only scopes in this list will be passed in the token refresh request.\n     */\n    refreshTokenAllowedScope?: string | undefined;\n\n    /**\n     * Defines request timeouts globally across all requests made to the authorisation server\n     */\n    requestTimeoutInSeconds?: number | undefined;\n\n    /**\n     * https://datatracker.ietf.org/doc/html/rfc6749#section-3.3 describes behavior when omitting scopes from sign in requests\n     * If the IDP supports default scopes, this setting will ignore the scopes property passed to the config. (Default: false)\n     */\n    omitScopeWhenRequesting?: boolean;\n}\n\n/**\n * The settings with defaults applied of the {@link OidcClient}.\n *\n * @public\n * @see {@link OidcClientSettings}\n */\nexport class OidcClientSettingsStore {\n    // metadata\n    public readonly authority: string;\n    public readonly metadataUrl: string;\n    public readonly metadata: Partial<OidcMetadata> | undefined;\n    public readonly metadataSeed: Partial<OidcMetadata> | undefined;\n    public readonly signingKeys: SigningKey[] | undefined;\n\n    // client config\n    public readonly client_id: string;\n    public readonly client_secret: string | undefined;\n    public readonly response_type: string;\n    public readonly scope: string;\n    public readonly redirect_uri: string;\n    public readonly post_logout_redirect_uri: string | undefined;\n    public readonly client_authentication: \"client_secret_basic\" | \"client_secret_post\";\n\n    // optional protocol params\n    public readonly prompt: string | undefined;\n    public readonly display: string | undefined;\n    public readonly max_age: number | undefined;\n    public readonly ui_locales: string | undefined;\n    public readonly acr_values: string | undefined;\n    public readonly resource: string | string[] | undefined;\n    public readonly response_mode: \"query\" | \"fragment\" | undefined;\n\n    // behavior flags\n    public readonly filterProtocolClaims: boolean | string[];\n    public readonly loadUserInfo: boolean;\n    public readonly staleStateAgeInSeconds: number;\n    public readonly mergeClaimsStrategy: { array: \"replace\" | \"merge\" };\n    public readonly omitScopeWhenRequesting: boolean;\n\n    public readonly stateStore: StateStore;\n\n    // extra\n    public readonly extraQueryParams: Record<string, string | number | boolean>;\n    public readonly extraTokenParams: Record<string, unknown>;\n    public readonly dpop: DPoPSettings | undefined;\n    public readonly extraHeaders: Record<string, ExtraHeader>;\n\n    public readonly revokeTokenAdditionalContentTypes?: string[];\n    public readonly fetchRequestCredentials: RequestCredentials;\n    public readonly refreshTokenAllowedScope: string | undefined;\n    public readonly disablePKCE: boolean;\n    public readonly requestTimeoutInSeconds: number | undefined;\n\n    public constructor({\n        // metadata related\n        authority, metadataUrl, metadata, signingKeys, metadataSeed,\n        // client related\n        client_id, client_secret, response_type = DefaultResponseType, scope = DefaultScope,\n        redirect_uri, post_logout_redirect_uri,\n        client_authentication = DefaultClientAuthentication,\n        // optional protocol\n        prompt, display, max_age, ui_locales, acr_values, resource, response_mode,\n        // behavior flags\n        filterProtocolClaims = true,\n        loadUserInfo = false,\n        requestTimeoutInSeconds,\n        staleStateAgeInSeconds = DefaultStaleStateAgeInSeconds,\n        mergeClaimsStrategy = { array: \"replace\" },\n        disablePKCE = false,\n        // other behavior\n        stateStore,\n        revokeTokenAdditionalContentTypes,\n        fetchRequestCredentials,\n        refreshTokenAllowedScope,\n        // extra\n        extraQueryParams = {},\n        extraTokenParams = {},\n        extraHeaders = {},\n        dpop,\n        omitScopeWhenRequesting = false,\n    }: OidcClientSettings) {\n\n        this.authority = authority;\n\n        if (metadataUrl) {\n            this.metadataUrl = metadataUrl;\n        } else {\n            this.metadataUrl = authority;\n            if (authority) {\n                if (!this.metadataUrl.endsWith(\"/\")) {\n                    this.metadataUrl += \"/\";\n                }\n                this.metadataUrl += \".well-known/openid-configuration\";\n            }\n        }\n\n        this.metadata = metadata;\n        this.metadataSeed = metadataSeed;\n        this.signingKeys = signingKeys;\n\n        this.client_id = client_id;\n        this.client_secret = client_secret;\n        this.response_type = response_type;\n        this.scope = scope;\n        this.redirect_uri = redirect_uri;\n        this.post_logout_redirect_uri = post_logout_redirect_uri;\n        this.client_authentication = client_authentication;\n\n        this.prompt = prompt;\n        this.display = display;\n        this.max_age = max_age;\n        this.ui_locales = ui_locales;\n        this.acr_values = acr_values;\n        this.resource = resource;\n        this.response_mode = response_mode;\n\n        this.filterProtocolClaims = filterProtocolClaims ?? true;\n        this.loadUserInfo = !!loadUserInfo;\n        this.staleStateAgeInSeconds = staleStateAgeInSeconds;\n        this.mergeClaimsStrategy = mergeClaimsStrategy;\n        this.omitScopeWhenRequesting = omitScopeWhenRequesting;\n        this.disablePKCE = !!disablePKCE;\n        this.revokeTokenAdditionalContentTypes = revokeTokenAdditionalContentTypes;\n\n        this.fetchRequestCredentials = fetchRequestCredentials ? fetchRequestCredentials : \"same-origin\";\n        this.requestTimeoutInSeconds = requestTimeoutInSeconds;\n\n        if (stateStore) {\n            this.stateStore = stateStore;\n        }\n        else {\n            const store = typeof window !== \"undefined\" ? window.localStorage : new InMemoryWebStorage();\n            this.stateStore = new WebStorageStateStore({ store });\n        }\n\n        this.refreshTokenAllowedScope = refreshTokenAllowedScope;\n\n        this.extraQueryParams = extraQueryParams;\n        this.extraTokenParams = extraTokenParams;\n        this.extraHeaders = extraHeaders;\n\n        this.dpop = dpop;\n        if (this.dpop && !this.dpop?.store) {\n            throw new Error(\"A DPoPStore is required when dpop is enabled\");\n        }\n    }\n}\n", "// Copyright (c) Brock <PERSON> & Dominic<PERSON>er. All rights reserved.\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\nimport { Logger, JwtUtils } from \"./utils\";\nimport { JsonService } from \"./JsonService\";\nimport type { MetadataService } from \"./MetadataService\";\nimport type { JwtClaims } from \"./Claims\";\nimport type { OidcClientSettingsStore } from \"./OidcClientSettings\";\n\n/**\n * @internal\n */\nexport class UserInfoService {\n    protected readonly _logger = new Logger(\"UserInfoService\");\n    private readonly _jsonService: JsonService;\n\n    public constructor(private readonly _settings: OidcClientSettingsStore,\n        private readonly _metadataService: MetadataService,\n    ) {\n        this._jsonService = new JsonService(\n            undefined,\n            this._getClaimsFromJwt,\n            this._settings.extraHeaders,\n        );\n    }\n\n    public async getClaims(token: string): Promise<JwtClaims> {\n        const logger = this._logger.create(\"getClaims\");\n        if (!token) {\n            this._logger.throw(new Error(\"No token passed\"));\n        }\n\n        const url = await this._metadataService.getUserInfoEndpoint();\n        logger.debug(\"got userinfo url\", url);\n\n        const claims = await this._jsonService.getJson(url, {\n            token,\n            credentials: this._settings.fetchRequestCredentials,\n            timeoutInSeconds: this._settings.requestTimeoutInSeconds,\n        });\n        logger.debug(\"got claims\", claims);\n\n        return claims;\n    }\n\n    protected _getClaimsFromJwt = async (responseText: string): Promise<JwtClaims> => {\n        const logger = this._logger.create(\"_getClaimsFromJwt\");\n        try {\n            const payload = JwtUtils.decode(responseText);\n            logger.debug(\"JWT decoding successful\");\n\n            return payload;\n        } catch (err) {\n            logger.error(\"Error parsing JWT response\");\n            throw err;\n        }\n    };\n}\n", "// Copyright (c) <PERSON> & <PERSON>. All rights reserved.\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\nimport { CryptoUtils, Logger } from \"./utils\";\nimport { JsonService } from \"./JsonService\";\nimport type { MetadataService } from \"./MetadataService\";\nimport type { ExtraHeader, OidcClientSettingsStore } from \"./OidcClientSettings\";\n\n/**\n * @internal\n */\nexport interface ExchangeCodeArgs {\n    client_id?: string;\n    client_secret?: string;\n    redirect_uri?: string;\n\n    grant_type?: string;\n    code: string;\n    code_verifier?: string;\n\n    extraHeaders?: Record<string, ExtraHeader>;\n}\n\n/**\n * @internal\n */\nexport interface ExchangeCredentialsArgs {\n    client_id?: string;\n    client_secret?: string;\n\n    grant_type?: string;\n    scope?: string;\n\n    username: string;\n    password: string;\n}\n\n/**\n * @internal\n */\nexport interface ExchangeRefreshTokenArgs {\n    client_id?: string;\n    client_secret?: string;\n    redirect_uri?: string;\n\n    grant_type?: string;\n    refresh_token: string;\n    scope?: string;\n    resource?: string | string[];\n\n    timeoutInSeconds?: number;\n\n    extraHeaders?: Record<string, ExtraHeader>;\n}\n\n/**\n * @internal\n */\nexport interface RevokeArgs {\n    token: string;\n    token_type_hint?: \"access_token\" | \"refresh_token\";\n}\n\n/**\n * @internal\n */\nexport class TokenClient {\n    private readonly _logger = new Logger(\"TokenClient\");\n    private readonly _jsonService;\n\n    public constructor(\n        private readonly _settings: OidcClientSettingsStore,\n        private readonly _metadataService: MetadataService,\n    ) {\n        this._jsonService = new JsonService(\n            this._settings.revokeTokenAdditionalContentTypes,\n            null,\n            this._settings.extraHeaders,\n        );\n    }\n\n    /**\n     * Exchange code.\n     *\n     * @see https://www.rfc-editor.org/rfc/rfc6749#section-4.1.3\n     */\n    public async exchangeCode({\n        grant_type = \"authorization_code\",\n        redirect_uri = this._settings.redirect_uri,\n        client_id = this._settings.client_id,\n        client_secret = this._settings.client_secret,\n        extraHeaders,\n        ...args\n    }: ExchangeCodeArgs): Promise<Record<string, unknown>> {\n        const logger = this._logger.create(\"exchangeCode\");\n        if (!client_id) {\n            logger.throw(new Error(\"A client_id is required\"));\n        }\n        if (!redirect_uri) {\n            logger.throw(new Error(\"A redirect_uri is required\"));\n        }\n        if (!args.code) {\n            logger.throw(new Error(\"A code is required\"));\n        }\n\n        const params = new URLSearchParams({ grant_type, redirect_uri });\n        for (const [key, value] of Object.entries(args)) {\n            if (value != null) {\n                params.set(key, value);\n            }\n        }\n        let basicAuth: string | undefined;\n        switch (this._settings.client_authentication) {\n            case \"client_secret_basic\":\n                if (client_secret === undefined || client_secret === null) {\n                    logger.throw(new Error(\"A client_secret is required\"));\n                    // eslint-disable-next-line @typescript-eslint/only-throw-error\n                    throw null; // https://github.com/microsoft/TypeScript/issues/46972\n                }\n                basicAuth = CryptoUtils.generateBasicAuth(client_id, client_secret);\n                break;\n            case \"client_secret_post\":\n                params.append(\"client_id\", client_id);\n                if (client_secret) {\n                    params.append(\"client_secret\", client_secret);\n                }\n                break;\n        }\n\n        const url = await this._metadataService.getTokenEndpoint(false);\n        logger.debug(\"got token endpoint\");\n\n        const response = await this._jsonService.postForm(url, {\n            body: params,\n            basicAuth,\n            timeoutInSeconds: this._settings.requestTimeoutInSeconds,\n            initCredentials: this._settings.fetchRequestCredentials,\n            extraHeaders,\n        });\n\n        logger.debug(\"got response\");\n\n        return response;\n    }\n\n    /**\n     * Exchange credentials.\n     *\n     * @see https://www.rfc-editor.org/rfc/rfc6749#section-4.3.2\n     */\n    public async exchangeCredentials({\n        grant_type = \"password\",\n        client_id = this._settings.client_id,\n        client_secret = this._settings.client_secret,\n        scope = this._settings.scope,\n        ...args\n    }: ExchangeCredentialsArgs): Promise<Record<string, unknown>> {\n        const logger = this._logger.create(\"exchangeCredentials\");\n\n        if (!client_id) {\n            logger.throw(new Error(\"A client_id is required\"));\n        }\n\n        const params = new URLSearchParams({ grant_type });\n        if (!this._settings.omitScopeWhenRequesting) {\n            params.set(\"scope\", scope);\n        }\n        for (const [key, value] of Object.entries(args)) {\n            if (value != null) {\n                params.set(key, value);\n            }\n        }\n\n        let basicAuth: string | undefined;\n        switch (this._settings.client_authentication) {\n            case \"client_secret_basic\":\n                if (client_secret === undefined || client_secret === null) {\n                    logger.throw(new Error(\"A client_secret is required\"));\n                    // eslint-disable-next-line @typescript-eslint/only-throw-error\n                    throw null; // https://github.com/microsoft/TypeScript/issues/46972\n                }\n                basicAuth = CryptoUtils.generateBasicAuth(client_id, client_secret);\n                break;\n            case \"client_secret_post\":\n                params.append(\"client_id\", client_id);\n                if (client_secret) {\n                    params.append(\"client_secret\", client_secret);\n                }\n                break;\n        }\n\n        const url = await this._metadataService.getTokenEndpoint(false);\n        logger.debug(\"got token endpoint\");\n\n        const response = await this._jsonService.postForm(url, { body: params, basicAuth, timeoutInSeconds: this._settings.requestTimeoutInSeconds, initCredentials: this._settings.fetchRequestCredentials });\n        logger.debug(\"got response\");\n\n        return response;\n    }\n\n    /**\n     * Exchange a refresh token.\n     *\n     * @see https://www.rfc-editor.org/rfc/rfc6749#section-6\n     */\n    public async exchangeRefreshToken({\n        grant_type = \"refresh_token\",\n        client_id = this._settings.client_id,\n        client_secret = this._settings.client_secret,\n        timeoutInSeconds,\n        extraHeaders,\n        ...args\n    }: ExchangeRefreshTokenArgs): Promise<Record<string, unknown>> {\n        const logger = this._logger.create(\"exchangeRefreshToken\");\n        if (!client_id) {\n            logger.throw(new Error(\"A client_id is required\"));\n        }\n        if (!args.refresh_token) {\n            logger.throw(new Error(\"A refresh_token is required\"));\n        }\n\n        const params = new URLSearchParams({ grant_type });\n        for (const [key, value] of Object.entries(args)) {\n            if (Array.isArray(value)) {\n                value.forEach(param => params.append(key, param));\n            }\n            else if (value != null) {\n                params.set(key, value);\n            }\n        }\n        let basicAuth: string | undefined;\n        switch (this._settings.client_authentication) {\n            case \"client_secret_basic\":\n                if (client_secret === undefined || client_secret === null) {\n                    logger.throw(new Error(\"A client_secret is required\"));\n                    // eslint-disable-next-line @typescript-eslint/only-throw-error\n                    throw null; // https://github.com/microsoft/TypeScript/issues/46972\n                }\n                basicAuth = CryptoUtils.generateBasicAuth(client_id, client_secret);\n                break;\n            case \"client_secret_post\":\n                params.append(\"client_id\", client_id);\n                if (client_secret) {\n                    params.append(\"client_secret\", client_secret);\n                }\n                break;\n        }\n\n        const url = await this._metadataService.getTokenEndpoint(false);\n        logger.debug(\"got token endpoint\");\n\n        const response = await this._jsonService.postForm(url, { body: params, basicAuth, timeoutInSeconds, initCredentials: this._settings.fetchRequestCredentials, extraHeaders });\n        logger.debug(\"got response\");\n\n        return response;\n    }\n\n    /**\n     * Revoke an access or refresh token.\n     *\n     * @see https://datatracker.ietf.org/doc/html/rfc7009#section-2.1\n     */\n    public async revoke(args: RevokeArgs): Promise<void> {\n        const logger = this._logger.create(\"revoke\");\n        if (!args.token) {\n            logger.throw(new Error(\"A token is required\"));\n        }\n\n        const url = await this._metadataService.getRevocationEndpoint(false);\n\n        logger.debug(`got revocation endpoint, revoking ${args.token_type_hint ?? \"default token type\"}`);\n\n        const params = new URLSearchParams();\n        for (const [key, value] of Object.entries(args)) {\n            if (value != null) {\n                params.set(key, value);\n            }\n        }\n        params.set(\"client_id\", this._settings.client_id);\n        if (this._settings.client_secret) {\n            params.set(\"client_secret\", this._settings.client_secret);\n        }\n\n        await this._jsonService.postForm(url, { body: params, timeoutInSeconds: this._settings.requestTimeoutInSeconds });\n        logger.debug(\"got response\");\n    }\n}\n", "// Copyright (c) <PERSON> & Dominic<PERSON>er. All rights reserved.\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\nimport { Logger, JwtUtils } from \"./utils\";\nimport { ErrorResponse } from \"./errors\";\nimport type { MetadataService } from \"./MetadataService\";\nimport { UserInfoService } from \"./UserInfoService\";\nimport { TokenClient } from \"./TokenClient\";\nimport type { ExtraHeader, OidcClientSettingsStore } from \"./OidcClientSettings\";\nimport type { SigninState } from \"./SigninState\";\nimport type { SigninResponse } from \"./SigninResponse\";\nimport type { State } from \"./State\";\nimport type { SignoutResponse } from \"./SignoutResponse\";\nimport type { UserProfile } from \"./User\";\nimport type { RefreshState } from \"./RefreshState\";\nimport type { IdTokenClaims } from \"./Claims\";\nimport type { ClaimsService } from \"./ClaimsService\";\n\n/**\n * @internal\n */\nexport class ResponseValidator {\n    protected readonly _logger = new Logger(\"ResponseValidator\");\n    protected readonly _userInfoService: UserInfoService;\n    protected readonly _tokenClient: TokenClient;\n\n    public constructor(\n        protected readonly _settings: OidcClientSettingsStore,\n        protected readonly _metadataService: MetadataService,\n        protected readonly _claimsService: ClaimsService,\n    ) {\n        this._userInfoService = new UserInfoService(this._settings, this._metadataService);\n        this._tokenClient = new TokenClient(this._settings, this._metadataService);\n    }\n\n    public async validateSigninResponse(response: SigninResponse, state: SigninState, extraHeaders?: Record<string, ExtraHeader>): Promise<void> {\n        const logger = this._logger.create(\"validateSigninResponse\");\n\n        this._processSigninState(response, state);\n        logger.debug(\"state processed\");\n\n        await this._processCode(response, state, extraHeaders);\n        logger.debug(\"code processed\");\n\n        if (response.isOpenId) {\n            this._validateIdTokenAttributes(response);\n        }\n        logger.debug(\"tokens validated\");\n\n        await this._processClaims(response, state?.skipUserInfo, response.isOpenId);\n        logger.debug(\"claims processed\");\n    }\n\n    public async validateCredentialsResponse(response: SigninResponse, skipUserInfo: boolean): Promise<void> {\n        const logger = this._logger.create(\"validateCredentialsResponse\");\n\n        if (response.isOpenId && !!response.id_token) {\n            this._validateIdTokenAttributes(response);\n        }\n        logger.debug(\"tokens validated\");\n\n        await this._processClaims(response, skipUserInfo, response.isOpenId);\n        logger.debug(\"claims processed\");\n    }\n\n    public async validateRefreshResponse(response: SigninResponse, state: RefreshState): Promise<void> {\n        const logger = this._logger.create(\"validateRefreshResponse\");\n\n        response.userState = state.data;\n        // if there's no session_state on the response, copy over session_state from original request\n        response.session_state ??= state.session_state;\n        // if there's no scope on the response, then assume all scopes granted (per-spec) and copy over scopes from original request\n        response.scope ??= state.scope;\n\n        // OpenID Connect Core 1.0 says that id_token is optional in refresh response:\n        // https://openid.net/specs/openid-connect-core-1_0.html#RefreshTokenResponse\n        if (response.isOpenId && !!response.id_token) {\n            this._validateIdTokenAttributes(response, state.id_token);\n            logger.debug(\"ID Token validated\");\n        }\n\n        if (!response.id_token) {\n            // if there's no id_token on the response, copy over id_token from original request\n            response.id_token = state.id_token;\n            // and decoded part too\n            response.profile = state.profile;\n        }\n\n        const hasIdToken = response.isOpenId && !!response.id_token;\n        await this._processClaims(response, false, hasIdToken);\n        logger.debug(\"claims processed\");\n    }\n\n    public validateSignoutResponse(response: SignoutResponse, state: State): void {\n        const logger = this._logger.create(\"validateSignoutResponse\");\n        if (state.id !== response.state) {\n            logger.throw(new Error(\"State does not match\"));\n        }\n\n        // now that we know the state matches, take the stored data\n        // and set it into the response so callers can get their state\n        // this is important for both success & error outcomes\n        logger.debug(\"state validated\");\n        response.userState = state.data;\n\n        if (response.error) {\n            logger.warn(\"Response was error\", response.error);\n            throw new ErrorResponse(response);\n        }\n    }\n\n    protected _processSigninState(response: SigninResponse, state: SigninState): void {\n        const logger = this._logger.create(\"_processSigninState\");\n        if (state.id !== response.state) {\n            logger.throw(new Error(\"State does not match\"));\n        }\n\n        if (!state.client_id) {\n            logger.throw(new Error(\"No client_id on state\"));\n        }\n\n        if (!state.authority) {\n            logger.throw(new Error(\"No authority on state\"));\n        }\n\n        // ensure we're using the correct authority\n        if (this._settings.authority !== state.authority) {\n            logger.throw(new Error(\"authority mismatch on settings vs. signin state\"));\n        }\n        if (this._settings.client_id && this._settings.client_id !== state.client_id) {\n            logger.throw(new Error(\"client_id mismatch on settings vs. signin state\"));\n        }\n\n        // now that we know the state matches, take the stored data\n        // and set it into the response so callers can get their state\n        // this is important for both success & error outcomes\n        logger.debug(\"state validated\");\n        response.userState = state.data;\n        response.url_state = state.url_state;\n        // if there's no scope on the response, then assume all scopes granted (per-spec) and copy over scopes from original request\n        response.scope ??= state.scope;\n\n        if (response.error) {\n            logger.warn(\"Response was error\", response.error);\n            throw new ErrorResponse(response);\n        }\n\n        if (state.code_verifier && !response.code) {\n            logger.throw(new Error(\"Expected code in response\"));\n        }\n\n    }\n\n    protected async _processClaims(response: SigninResponse, skipUserInfo = false, validateSub = true): Promise<void> {\n        const logger = this._logger.create(\"_processClaims\");\n        response.profile = this._claimsService.filterProtocolClaims(response.profile);\n\n        if (skipUserInfo || !this._settings.loadUserInfo || !response.access_token) {\n            logger.debug(\"not loading user info\");\n            return;\n        }\n\n        logger.debug(\"loading user info\");\n        const claims = await this._userInfoService.getClaims(response.access_token);\n        logger.debug(\"user info claims received from user info endpoint\");\n\n        if (validateSub && claims.sub !== response.profile.sub) {\n            logger.throw(new Error(\"subject from UserInfo response does not match subject in ID Token\"));\n        }\n\n        response.profile = this._claimsService.mergeClaims(response.profile, this._claimsService.filterProtocolClaims(claims as IdTokenClaims));\n        logger.debug(\"user info claims received, updated profile:\", response.profile);\n    }\n\n    protected async _processCode(response: SigninResponse, state: SigninState, extraHeaders?: Record<string, ExtraHeader>): Promise<void> {\n        const logger = this._logger.create(\"_processCode\");\n        if (response.code) {\n            logger.debug(\"Validating code\");\n            const tokenResponse = await this._tokenClient.exchangeCode({\n                client_id: state.client_id,\n                client_secret: state.client_secret,\n                code: response.code,\n                redirect_uri: state.redirect_uri,\n                code_verifier: state.code_verifier,\n                extraHeaders: extraHeaders,\n                ...state.extraTokenParams,\n            });\n            Object.assign(response, tokenResponse);\n        } else {\n            logger.debug(\"No code to process\");\n        }\n    }\n\n    protected _validateIdTokenAttributes(response: SigninResponse, existingToken?: string): void {\n        const logger = this._logger.create(\"_validateIdTokenAttributes\");\n\n        logger.debug(\"decoding ID Token JWT\");\n        const incoming = JwtUtils.decode(response.id_token ?? \"\");\n\n        if (!incoming.sub) {\n            logger.throw(new Error(\"ID Token is missing a subject claim\"));\n        }\n\n        if (existingToken) {\n            const existing = JwtUtils.decode(existingToken);\n            if (incoming.sub !== existing.sub) {\n                logger.throw(new Error(\"sub in id_token does not match current sub\"));\n            }\n            if (incoming.auth_time && incoming.auth_time !== existing.auth_time) {\n                logger.throw(new Error(\"auth_time in id_token does not match original auth_time\"));\n            }\n            if (incoming.azp && incoming.azp !== existing.azp) {\n                logger.throw(new Error(\"azp in id_token does not match original azp\"));\n            }\n            if (!incoming.azp && existing.azp) {\n                logger.throw(new Error(\"azp not in id_token, but present in original id_token\"));\n            }\n        }\n\n        response.profile = incoming as UserProfile;\n    }\n}\n", "// Copyright (c) <PERSON> & <PERSON>. All rights reserved.\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\nimport { Logger, CryptoUtils, Timer } from \"./utils\";\nimport type { StateStore } from \"./StateStore\";\n\n/**\n * @public\n */\nexport class State {\n    public readonly id: string;\n    public readonly created: number;\n    public readonly request_type: string | undefined;\n    public readonly url_state: string | undefined;\n\n    /** custom \"state\", which can be used by a caller to have \"data\" round tripped */\n    public readonly data?: unknown;\n\n    public constructor(args: {\n        id?: string;\n        data?: unknown;\n        created?: number;\n        request_type?: string;\n        url_state?: string;\n    }) {\n        this.id = args.id || CryptoUtils.generateUUIDv4();\n        this.data = args.data;\n\n        if (args.created && args.created > 0) {\n            this.created = args.created;\n        }\n        else {\n            this.created = Timer.getEpochTime();\n        }\n        this.request_type = args.request_type;\n        this.url_state = args.url_state;\n    }\n\n    public toStorageString(): string {\n        new Logger(\"State\").create(\"toStorageString\");\n        return JSON.stringify({\n            id: this.id,\n            data: this.data,\n            created: this.created,\n            request_type: this.request_type,\n            url_state: this.url_state,\n        });\n    }\n\n    public static fromStorageString(storageString: string): Promise<State> {\n        Logger.createStatic(\"State\", \"fromStorageString\");\n        return Promise.resolve(new State(JSON.parse(storageString)));\n    }\n\n    public static async clearStaleState(storage: StateStore, age: number): Promise<void> {\n        const logger = Logger.createStatic(\"State\", \"clearStaleState\");\n        const cutoff = Timer.getEpochTime() - age;\n\n        const keys = await storage.getAllKeys();\n        logger.debug(\"got keys\", keys);\n\n        for (let i = 0; i < keys.length; i++) {\n            const key = keys[i];\n            const item = await storage.get(key);\n            let remove = false;\n\n            if (item) {\n                try {\n                    const state = await State.fromStorageString(item);\n\n                    logger.debug(\"got item from key:\", key, state.created);\n                    if (state.created <= cutoff) {\n                        remove = true;\n                    }\n                }\n                catch (err) {\n                    logger.error(\"Error parsing state for key:\", key, err);\n                    remove = true;\n                }\n            }\n            else {\n                logger.debug(\"no item in storage for key:\", key);\n                remove = true;\n            }\n\n            if (remove) {\n                logger.debug(\"removed item for key:\", key);\n                void storage.remove(key);\n            }\n        }\n    }\n}\n", "// Copyright (c) <PERSON> & <PERSON>. All rights reserved.\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\nimport { Logger, CryptoUtils } from \"./utils\";\nimport { State } from \"./State\";\n\n/** @public */\nexport interface SigninStateArgs {\n    id?: string;\n    data?: unknown;\n    created?: number;\n    request_type?: string;\n\n    code_verifier?: string;\n    code_challenge?: string;\n    authority: string;\n    client_id: string;\n    redirect_uri: string;\n    scope: string;\n    client_secret?: string;\n    extraTokenParams?: Record<string, unknown>;\n    response_mode?: \"query\" | \"fragment\";\n    skipUserInfo?: boolean;\n    url_state?: string;\n}\n\n/** @public */\nexport type SigninStateCreateArgs = Omit<SigninStateArgs, \"code_verifier\"> & {\n    code_verifier?: string | boolean;\n};\n\n/**\n * @public\n */\nexport class SigninState extends State {\n    // isCode\n    /** The same code_verifier that was used to obtain the authorization_code via PKCE. */\n    public readonly code_verifier: string | undefined;\n    /** Used to secure authorization code grants via Proof Key for Code Exchange (PKCE). */\n    public readonly code_challenge: string | undefined;\n\n    // to ensure state still matches settings\n    /** @see {@link OidcClientSettings.authority} */\n    public readonly authority: string;\n    /** @see {@link OidcClientSettings.client_id} */\n    public readonly client_id: string;\n    /** @see {@link OidcClientSettings.redirect_uri} */\n    public readonly redirect_uri: string;\n    /** @see {@link OidcClientSettings.scope} */\n    public readonly scope: string;\n    /** @see {@link OidcClientSettings.client_secret} */\n    public readonly client_secret: string | undefined;\n    /** @see {@link OidcClientSettings.extraTokenParams} */\n    public readonly extraTokenParams: Record<string, unknown> | undefined;\n    /** @see {@link OidcClientSettings.response_mode} */\n    public readonly response_mode: \"query\" | \"fragment\" | undefined;\n\n    public readonly skipUserInfo: boolean | undefined;\n\n    private constructor(args: SigninStateArgs) {\n        super(args);\n\n        this.code_verifier = args.code_verifier;\n        this.code_challenge = args.code_challenge;\n        this.authority = args.authority;\n        this.client_id = args.client_id;\n        this.redirect_uri = args.redirect_uri;\n        this.scope = args.scope;\n        this.client_secret = args.client_secret;\n        this.extraTokenParams = args.extraTokenParams;\n\n        this.response_mode = args.response_mode;\n        this.skipUserInfo = args.skipUserInfo;\n    }\n\n    public static async create(args: SigninStateCreateArgs): Promise<SigninState> {\n        const code_verifier = args.code_verifier === true ? CryptoUtils.generateCodeVerifier() : (args.code_verifier || undefined);\n        const code_challenge = code_verifier ? (await CryptoUtils.generateCodeChallenge(code_verifier)) : undefined;\n\n        return new SigninState({\n            ...args,\n            code_verifier,\n            code_challenge,\n        });\n    }\n\n    public toStorageString(): string {\n        new Logger(\"SigninState\").create(\"toStorageString\");\n        return JSON.stringify({\n            id: this.id,\n            data: this.data,\n            created: this.created,\n            request_type: this.request_type,\n            url_state: this.url_state,\n\n            code_verifier: this.code_verifier,\n            authority: this.authority,\n            client_id: this.client_id,\n            redirect_uri: this.redirect_uri,\n            scope: this.scope,\n            client_secret: this.client_secret,\n            extraTokenParams : this.extraTokenParams,\n            response_mode: this.response_mode,\n            skipUserInfo: this.skipUserInfo,\n        });\n    }\n\n    public static fromStorageString(storageString: string): Promise<SigninState> {\n        Logger.createStatic(\"SigninState\", \"fromStorageString\");\n        const data = JSON.parse(storageString);\n        return SigninState.create(data);\n    }\n}\n", "// Copyright (c) <PERSON> & <PERSON>. All rights reserved.\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\nimport { Logger, URL_STATE_DELIMITER } from \"./utils\";\nimport { SigninState } from \"./SigninState\";\n\n/**\n * @public\n * @see https://openid.net/specs/openid-connect-core-1_0.html#AuthRequest\n */\nexport interface SigninRequestCreateArgs {\n    // mandatory\n    url: string;\n    authority: string;\n    client_id: string;\n    redirect_uri: string;\n    response_type: string;\n    scope: string;\n\n    // optional\n    response_mode?: \"query\" | \"fragment\";\n    nonce?: string;\n    display?: string;\n    dpopJkt?: string;\n    prompt?: string;\n    max_age?: number;\n    ui_locales?: string;\n    id_token_hint?: string;\n    login_hint?: string;\n    acr_values?: string;\n\n    // other\n    resource?: string | string[];\n    request?: string;\n    request_uri?: string;\n    request_type?: string;\n    extraQueryParams?: Record<string, string | number | boolean>;\n\n    // special\n    extraTokenParams?: Record<string, unknown>;\n    client_secret?: string;\n    skipUserInfo?: boolean;\n    disablePKCE?: boolean;\n    /** custom \"state\", which can be used by a caller to have \"data\" round tripped */\n    state_data?: unknown;\n    url_state?: string;\n    omitScopeWhenRequesting?: boolean;\n}\n\n/**\n * @public\n */\nexport class SigninRequest {\n    private static readonly _logger = new Logger(\"SigninRequest\");\n\n    public readonly url: string;\n    public readonly state: SigninState;\n\n    private constructor(args: {\n        url: string;\n        state: SigninState;\n    }) {\n        this.url = args.url;\n        this.state = args.state;\n    }\n\n    public static async create({\n        // mandatory\n        url, authority, client_id, redirect_uri, response_type, scope,\n        // optional\n        state_data, response_mode, request_type, client_secret, nonce, url_state,\n        resource,\n        skipUserInfo,\n        extraQueryParams,\n        extraTokenParams,\n        disablePKCE,\n        dpopJkt,\n        omitScopeWhenRequesting,\n        ...optionalParams\n    }: SigninRequestCreateArgs): Promise<SigninRequest> {\n        if (!url) {\n            this._logger.error(\"create: No url passed\");\n            throw new Error(\"url\");\n        }\n        if (!client_id) {\n            this._logger.error(\"create: No client_id passed\");\n            throw new Error(\"client_id\");\n        }\n        if (!redirect_uri) {\n            this._logger.error(\"create: No redirect_uri passed\");\n            throw new Error(\"redirect_uri\");\n        }\n        if (!response_type) {\n            this._logger.error(\"create: No response_type passed\");\n            throw new Error(\"response_type\");\n        }\n        if (!scope) {\n            this._logger.error(\"create: No scope passed\");\n            throw new Error(\"scope\");\n        }\n        if (!authority) {\n            this._logger.error(\"create: No authority passed\");\n            throw new Error(\"authority\");\n        }\n\n        const state = await SigninState.create({\n            data: state_data,\n            request_type,\n            url_state,\n            code_verifier: !disablePKCE,\n            client_id, authority, redirect_uri,\n            response_mode,\n            client_secret, scope, extraTokenParams,\n            skipUserInfo,\n        });\n\n        const parsedUrl = new URL(url);\n        parsedUrl.searchParams.append(\"client_id\", client_id);\n        parsedUrl.searchParams.append(\"redirect_uri\", redirect_uri);\n        parsedUrl.searchParams.append(\"response_type\", response_type);\n        if (!omitScopeWhenRequesting) {\n            parsedUrl.searchParams.append(\"scope\", scope);\n        }\n        if (nonce) {\n            parsedUrl.searchParams.append(\"nonce\", nonce);\n        }\n\n        if (dpopJkt) {\n            parsedUrl.searchParams.append(\"dpop_jkt\", dpopJkt);\n        }\n\n        let stateParam = state.id;\n        if (url_state) {\n            stateParam = `${stateParam}${URL_STATE_DELIMITER}${url_state}`;\n        }\n        parsedUrl.searchParams.append(\"state\", stateParam);\n        if (state.code_challenge) {\n            parsedUrl.searchParams.append(\"code_challenge\", state.code_challenge);\n            parsedUrl.searchParams.append(\"code_challenge_method\", \"S256\");\n        }\n\n        if (resource) {\n            // https://datatracker.ietf.org/doc/html/rfc8707\n            const resources = Array.isArray(resource) ? resource : [resource];\n            resources\n                .forEach(r => parsedUrl.searchParams.append(\"resource\", r));\n        }\n\n        for (const [key, value] of Object.entries({ response_mode, ...optionalParams, ...extraQueryParams })) {\n            if (value != null) {\n                parsedUrl.searchParams.append(key, value.toString());\n            }\n        }\n\n        return new SigninRequest({\n            url: parsedUrl.href,\n            state,\n        });\n    }\n}\n", "// Copyright (c) <PERSON> & <PERSON>. All rights reserved.\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\nimport { Timer, URL_STATE_DELIMITER } from \"./utils\";\nimport type { UserProfile } from \"./User\";\n\nconst OidcScope = \"openid\";\n\n/**\n * @public\n * @see https://openid.net/specs/openid-connect-core-1_0.html#AuthResponse\n * @see https://openid.net/specs/openid-connect-core-1_0.html#AuthError\n */\nexport class SigninResponse {\n    // props present in the initial callback response regardless of success\n    public readonly state: string | null;\n    /** @see {@link User.session_state} */\n    public session_state: string | null;\n\n    // error props\n    /** @see {@link ErrorResponse.error} */\n    public readonly error: string | null;\n    /** @see {@link ErrorResponse.error_description} */\n    public readonly error_description: string | null;\n    /** @see {@link ErrorResponse.error_uri} */\n    public readonly error_uri: string | null;\n\n    // success props\n    public readonly code: string | null;\n\n    // props set after validation\n    /** @see {@link User.id_token} */\n    public id_token?: string;\n    /** @see {@link User.access_token} */\n    public access_token = \"\";\n    /** @see {@link User.token_type} */\n    public token_type = \"\";\n    /** @see {@link User.refresh_token} */\n    public refresh_token?: string;\n    /** @see {@link User.scope} */\n    public scope?: string;\n    /** @see {@link User.expires_at} */\n    public expires_at?: number;\n\n    /** custom state data set during the initial signin request */\n    public userState: unknown;\n    public url_state?: string;\n\n    /** @see {@link User.profile} */\n    public profile: UserProfile = {} as UserProfile;\n\n    public constructor(params: URLSearchParams) {\n        this.state = params.get(\"state\");\n        this.session_state = params.get(\"session_state\");\n        if (this.state) {\n            const splitState = decodeURIComponent(this.state).split(URL_STATE_DELIMITER);\n            this.state = splitState[0];\n            if (splitState.length > 1) {\n                this.url_state = splitState.slice(1).join(URL_STATE_DELIMITER);\n            }\n        }\n\n        this.error = params.get(\"error\");\n        this.error_description = params.get(\"error_description\");\n        this.error_uri = params.get(\"error_uri\");\n\n        this.code = params.get(\"code\");\n    }\n\n    public get expires_in(): number | undefined {\n        if (this.expires_at === undefined) {\n            return undefined;\n        }\n        return this.expires_at - Timer.getEpochTime();\n    }\n    public set expires_in(value: number | undefined) {\n        // spec expects a number, but normalize here just in case\n        if (typeof value === \"string\") value = Number(value);\n        if (value !== undefined && value >= 0) {\n            this.expires_at = Math.floor(value) + Timer.getEpochTime();\n        }\n    }\n\n    public get isOpenId(): boolean {\n        return this.scope?.split(\" \").includes(OidcScope) || !!this.id_token;\n    }\n}\n", "// Copyright (c) <PERSON> & <PERSON>. All rights reserved.\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\nimport { Logger, URL_STATE_DELIMITER } from \"./utils\";\nimport { State } from \"./State\";\n\n/**\n * @public\n * @see https://openid.net/specs/openid-connect-rpinitiated-1_0.html#RPLogout\n */\nexport interface SignoutRequestArgs {\n    // mandatory\n    url: string;\n\n    // optional\n    id_token_hint?: string;\n    client_id?: string;\n    post_logout_redirect_uri?: string;\n    extraQueryParams?: Record<string, string | number | boolean>;\n\n    // special\n    request_type?: string;\n    /** custom \"state\", which can be used by a caller to have \"data\" round tripped */\n    state_data?: unknown;\n    url_state?: string;\n}\n\n/**\n * @public\n */\nexport class SignoutRequest {\n    private readonly _logger = new Logger(\"SignoutRequest\");\n\n    public readonly url: string;\n    public readonly state?: State;\n\n    public constructor({\n        url,\n        state_data, id_token_hint, post_logout_redirect_uri, extraQueryParams, request_type, client_id, url_state,\n    }: SignoutRequestArgs) {\n        if (!url) {\n            this._logger.error(\"ctor: No url passed\");\n            throw new Error(\"url\");\n        }\n\n        const parsedUrl = new URL(url);\n        if (id_token_hint) {\n            parsedUrl.searchParams.append(\"id_token_hint\", id_token_hint);\n        }\n        if (client_id) {\n            parsedUrl.searchParams.append(\"client_id\", client_id);\n        }\n\n        if (post_logout_redirect_uri) {\n            parsedUrl.searchParams.append(\"post_logout_redirect_uri\", post_logout_redirect_uri);\n\n            // Add state if either data needs to be stored, or url_state set for an intermediate proxy\n            if (state_data || url_state) {\n                this.state = new State({ data: state_data, request_type, url_state });\n\n                let stateParam = this.state.id;\n                if (url_state) {\n                    stateParam = `${stateParam}${URL_STATE_DELIMITER}${url_state}`;\n                }\n                parsedUrl.searchParams.append(\"state\", stateParam);\n            }\n        }\n\n        for (const [key, value] of Object.entries({ ...extraQueryParams })) {\n            if (value != null) {\n                parsedUrl.searchParams.append(key, value.toString());\n            }\n        }\n\n        this.url = parsedUrl.href;\n    }\n}\n", "// Copyright (c) <PERSON> & <PERSON>. All rights reserved.\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\nimport { URL_STATE_DELIMITER } from \"./utils\";\n\n/**\n * @public\n * @see https://openid.net/specs/openid-connect-core-1_0.html#AuthError\n */\nexport class SignoutResponse {\n    public readonly state: string | null;\n\n    // error props\n    /** @see {@link ErrorResponse.error} */\n    public error: string | null;\n    /** @see {@link ErrorResponse.error_description} */\n    public error_description: string | null;\n    /** @see {@link ErrorResponse.error_uri} */\n    public error_uri: string | null;\n\n    /** custom state data set during the initial signin request */\n    public userState: unknown;\n    public url_state?: string;\n\n    public constructor(params: URLSearchParams) {\n        this.state = params.get(\"state\");\n        if (this.state) {\n            const splitState = decodeURIComponent(this.state).split(URL_STATE_DELIMITER);\n            this.state = splitState[0];\n            if (splitState.length > 1) {\n                this.url_state = splitState.slice(1).join(URL_STATE_DELIMITER);\n            }\n        }\n\n        this.error = params.get(\"error\");\n        this.error_description = params.get(\"error_description\");\n        this.error_uri = params.get(\"error_uri\");\n    }\n}\n", "// Copyright (c) <PERSON> & <PERSON>. All rights reserved.\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\nimport type { JwtClaims } from \"./Claims\";\nimport type { OidcClientSettingsStore } from \"./OidcClientSettings\";\nimport type { UserProfile } from \"./User\";\nimport { Logger } from \"./utils\";\n\n/**\n * Protocol claims that could be removed by default from profile.\n * Derived from the following sets of claims:\n * - {@link https://datatracker.ietf.org/doc/html/rfc7519.html#section-4.1}\n * - {@link https://openid.net/specs/openid-connect-core-1_0.html#IDToken}\n * - {@link https://openid.net/specs/openid-connect-core-1_0.html#CodeIDToken}\n *\n * @internal\n */\nconst DefaultProtocolClaims = [\n    \"nbf\",\n    \"jti\",\n    \"auth_time\",\n    \"nonce\",\n    \"acr\",\n    \"amr\",\n    \"azp\",\n    \"at_hash\", // https://openid.net/specs/openid-connect-core-1_0.html#CodeIDToken\n] as const;\n\n/**\n * Protocol claims that should never be removed from profile.\n * \"sub\" is needed internally and others should remain required as per the OIDC specs.\n *\n * @internal\n */\nconst InternalRequiredProtocolClaims = [\"sub\", \"iss\", \"aud\", \"exp\", \"iat\"];\n\n/**\n * @internal\n */\nexport class ClaimsService {\n    protected readonly _logger = new Logger(\"ClaimsService\");\n    public constructor(\n        protected readonly _settings: OidcClientSettingsStore,\n    ) {}\n\n    public filterProtocolClaims(claims: UserProfile): UserProfile {\n        const result = { ...claims };\n\n        if (this._settings.filterProtocolClaims) {\n            let protocolClaims;\n            if (Array.isArray(this._settings.filterProtocolClaims)) {\n                protocolClaims = this._settings.filterProtocolClaims;\n            } else {\n                protocolClaims = DefaultProtocolClaims;\n            }\n\n            for (const claim of protocolClaims) {\n                if (!InternalRequiredProtocolClaims.includes(claim)) {\n                    delete result[claim];\n                }\n            }\n        }\n\n        return result;\n    }\n\n    public mergeClaims(claims1: JwtClaims, claims2: JwtClaims): UserProfile;\n    public mergeClaims(claims1: UserProfile, claims2: JwtClaims): UserProfile {\n        const result = { ...claims1 };\n        for (const [claim, values] of Object.entries(claims2)) {\n            if (result[claim] !== values) {\n                if (Array.isArray(result[claim]) || Array.isArray(values)) {\n                    if (this._settings.mergeClaimsStrategy.array == \"replace\") {\n                        result[claim] = values;\n                    } else {\n                        const mergedValues = Array.isArray(result[claim]) ? result[claim] as unknown[] : [result[claim]];\n                        for (const value of Array.isArray(values) ? values : [values]) {\n                            if (!mergedValues.includes(value)) {\n                                mergedValues.push(value);\n                            }\n                        }\n                        result[claim] = mergedValues;\n                    }\n                } else if (typeof result[claim] === \"object\" && typeof values === \"object\") {\n                    result[claim] = this.mergeClaims(result[claim] as JwtClaims, values as JwtClaims);\n                } else {\n                    result[claim] = values;\n                }\n            }\n        }\n\n        return result;\n    }\n}\n", "/**\n * @public\n */\nexport interface DPoPStore {\n    set(key: string, value: DPoPState): Promise<void>;\n    get(key: string): Promise<DPoPState>;\n    remove(key: string): Promise<DPoPState>;\n    getAllKeys(): Promise<string[]>;\n}\n\n/**\n * @public\n */\nexport class DPoPState {\n    public constructor(\n        public readonly keys: CryptoKeyPair,\n        public nonce?: string,\n    ) { }\n}\n", "// Copyright (c) <PERSON> & Dominic<PERSON>. All rights reserved.\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\nimport { CryptoUtils, Logger, UrlUtils } from \"./utils\";\nimport { ErrorResponse } from \"./errors\";\nimport { type ExtraHeader, type OidcClientSettings, OidcClientSettingsStore } from \"./OidcClientSettings\";\nimport { ResponseValidator } from \"./ResponseValidator\";\nimport { MetadataService } from \"./MetadataService\";\nimport type { RefreshState } from \"./RefreshState\";\nimport { SigninRequest, type SigninRequestCreateArgs } from \"./SigninRequest\";\nimport { SigninResponse } from \"./SigninResponse\";\nimport { SignoutRequest, type SignoutRequestArgs } from \"./SignoutRequest\";\nimport { SignoutResponse } from \"./SignoutResponse\";\nimport { SigninState } from \"./SigninState\";\nimport { State } from \"./State\";\nimport { TokenClient } from \"./TokenClient\";\nimport { ClaimsService } from \"./ClaimsService\";\nimport { DPoPState, type DPoPStore } from \"./DPoPStore\";\nimport { ErrorDPoPNonce } from \"./errors/ErrorDPoPNonce\";\n\n/**\n * @public\n */\nexport interface CreateSigninRequestArgs\n    extends Omit<SigninRequestCreateArgs, \"url\" | \"authority\" | \"client_id\" | \"redirect_uri\" | \"response_type\" | \"scope\" | \"state_data\"> {\n    redirect_uri?: string;\n    response_type?: string;\n    scope?: string;\n    dpopJkt?: string;\n\n    /** custom \"state\", which can be used by a caller to have \"data\" round tripped */\n    state?: unknown;\n}\n\n/**\n * @public\n */\nexport interface UseRefreshTokenArgs {\n    redirect_uri?: string;\n    resource?: string | string[];\n    extraTokenParams?: Record<string, unknown>;\n    timeoutInSeconds?: number;\n\n    state: RefreshState;\n\n    extraHeaders?: Record<string, ExtraHeader>;\n}\n\n/**\n * @public\n */\nexport type CreateSignoutRequestArgs = Omit<SignoutRequestArgs, \"url\" | \"state_data\"> & {\n    /** custom \"state\", which can be used by a caller to have \"data\" round tripped */\n    state?: unknown;\n};\n\n/**\n * @public\n */\nexport type ProcessResourceOwnerPasswordCredentialsArgs = {\n    username: string;\n    password: string;\n    skipUserInfo?: boolean;\n    extraTokenParams?: Record<string, unknown>;\n};\n\n/**\n * Provides the raw OIDC/OAuth2 protocol support for the authorization endpoint and the end session endpoint in the\n * authorization server. It provides a bare-bones protocol implementation and is used by the UserManager class.\n * Only use this class if you simply want protocol support without the additional management features of the\n * UserManager class.\n *\n * @public\n */\nexport class OidcClient {\n    public readonly settings: OidcClientSettingsStore;\n    protected readonly _logger = new Logger(\"OidcClient\");\n\n    public readonly metadataService: MetadataService;\n    protected readonly _claimsService: ClaimsService;\n    protected readonly _validator: ResponseValidator;\n    protected readonly _tokenClient: TokenClient;\n\n    public constructor(settings: OidcClientSettings);\n    public constructor(settings: OidcClientSettingsStore, metadataService: MetadataService);\n    public constructor(settings: OidcClientSettings | OidcClientSettingsStore, metadataService?: MetadataService) {\n        this.settings = settings instanceof OidcClientSettingsStore ? settings : new OidcClientSettingsStore(settings);\n\n        this.metadataService = metadataService ?? new MetadataService(this.settings);\n        this._claimsService = new ClaimsService(this.settings);\n        this._validator = new ResponseValidator(this.settings, this.metadataService, this._claimsService);\n        this._tokenClient = new TokenClient(this.settings, this.metadataService);\n    }\n\n    public async createSigninRequest({\n        state,\n        request,\n        request_uri,\n        request_type,\n        id_token_hint,\n        login_hint,\n        skipUserInfo,\n        nonce,\n        url_state,\n        response_type = this.settings.response_type,\n        scope = this.settings.scope,\n        redirect_uri = this.settings.redirect_uri,\n        prompt = this.settings.prompt,\n        display = this.settings.display,\n        max_age = this.settings.max_age,\n        ui_locales = this.settings.ui_locales,\n        acr_values = this.settings.acr_values,\n        resource = this.settings.resource,\n        response_mode = this.settings.response_mode,\n        extraQueryParams = this.settings.extraQueryParams,\n        extraTokenParams = this.settings.extraTokenParams,\n        dpopJkt,\n        omitScopeWhenRequesting = this.settings.omitScopeWhenRequesting,\n    }: CreateSigninRequestArgs): Promise<SigninRequest> {\n        const logger = this._logger.create(\"createSigninRequest\");\n\n        if (response_type !== \"code\") {\n            throw new Error(\"Only the Authorization Code flow (with PKCE) is supported\");\n        }\n\n        const url = await this.metadataService.getAuthorizationEndpoint();\n        logger.debug(\"Received authorization endpoint\", url);\n\n        const signinRequest = await SigninRequest.create({\n            url,\n            authority: this.settings.authority,\n            client_id: this.settings.client_id,\n            redirect_uri,\n            response_type,\n            scope,\n            state_data: state,\n            url_state,\n            prompt, display, max_age, ui_locales, id_token_hint, login_hint, acr_values, dpopJkt,\n            resource, request, request_uri, extraQueryParams, extraTokenParams, request_type, response_mode,\n            client_secret: this.settings.client_secret,\n            skipUserInfo,\n            nonce,\n            disablePKCE: this.settings.disablePKCE,\n            omitScopeWhenRequesting,\n        });\n\n        // house cleaning\n        await this.clearStaleState();\n\n        const signinState = signinRequest.state;\n        await this.settings.stateStore.set(signinState.id, signinState.toStorageString());\n        return signinRequest;\n    }\n\n    public async readSigninResponseState(url: string, removeState = false): Promise<{ state: SigninState; response: SigninResponse }> {\n        const logger = this._logger.create(\"readSigninResponseState\");\n\n        const response = new SigninResponse(UrlUtils.readParams(url, this.settings.response_mode));\n        if (!response.state) {\n            logger.throw(new Error(\"No state in response\"));\n            // need to throw within this function's body for type narrowing to work\n            // eslint-disable-next-line @typescript-eslint/only-throw-error\n            throw null; // https://github.com/microsoft/TypeScript/issues/46972\n        }\n\n        const storedStateString = await this.settings.stateStore[removeState ? \"remove\" : \"get\"](response.state);\n        if (!storedStateString) {\n            logger.throw(new Error(\"No matching state found in storage\"));\n            // eslint-disable-next-line @typescript-eslint/only-throw-error\n            throw null; // https://github.com/microsoft/TypeScript/issues/46972\n        }\n\n        const state = await SigninState.fromStorageString(storedStateString);\n        return { state, response };\n    }\n\n    public async processSigninResponse(url: string, extraHeaders?: Record<string, ExtraHeader>, removeState = true): Promise<SigninResponse> {\n        const logger = this._logger.create(\"processSigninResponse\");\n\n        const { state, response } = await this.readSigninResponseState(url, removeState);\n        logger.debug(\"received state from storage; validating response\");\n\n        if (this.settings.dpop && this.settings.dpop.store) {\n            const dpopProof = await this.getDpopProof(this.settings.dpop.store);\n            extraHeaders = { ...extraHeaders, \"DPoP\": dpopProof };\n        }\n\n        /**\n         * The DPoP spec describes a method for Authorization Servers to supply a nonce value\n         * in order to limit the lifetime of a given DPoP proof.\n         * See https://datatracker.ietf.org/doc/html/rfc9449#name-authorization-server-provid\n         * This involves the AS returning a 400 bad request with a DPoP-Nonce header containing\n         * the nonce value. The client must then retry the request with a recomputed DPoP proof\n         * containing the supplied nonce value.\n         */\n        try {\n            await this._validator.validateSigninResponse(response, state, extraHeaders);\n        }\n        catch (err) {\n            if (err instanceof ErrorDPoPNonce && this.settings.dpop) {\n                const dpopProof = await this.getDpopProof(this.settings.dpop.store, err.nonce);\n                extraHeaders![\"DPoP\"] = dpopProof;\n                await this._validator.validateSigninResponse(response, state, extraHeaders);\n            } else {\n                throw err;\n            }\n        }\n\n        return response;\n    }\n\n    async getDpopProof(dpopStore: DPoPStore, nonce?: string): Promise<string> {\n        let keyPair: CryptoKeyPair;\n        let dpopState: DPoPState;\n\n        if (!(await dpopStore.getAllKeys()).includes(this.settings.client_id)) {\n            keyPair = await CryptoUtils.generateDPoPKeys();\n            dpopState = new DPoPState(keyPair, nonce);\n            await dpopStore.set(this.settings.client_id, dpopState);\n        } else {\n            dpopState = await dpopStore.get(this.settings.client_id);\n\n            // if the server supplied nonce has changed since the last request, update the nonce\n            if (dpopState.nonce !== nonce && nonce) {\n                dpopState.nonce = nonce;\n                await dpopStore.set(this.settings.client_id, dpopState);\n            }\n        }\n\n        return await CryptoUtils.generateDPoPProof({\n            url: await this.metadataService.getTokenEndpoint(false),\n            httpMethod: \"POST\",\n            keyPair: dpopState.keys,\n            nonce: dpopState.nonce,\n        });\n    }\n\n    public async processResourceOwnerPasswordCredentials({\n        username,\n        password,\n        skipUserInfo = false,\n        extraTokenParams = {},\n    }: ProcessResourceOwnerPasswordCredentialsArgs): Promise<SigninResponse> {\n        const tokenResponse: Record<string, unknown> = await this._tokenClient.exchangeCredentials({ username, password, ...extraTokenParams });\n        const signinResponse: SigninResponse = new SigninResponse(new URLSearchParams());\n        Object.assign(signinResponse, tokenResponse);\n        await this._validator.validateCredentialsResponse(signinResponse, skipUserInfo);\n        return signinResponse;\n    }\n\n    public async useRefreshToken({\n        state,\n        redirect_uri,\n        resource,\n        timeoutInSeconds,\n        extraHeaders,\n        extraTokenParams,\n    }: UseRefreshTokenArgs): Promise<SigninResponse> {\n        const logger = this._logger.create(\"useRefreshToken\");\n\n        // https://github.com/authts/oidc-client-ts/issues/695\n        // In some cases (e.g. AzureAD), not all granted scopes are allowed on token refresh requests.\n        // Therefore, we filter all granted scopes by a list of allowable scopes.\n        let scope;\n        if (this.settings.refreshTokenAllowedScope === undefined) {\n            scope = state.scope;\n        } else {\n            const allowableScopes = this.settings.refreshTokenAllowedScope.split(\" \");\n            const providedScopes = state.scope?.split(\" \") || [];\n\n            scope = providedScopes.filter(s => allowableScopes.includes(s)).join(\" \");\n        }\n\n        if (this.settings.dpop && this.settings.dpop.store) {\n            const dpopProof = await this.getDpopProof(this.settings.dpop.store);\n            extraHeaders = { ...extraHeaders, \"DPoP\": dpopProof };\n        }\n\n        /**\n         * The DPoP spec describes a method for Authorization Servers to supply a nonce value\n         * in order to limit the lifetime of a given DPoP proof.\n         * See https://datatracker.ietf.org/doc/html/rfc9449#name-authorization-server-provid\n         * This involves the AS returning a 400 bad request with a DPoP-Nonce header containing\n         * the nonce value. The client must then retry the request with a recomputed DPoP proof\n         * containing the supplied nonce value.\n         */\n        let result;\n        try {\n            result = await this._tokenClient.exchangeRefreshToken({\n                refresh_token: state.refresh_token,\n                // provide the (possible filtered) scope list\n                scope,\n                redirect_uri,\n                resource,\n                timeoutInSeconds,\n                extraHeaders,\n                ...extraTokenParams,\n            });\n        } catch (err) {\n            if (err instanceof ErrorDPoPNonce && this.settings.dpop) {\n                extraHeaders![\"DPoP\"] = await this.getDpopProof(this.settings.dpop.store, err.nonce);\n                result = await this._tokenClient.exchangeRefreshToken({\n                    refresh_token: state.refresh_token,\n                    // provide the (possible filtered) scope list\n                    scope,\n                    redirect_uri,\n                    resource,\n                    timeoutInSeconds,\n                    extraHeaders,\n                    ...extraTokenParams,\n                });\n            } else {\n                throw err;\n            }\n        }\n\n        const response = new SigninResponse(new URLSearchParams());\n        Object.assign(response, result);\n        logger.debug(\"validating response\", response);\n        await this._validator.validateRefreshResponse(response, {\n            ...state,\n            // override the scope in the state handed over to the validator\n            // so it can set the granted scope to the requested scope in case none is included in the response\n            scope,\n        });\n        return response;\n    }\n\n    public async createSignoutRequest({\n        state,\n        id_token_hint,\n        client_id,\n        request_type,\n        url_state,\n        post_logout_redirect_uri = this.settings.post_logout_redirect_uri,\n        extraQueryParams = this.settings.extraQueryParams,\n    }: CreateSignoutRequestArgs = {}): Promise<SignoutRequest> {\n        const logger = this._logger.create(\"createSignoutRequest\");\n\n        const url = await this.metadataService.getEndSessionEndpoint();\n        if (!url) {\n            logger.throw(new Error(\"No end session endpoint\"));\n            // eslint-disable-next-line @typescript-eslint/only-throw-error\n            throw null; // https://github.com/microsoft/TypeScript/issues/46972\n        }\n\n        logger.debug(\"Received end session endpoint\", url);\n\n        // specify the client identifier when post_logout_redirect_uri is used but id_token_hint is not\n        if (!client_id && post_logout_redirect_uri && !id_token_hint) {\n            client_id = this.settings.client_id;\n        }\n\n        const request = new SignoutRequest({\n            url,\n            id_token_hint,\n            client_id,\n            post_logout_redirect_uri,\n            state_data: state,\n            extraQueryParams,\n            request_type,\n            url_state,\n        });\n\n        // house cleaning\n        await this.clearStaleState();\n\n        const signoutState = request.state;\n        if (signoutState) {\n            logger.debug(\"Signout request has state to persist\");\n            await this.settings.stateStore.set(signoutState.id, signoutState.toStorageString());\n        }\n\n        return request;\n    }\n\n    public async readSignoutResponseState(url: string, removeState = false): Promise<{ state: State | undefined; response: SignoutResponse }> {\n        const logger = this._logger.create(\"readSignoutResponseState\");\n\n        const response = new SignoutResponse(UrlUtils.readParams(url, this.settings.response_mode));\n        if (!response.state) {\n            logger.debug(\"No state in response\");\n\n            if (response.error) {\n                logger.warn(\"Response was error:\", response.error);\n                throw new ErrorResponse(response);\n            }\n\n            return { state: undefined, response };\n        }\n\n        const storedStateString = await this.settings.stateStore[removeState ? \"remove\" : \"get\"](response.state);\n        if (!storedStateString) {\n            logger.throw(new Error(\"No matching state found in storage\"));\n            // eslint-disable-next-line @typescript-eslint/only-throw-error\n            throw null; // https://github.com/microsoft/TypeScript/issues/46972\n        }\n\n        const state = await State.fromStorageString(storedStateString);\n        return { state, response };\n    }\n\n    public async processSignoutResponse(url: string): Promise<SignoutResponse> {\n        const logger = this._logger.create(\"processSignoutResponse\");\n\n        const { state, response } = await this.readSignoutResponseState(url, true);\n        if (state) {\n            logger.debug(\"Received state from storage; validating response\");\n            this._validator.validateSignoutResponse(response, state);\n        } else {\n            logger.debug(\"No state from storage; skipping response validation\");\n        }\n\n        return response;\n    }\n\n    public clearStaleState(): Promise<void> {\n        this._logger.create(\"clearStaleState\");\n        return State.clearStaleState(this.settings.stateStore, this.settings.staleStateAgeInSeconds);\n    }\n\n    public async revokeToken(token: string, type?: \"access_token\" | \"refresh_token\"): Promise<void> {\n        this._logger.create(\"revokeToken\");\n        return await this._tokenClient.revoke({\n            token,\n            token_type_hint: type,\n        });\n    }\n}\n", "// Copyright (c) <PERSON> & <PERSON>. All rights reserved.\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\nimport { Logger } from \"./utils\";\nimport { CheckSessionIFrame } from \"./CheckSessionIFrame\";\nimport type { UserManager } from \"./UserManager\";\nimport type { User } from \"./User\";\n\n/**\n * @public\n */\nexport class SessionMonitor {\n    private readonly _logger = new Logger(\"SessionMonitor\");\n\n    private _sub: string | undefined;\n    private _checkSessionIFrame?: CheckSessionIFrame;\n\n    public constructor(private readonly _userManager: UserManager) {\n        if (!_userManager) {\n            this._logger.throw(new Error(\"No user manager passed\"));\n        }\n\n        this._userManager.events.addUserLoaded(this._start);\n        this._userManager.events.addUserUnloaded(this._stop);\n\n        this._init().catch((err: unknown) => {\n            // catch to suppress errors since we're in a ctor\n            this._logger.error(err);\n        });\n    }\n\n    protected async _init(): Promise<void> {\n        this._logger.create(\"_init\");\n        const user = await this._userManager.getUser();\n        // doing this manually here since calling getUser\n        // doesn't trigger load event.\n        if (user) {\n            void this._start(user);\n        }\n        else if (this._userManager.settings.monitorAnonymousSession) {\n            const session = await this._userManager.querySessionStatus();\n            if (session) {\n                const tmpUser = {\n                    session_state: session.session_state,\n                    profile: session.sub ? {\n                        sub: session.sub,\n                    } : null,\n                };\n                void this._start(tmpUser);\n            }\n        }\n    }\n\n    protected _start = async (\n        user: User | {\n            session_state: string;\n            profile: { sub: string } | null;\n        },\n    ): Promise<void> => {\n        const session_state = user.session_state;\n        if (!session_state) {\n            return;\n        }\n        const logger = this._logger.create(\"_start\");\n\n        if (user.profile) {\n            this._sub = user.profile.sub;\n            logger.debug(\"session_state\", session_state, \", sub\", this._sub);\n        }\n        else {\n            this._sub = undefined;\n            logger.debug(\"session_state\", session_state, \", anonymous user\");\n        }\n\n        if (this._checkSessionIFrame) {\n            this._checkSessionIFrame.start(session_state);\n            return;\n        }\n\n        try {\n            const url = await this._userManager.metadataService.getCheckSessionIframe();\n            if (url) {\n                logger.debug(\"initializing check session iframe\");\n\n                const client_id = this._userManager.settings.client_id;\n                const intervalInSeconds = this._userManager.settings.checkSessionIntervalInSeconds;\n                const stopOnError = this._userManager.settings.stopCheckSessionOnError;\n\n                const checkSessionIFrame = new CheckSessionIFrame(this._callback, client_id, url, intervalInSeconds, stopOnError);\n                await checkSessionIFrame.load();\n                this._checkSessionIFrame = checkSessionIFrame;\n                checkSessionIFrame.start(session_state);\n            }\n            else {\n                logger.warn(\"no check session iframe found in the metadata\");\n            }\n        }\n        catch (err) {\n            // catch to suppress errors since we're in non-promise callback\n            logger.error(\"Error from getCheckSessionIframe:\", err instanceof Error ? err.message : err);\n        }\n    };\n\n    protected _stop = (): void => {\n        const logger = this._logger.create(\"_stop\");\n        this._sub = undefined;\n\n        if (this._checkSessionIFrame) {\n            this._checkSessionIFrame.stop();\n        }\n\n        if (this._userManager.settings.monitorAnonymousSession) {\n            // using a timer to delay re-initialization to avoid race conditions during signout\n            // TODO rewrite to use promise correctly\n            // eslint-disable-next-line @typescript-eslint/no-misused-promises\n            const timerHandle = setInterval(async () => {\n                clearInterval(timerHandle);\n\n                try {\n                    const session = await this._userManager.querySessionStatus();\n                    if (session) {\n                        const tmpUser = {\n                            session_state: session.session_state,\n                            profile: session.sub ? {\n                                sub: session.sub,\n                            } : null,\n                        };\n                        void this._start(tmpUser);\n                    }\n                }\n                catch (err) {\n                    // catch to suppress errors since we're in a callback\n                    logger.error(\"error from querySessionStatus\", err instanceof Error ? err.message : err);\n                }\n            }, 1000);\n        }\n    };\n\n    protected _callback = async (): Promise<void> => {\n        const logger = this._logger.create(\"_callback\");\n        try {\n            const session = await this._userManager.querySessionStatus();\n            let raiseEvent = true;\n\n            if (session && this._checkSessionIFrame) {\n                if (session.sub === this._sub) {\n                    raiseEvent = false;\n                    this._checkSessionIFrame.start(session.session_state);\n\n                    logger.debug(\"same sub still logged in at OP, session state has changed, restarting check session iframe; session_state\", session.session_state);\n                    await this._userManager.events._raiseUserSessionChanged();\n                }\n                else {\n                    logger.debug(\"different subject signed into OP\", session.sub);\n                }\n            }\n            else {\n                logger.debug(\"subject no longer signed into OP\");\n            }\n\n            if (raiseEvent) {\n                if (this._sub) {\n                    await this._userManager.events._raiseUserSignedOut();\n                }\n                else {\n                    await this._userManager.events._raiseUserSignedIn();\n                }\n            } else {\n                logger.debug(\"no change in session detected, no event to raise\");\n            }\n        }\n        catch (err) {\n            if (this._sub) {\n                logger.debug(\"Error calling queryCurrentSigninSession; raising signed out event\", err);\n                await this._userManager.events._raiseUserSignedOut();\n            }\n        }\n    };\n}\n", "// Copyright (c) <PERSON> & <PERSON>. All rights reserved.\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\nimport { Logger, Timer } from \"./utils\";\nimport type { IdTokenClaims } from \"./Claims\";\n\n/**\n * Holds claims represented by a combination of the `id_token` and the user info endpoint.\n *\n * @public\n */\nexport type UserProfile = IdTokenClaims;\n\n/**\n * @public\n */\nexport class User {\n    /**\n     * A JSON Web Token (JWT). Only provided if `openid` scope was requested.\n     * The application can access the data decoded by using the `profile` property.\n     */\n    public id_token?: string;\n\n    /** The session state value returned from the OIDC provider. */\n    public session_state: string | null;\n\n    /**\n     * The requested access token returned from the OIDC provider. The application can use this token to\n     * authenticate itself to the secured resource.\n     */\n    public access_token: string;\n\n    /**\n     * An OAuth 2.0 refresh token. The app can use this token to acquire additional access tokens after the\n     * current access token expires. Refresh tokens are long-lived and can be used to maintain access to resources\n     * for extended periods of time.\n     */\n    public refresh_token?: string;\n\n    /** Typically \"Bearer\" */\n    public token_type: string;\n\n    /** The scopes that the requested access token is valid for. */\n    public scope?: string;\n\n    /** The claims represented by a combination of the `id_token` and the user info endpoint. */\n    public profile: UserProfile;\n\n    /** The expires at returned from the OIDC provider. */\n    public expires_at?: number;\n\n    /** custom state data set during the initial signin request */\n    public readonly state: unknown;\n    public readonly url_state?: string;\n\n    public constructor(args: {\n        id_token?: string;\n        session_state?: string | null;\n        access_token: string;\n        refresh_token?: string;\n        token_type: string;\n        scope?: string;\n        profile: UserProfile;\n        expires_at?: number;\n        userState?: unknown;\n        url_state?: string;\n    }) {\n        this.id_token = args.id_token;\n        this.session_state = args.session_state ?? null;\n        this.access_token = args.access_token;\n        this.refresh_token = args.refresh_token;\n\n        this.token_type = args.token_type;\n        this.scope = args.scope;\n        this.profile = args.profile;\n        this.expires_at = args.expires_at;\n        this.state = args.userState;\n        this.url_state = args.url_state;\n    }\n\n    /** Computed number of seconds the access token has remaining. */\n    public get expires_in(): number | undefined {\n        if (this.expires_at === undefined) {\n            return undefined;\n        }\n        return this.expires_at - Timer.getEpochTime();\n    }\n\n    public set expires_in(value: number | undefined) {\n        if (value !== undefined) {\n            this.expires_at = Math.floor(value) + Timer.getEpochTime();\n        }\n    }\n\n    /** Computed value indicating if the access token is expired. */\n    public get expired(): boolean | undefined {\n        const expires_in = this.expires_in;\n        if (expires_in === undefined) {\n            return undefined;\n        }\n        return expires_in <= 0;\n    }\n\n    /** Array representing the parsed values from the `scope`. */\n    public get scopes(): string[] {\n        return this.scope?.split(\" \") ?? [];\n    }\n\n    public toStorageString(): string {\n        new Logger(\"User\").create(\"toStorageString\");\n        return JSON.stringify({\n            id_token: this.id_token,\n            session_state: this.session_state,\n            access_token: this.access_token,\n            refresh_token: this.refresh_token,\n            token_type: this.token_type,\n            scope: this.scope,\n            profile: this.profile,\n            expires_at: this.expires_at,\n        });\n    }\n\n    public static fromStorageString(storageString: string): User {\n        Logger.createStatic(\"User\", \"fromStorageString\");\n        return new User(JSON.parse(storageString));\n    }\n}\n", "// Copyright (c) <PERSON> & <PERSON>. All rights reserved.\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\nimport { Event, Logger, UrlUtils } from \"../utils\";\nimport type { IWindow, NavigateParams, NavigateResponse } from \"./IWindow\";\n\nconst messageSource = \"oidc-client\";\n\ninterface MessageData {\n    source: string;\n    url: string;\n    keepOpen: boolean;\n}\n\n/**\n * Window implementation which resolves via communication from a child window\n * via the `Window.postMessage()` interface.\n *\n * @internal\n */\nexport abstract class AbstractChildWindow implements IWindow {\n    protected abstract readonly _logger: Logger;\n    protected readonly _abort = new Event<[reason: Error]>(\"Window navigation aborted\");\n    protected readonly _disposeHandlers = new Set<() => void>();\n\n    protected _window: WindowProxy | null = null;\n\n    public async navigate(params: NavigateParams): Promise<NavigateResponse> {\n        const logger = this._logger.create(\"navigate\");\n        if (!this._window) {\n            throw new Error(\"Attempted to navigate on a disposed window\");\n        }\n\n        logger.debug(\"setting URL in window\");\n        this._window.location.replace(params.url);\n\n        const { url, keepOpen } = await new Promise<MessageData>((resolve, reject) => {\n            const listener = (e: MessageEvent) => {\n                const data: MessageData | undefined = e.data;\n                const origin = params.scriptOrigin ?? window.location.origin;\n                if (e.origin !== origin || data?.source !== messageSource) {\n                    // silently discard events not intended for us\n                    return;\n                }\n                try {\n                    const state = UrlUtils.readParams(data.url, params.response_mode).get(\"state\");\n                    if (!state) {\n                        logger.warn(\"no state found in response url\");\n                    }\n                    if (e.source !== this._window && state !== params.state) {\n                        // MessageEvent source is a relatively modern feature, we can't rely on it\n                        // so we also inspect the payload for a matching state key as an alternative\n                        return;\n                    }\n                }\n                catch {\n                    this._dispose();\n                    reject(new Error(\"Invalid response from window\"));\n                }\n                resolve(data);\n            };\n            window.addEventListener(\"message\", listener, false);\n            this._disposeHandlers.add(() => window.removeEventListener(\"message\", listener, false));\n            this._disposeHandlers.add(this._abort.addHandler((reason) => {\n                this._dispose();\n                reject(reason);\n            }));\n        });\n        logger.debug(\"got response from window\");\n        this._dispose();\n\n        if (!keepOpen) {\n            this.close();\n        }\n\n        return { url };\n    }\n\n    public abstract close(): void;\n\n    private _dispose(): void {\n        this._logger.create(\"_dispose\");\n\n        for (const dispose of this._disposeHandlers) {\n            dispose();\n        }\n        this._disposeHandlers.clear();\n    }\n\n    protected static _notifyParent(parent: Window, url: string, keepOpen = false, targetOrigin = window.location.origin): void {\n        parent.postMessage({\n            source: messageSource,\n            url,\n            keepOpen,\n        } as MessageData, targetOrigin);\n    }\n}\n", "// Copyright (c) <PERSON> & <PERSON>. All rights reserved.\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\nimport { type OidcClientSettings, OidcClientSettingsStore } from \"./OidcClientSettings\";\nimport type { PopupWindowFeatures } from \"./utils/PopupUtils\";\nimport { WebStorageStateStore } from \"./WebStorageStateStore\";\nimport { InMemoryWebStorage } from \"./InMemoryWebStorage\";\n\nexport const DefaultPopupWindowFeatures: PopupWindowFeatures = {\n    location: false,\n    toolbar: false,\n    height: 640,\n    closePopupWindowAfterInSeconds: -1,\n};\nexport const DefaultPopupTarget = \"_blank\";\nconst DefaultAccessTokenExpiringNotificationTimeInSeconds = 60;\nconst DefaultCheckSessionIntervalInSeconds = 2;\nexport const DefaultSilentRequestTimeoutInSeconds = 10;\n\n/**\n * The settings used to configure the {@link UserManager}.\n *\n * @public\n */\nexport interface UserManagerSettings extends OidcClientSettings {\n    /** The URL for the page containing the call to signinPopupCallback to handle the callback from the OIDC/OAuth2 */\n    popup_redirect_uri?: string;\n    popup_post_logout_redirect_uri?: string;\n    /**\n     * The features parameter to window.open for the popup signin window. By default, the popup is\n     * placed centered in front of the window opener.\n     * (default: \\{ location: false, menubar: false, height: 640, closePopupWindowAfterInSeconds: -1 \\})\n     */\n    popupWindowFeatures?: PopupWindowFeatures;\n    /** The target parameter to window.open for the popup signin window (default: \"_blank\") */\n    popupWindowTarget?: string;\n    /** The methods window.location method used to redirect (default: \"assign\") */\n    redirectMethod?: \"replace\" | \"assign\";\n    /** The methods target window being redirected (default: \"self\") */\n    redirectTarget?: \"top\" | \"self\";\n\n    /** The target to pass while calling postMessage inside iframe for callback (default: window.location.origin) */\n    iframeNotifyParentOrigin?: string;\n\n    /** The script origin to check during 'message' callback execution while performing silent auth via iframe (default: window.location.origin) */\n    iframeScriptOrigin?: string;\n\n    /** The URL for the page containing the code handling the silent renew */\n    silent_redirect_uri?: string;\n    /** Number of seconds to wait for the silent renew to return before assuming it has failed or timed out (default: 10) */\n    silentRequestTimeoutInSeconds?: number;\n    /** Flag to indicate if there should be an automatic attempt to renew the access token prior to its expiration. The automatic renew attempt starts 1 minute before the access token expires (default: true) */\n    automaticSilentRenew?: boolean;\n    /** Flag to validate user.profile.sub in silent renew calls (default: true) */\n    validateSubOnSilentRenew?: boolean;\n    /** Flag to control if id_token is included as id_token_hint in silent renew calls (default: false) */\n    includeIdTokenInSilentRenew?: boolean;\n\n    /** Will raise events for when user has performed a signout at the OP (default: false) */\n    monitorSession?: boolean;\n    monitorAnonymousSession?: boolean;\n    /** Interval in seconds to check the user's session (default: 2) */\n    checkSessionIntervalInSeconds?: number;\n    query_status_response_type?: string;\n    stopCheckSessionOnError?: boolean;\n\n    /**\n     * The `token_type_hint`s to pass to the authority server by default (default: [\"access_token\", \"refresh_token\"])\n     *\n     * Token types will be revoked in the same order as they are given here.\n     */\n    revokeTokenTypes?: (\"access_token\" | \"refresh_token\")[];\n    /** Will invoke the revocation endpoint on signout if there is an access token for the user (default: false) */\n    revokeTokensOnSignout?: boolean;\n    /** Flag to control if id_token is included as id_token_hint in silent signout calls (default: false) */\n    includeIdTokenInSilentSignout?: boolean;\n\n    /** The number of seconds before an access token is to expire to raise the accessTokenExpiring event (default: 60) */\n    accessTokenExpiringNotificationTimeInSeconds?: number;\n\n    /**\n     * Storage object used to persist User for currently authenticated user (default: window.sessionStorage, InMemoryWebStorage iff no window).\n     *  E.g. `userStore: new WebStorageStateStore({ store: window.localStorage })`\n     */\n    userStore?: WebStorageStateStore;\n}\n\n/**\n * The settings with defaults applied of the {@link UserManager}.\n * @see {@link UserManagerSettings}\n *\n * @public\n */\nexport class UserManagerSettingsStore extends OidcClientSettingsStore {\n    public readonly popup_redirect_uri: string;\n    public readonly popup_post_logout_redirect_uri: string | undefined;\n    public readonly popupWindowFeatures: PopupWindowFeatures;\n    public readonly popupWindowTarget: string;\n    public readonly redirectMethod: \"replace\" | \"assign\";\n    public readonly redirectTarget: \"top\" | \"self\";\n\n    public readonly iframeNotifyParentOrigin: string | undefined;\n    public readonly iframeScriptOrigin: string | undefined;\n\n    public readonly silent_redirect_uri: string;\n    public readonly silentRequestTimeoutInSeconds: number;\n    public readonly automaticSilentRenew: boolean;\n    public readonly validateSubOnSilentRenew: boolean;\n    public readonly includeIdTokenInSilentRenew: boolean;\n\n    public readonly monitorSession: boolean;\n    public readonly monitorAnonymousSession: boolean;\n    public readonly checkSessionIntervalInSeconds: number;\n    public readonly query_status_response_type: string;\n    public readonly stopCheckSessionOnError: boolean;\n\n    public readonly revokeTokenTypes: (\"access_token\" | \"refresh_token\")[];\n    public readonly revokeTokensOnSignout: boolean;\n    public readonly includeIdTokenInSilentSignout: boolean;\n\n    public readonly accessTokenExpiringNotificationTimeInSeconds: number;\n\n    public readonly userStore: WebStorageStateStore;\n\n    public constructor(args: UserManagerSettings) {\n        const {\n            popup_redirect_uri = args.redirect_uri,\n            popup_post_logout_redirect_uri = args.post_logout_redirect_uri,\n            popupWindowFeatures = DefaultPopupWindowFeatures,\n            popupWindowTarget = DefaultPopupTarget,\n            redirectMethod = \"assign\",\n            redirectTarget = \"self\",\n\n            iframeNotifyParentOrigin = args.iframeNotifyParentOrigin,\n            iframeScriptOrigin = args.iframeScriptOrigin,\n\n            requestTimeoutInSeconds,\n            silent_redirect_uri = args.redirect_uri,\n            silentRequestTimeoutInSeconds,\n            automaticSilentRenew = true,\n            validateSubOnSilentRenew = true,\n            includeIdTokenInSilentRenew = false,\n\n            monitorSession = false,\n            monitorAnonymousSession = false,\n            checkSessionIntervalInSeconds = DefaultCheckSessionIntervalInSeconds,\n            query_status_response_type = \"code\",\n            stopCheckSessionOnError = true,\n\n            revokeTokenTypes = [\"access_token\", \"refresh_token\"],\n            revokeTokensOnSignout = false,\n            includeIdTokenInSilentSignout = false,\n\n            accessTokenExpiringNotificationTimeInSeconds = DefaultAccessTokenExpiringNotificationTimeInSeconds,\n\n            userStore,\n        } = args;\n\n        super(args);\n\n        this.popup_redirect_uri = popup_redirect_uri;\n        this.popup_post_logout_redirect_uri = popup_post_logout_redirect_uri;\n        this.popupWindowFeatures = popupWindowFeatures;\n        this.popupWindowTarget = popupWindowTarget;\n        this.redirectMethod = redirectMethod;\n        this.redirectTarget = redirectTarget;\n\n        this.iframeNotifyParentOrigin = iframeNotifyParentOrigin;\n        this.iframeScriptOrigin = iframeScriptOrigin;\n\n        this.silent_redirect_uri = silent_redirect_uri;\n        this.silentRequestTimeoutInSeconds = silentRequestTimeoutInSeconds || requestTimeoutInSeconds || DefaultSilentRequestTimeoutInSeconds;\n        this.automaticSilentRenew = automaticSilentRenew;\n        this.validateSubOnSilentRenew = validateSubOnSilentRenew;\n        this.includeIdTokenInSilentRenew = includeIdTokenInSilentRenew;\n\n        this.monitorSession = monitorSession;\n        this.monitorAnonymousSession = monitorAnonymousSession;\n        this.checkSessionIntervalInSeconds = checkSessionIntervalInSeconds;\n        this.stopCheckSessionOnError = stopCheckSessionOnError;\n        this.query_status_response_type = query_status_response_type;\n\n        this.revokeTokenTypes = revokeTokenTypes;\n        this.revokeTokensOnSignout = revokeTokensOnSignout;\n        this.includeIdTokenInSilentSignout = includeIdTokenInSilentSignout;\n\n        this.accessTokenExpiringNotificationTimeInSeconds = accessTokenExpiringNotificationTimeInSeconds;\n\n        if (userStore) {\n            this.userStore = userStore;\n        }\n        else {\n            const store = typeof window !== \"undefined\" ? window.sessionStorage : new InMemoryWebStorage();\n            this.userStore = new WebStorageStateStore({ store });\n        }\n    }\n}\n", "// Copyright (c) Brock <PERSON> & <PERSON>. All rights reserved.\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\nimport { Logger } from \"../utils\";\nimport { ErrorTimeout } from \"../errors\";\nimport type { NavigateParams, NavigateResponse } from \"./IWindow\";\nimport { AbstractChildWindow } from \"./AbstractChildWindow\";\nimport { DefaultSilentRequestTimeoutInSeconds } from \"../UserManagerSettings\";\n\n/**\n * @public\n */\nexport interface IFrameWindowParams {\n    silentRequestTimeoutInSeconds?: number;\n}\n\n/**\n * @internal\n */\nexport class IFrameWindow extends AbstractChildWindow {\n    protected readonly _logger = new Logger(\"IFrameWindow\");\n    private _frame: HTMLIFrameElement | null;\n    private _timeoutInSeconds: number;\n\n    public constructor({\n        silentRequestTimeoutInSeconds = DefaultSilentRequestTimeoutInSeconds,\n    }: IFrameWindowParams) {\n        super();\n        this._timeoutInSeconds = silentRequestTimeoutInSeconds;\n\n        this._frame = IFrameWindow.createHiddenIframe();\n        this._window = this._frame.contentWindow;\n    }\n\n    private static createHiddenIframe(): HTMLIFrameElement {\n        const iframe = window.document.createElement(\"iframe\");\n\n        // shotgun approach\n        iframe.style.visibility = \"hidden\";\n        iframe.style.position = \"fixed\";\n        iframe.style.left = \"-1000px\";\n        iframe.style.top = \"0\";\n        iframe.width = \"0\";\n        iframe.height = \"0\";\n\n        window.document.body.appendChild(iframe);\n        return iframe;\n    }\n\n    public async navigate(params: NavigateParams): Promise<NavigateResponse> {\n        this._logger.debug(\"navigate: Using timeout of:\", this._timeoutInSeconds);\n        const timer = setTimeout(() => void this._abort.raise(new ErrorTimeout(\"IFrame timed out without a response\")), this._timeoutInSeconds * 1000);\n        this._disposeHandlers.add(() => clearTimeout(timer));\n\n        return await super.navigate(params);\n    }\n\n    public close(): void {\n        if (this._frame) {\n            if (this._frame.parentNode) {\n                this._frame.addEventListener(\"load\", (ev) => {\n                    const frame = ev.target as HTMLIFrameElement;\n                    frame.parentNode?.removeChild(frame);\n                    void this._abort.raise(new Error(\"IFrame removed from DOM\"));\n                }, true);\n                this._frame.contentWindow?.location.replace(\"about:blank\");\n            }\n            this._frame = null;\n        }\n        this._window = null;\n    }\n\n    public static notifyParent(url: string, targetOrigin?: string): void {\n        return super._notifyParent(window.parent, url, false, targetOrigin);\n    }\n}\n", "// Copyright (c) <PERSON> & <PERSON>. All rights reserved.\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\nimport { Logger } from \"../utils\";\nimport type { UserManagerSettingsStore } from \"../UserManagerSettings\";\nimport { IFrameWindow, type IFrameWindowParams } from \"./IFrameWindow\";\nimport type { INavigator } from \"./INavigator\";\n\n/**\n * @internal\n */\nexport class IF<PERSON>eNavigator implements INavigator {\n    private readonly _logger = new Logger(\"IFrameNavigator\");\n\n    constructor(private _settings: UserManagerSettingsStore) {}\n\n    public async prepare({\n        silentRequestTimeoutInSeconds = this._settings.silentRequestTimeoutInSeconds,\n    }: IFrameWindowParams): Promise<IFrameWindow> {\n        return new IFrameWindow({ silentRequestTimeoutInSeconds });\n    }\n\n    public async callback(url: string): Promise<void> {\n        this._logger.create(\"callback\");\n        IFrameWindow.notifyParent(url, this._settings.iframeNotifyParentOrigin);\n    }\n}\n", "// Copyright (c) Brock <PERSON> & Dominic<PERSON>. All rights reserved.\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\nimport { Logger, PopupUtils, type PopupWindowFeatures } from \"../utils\";\nimport { DefaultPopupWindowFeatures, DefaultPopupTarget } from \"../UserManagerSettings\";\nimport { AbstractChildWindow } from \"./AbstractChildWindow\";\nimport type { NavigateParams, NavigateResponse } from \"./IWindow\";\n\nconst checkForPopupClosedInterval = 500;\nconst second = 1000;\n\n/**\n * @public\n */\nexport interface PopupWindowParams {\n    popupWindowFeatures?: PopupWindowFeatures;\n    popupWindowTarget?: string;\n    /** An AbortSignal to set request's signal. */\n    popupSignal?: AbortSignal | null;\n}\n\n/**\n * @internal\n */\nexport class PopupWindow extends AbstractChildWindow {\n    protected readonly _logger = new Logger(\"PopupWindow\");\n\n    protected _window: WindowProxy | null;\n\n    public constructor({\n        popupWindowTarget = DefaultPopupTarget,\n        popupWindowFeatures = {},\n        popupSignal,\n    }: PopupWindowParams) {\n        super();\n        const centeredPopup = PopupUtils.center({ ...DefaultPopupWindowFeatures, ...popupWindowFeatures });\n        this._window = window.open(undefined, popupWindowTarget, PopupUtils.serialize(centeredPopup));\n\n        if (popupSignal) {\n            popupSignal.addEventListener(\"abort\", () => {\n                void this._abort.raise(new Error(popupSignal.reason ?? \"Popup aborted\"));\n            });\n        }\n\n        if (popupWindowFeatures.closePopupWindowAfterInSeconds && popupWindowFeatures.closePopupWindowAfterInSeconds > 0) {\n            setTimeout(() => {\n                if (!this._window || typeof this._window.closed !== \"boolean\" || this._window.closed) {\n                    void this._abort.raise(new Error(\"Popup blocked by user\"));\n                    return;\n                }\n\n                this.close();\n            }, popupWindowFeatures.closePopupWindowAfterInSeconds * second);\n        }\n    }\n\n    public async navigate(params: NavigateParams): Promise<NavigateResponse> {\n        this._window?.focus();\n\n        const popupClosedInterval = setInterval(() => {\n            if (!this._window || this._window.closed) {\n                void this._abort.raise(new Error(\"Popup closed by user\"));\n            }\n        }, checkForPopupClosedInterval);\n        this._disposeHandlers.add(() => clearInterval(popupClosedInterval));\n\n        return await super.navigate(params);\n    }\n\n    public close(): void {\n        if (this._window) {\n            if (!this._window.closed) {\n                this._window.close();\n                void this._abort.raise(new Error(\"Popup closed\"));\n            }\n        }\n        this._window = null;\n    }\n\n    public static notifyOpener(url: string, keepOpen: boolean): void {\n        if (!window.opener) {\n            throw new Error(\"No window.opener. Can't complete notification.\");\n        }\n        return super._notifyParent(window.opener, url, keepOpen);\n    }\n}\n", "// Copyright (c) <PERSON> & Dominic<PERSON>er. All rights reserved.\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\nimport { Logger } from \"../utils\";\nimport { PopupWindow, type PopupWindowParams } from \"./PopupWindow\";\nimport type { INavigator } from \"./INavigator\";\nimport type { UserManagerSettingsStore } from \"../UserManagerSettings\";\n\n/**\n * @internal\n */\nexport class PopupNavigator implements INavigator {\n    private readonly _logger = new Logger(\"PopupNavigator\");\n\n    constructor(private _settings: UserManagerSettingsStore) { }\n\n    public async prepare({\n        popupWindowFeatures = this._settings.popupWindowFeatures,\n        popupWindowTarget = this._settings.popupWindowTarget,\n        popupSignal,\n    }: PopupWindowParams): Promise<PopupWindow> {\n        return new PopupWindow({ popupWindowFeatures, popupWindowTarget, popupSignal });\n    }\n\n    public async callback(url: string, { keepOpen = false }): Promise<void> {\n        this._logger.create(\"callback\");\n\n        PopupWindow.notifyOpener(url, keepOpen);\n    }\n}\n", "// Copyright (c) Brock <PERSON> & Dominic<PERSON>. All rights reserved.\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\nimport { Logger } from \"../utils\";\nimport type { UserManagerSettingsStore } from \"../UserManagerSettings\";\nimport type { INavigator } from \"./INavigator\";\nimport type { IWindow } from \"./IWindow\";\n\n/**\n * @public\n */\nexport interface RedirectParams {\n    redirectMethod?: \"replace\" | \"assign\";\n    redirectTarget?: \"top\" | \"self\";\n}\n\n/**\n * @internal\n */\nexport class RedirectNavigator implements INavigator {\n    private readonly _logger = new Logger(\"RedirectNavigator\");\n\n    constructor(private _settings: UserManagerSettingsStore) {}\n\n    public async prepare({\n        redirectMethod = this._settings.redirectMethod,\n        redirectTarget = this._settings.redirectTarget,\n    }: RedirectParams): Promise<IWindow> {\n        this._logger.create(\"prepare\");\n        let targetWindow = window.self as Window;\n\n        if (redirectTarget === \"top\") {\n            targetWindow = window.top ?? window.self;\n        }\n    \n        const redirect = targetWindow.location[redirectMethod].bind(targetWindow.location) as (url: string) => never;\n        let abort: (reason: Error) => void;\n        return {\n            navigate: async (params): Promise<never> => {\n                this._logger.create(\"navigate\");\n                // We use a promise that never resolves to block the caller\n                const promise = new Promise((resolve, reject) => {\n                    abort = reject;\n                });\n                redirect(params.url);\n                return await (promise as Promise<never>);\n            },\n            close: () => {\n                this._logger.create(\"close\");\n                abort?.(new Error(\"Redirect aborted\"));\n                targetWindow.stop();\n            },\n        };\n    }\n\n    public async callback(): Promise<void> {\n        return;\n    }\n}\n", "// Copyright (c) <PERSON> & <PERSON>. All rights reserved.\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\nimport { Logger, Event } from \"./utils\";\nimport { AccessTokenEvents } from \"./AccessTokenEvents\";\nimport type { UserManagerSettingsStore } from \"./UserManagerSettings\";\nimport type { User } from \"./User\";\n\n/**\n * @public\n */\nexport type UserLoadedCallback = (user: User) => Promise<void> | void;\n/**\n * @public\n */\nexport type UserUnloadedCallback = () => Promise<void> | void;\n/**\n * @public\n */\nexport type SilentRenewErrorCallback = (error: Error) => Promise<void> | void;\n/**\n * @public\n */\nexport type UserSignedInCallback = () => Promise<void> | void;\n/**\n * @public\n */\nexport type UserSignedOutCallback = () => Promise<void> | void;\n/**\n * @public\n */\nexport type UserSessionChangedCallback = () => Promise<void> | void;\n\n/**\n * @public\n */\nexport class UserManagerEvents extends AccessTokenEvents {\n    protected readonly _logger = new Logger(\"UserManagerEvents\");\n\n    private readonly _userLoaded = new Event<[User]>(\"User loaded\");\n    private readonly _userUnloaded = new Event<[]>(\"User unloaded\");\n    private readonly _silentRenewError = new Event<[Error]>(\"Silent renew error\");\n    private readonly _userSignedIn = new Event<[]>(\"User signed in\");\n    private readonly _userSignedOut = new Event<[]>(\"User signed out\");\n    private readonly _userSessionChanged = new Event<[]>(\"User session changed\");\n\n    public constructor(settings: UserManagerSettingsStore) {\n        super({ expiringNotificationTimeInSeconds: settings.accessTokenExpiringNotificationTimeInSeconds });\n    }\n\n    public async load(user: User, raiseEvent=true): Promise<void> {\n        await super.load(user);\n        if (raiseEvent) {\n            await this._userLoaded.raise(user);\n        }\n    }\n\n    public async unload(): Promise<void> {\n        await super.unload();\n        await this._userUnloaded.raise();\n    }\n\n    /**\n     * Add callback: Raised when a user session has been established (or re-established).\n     */\n    public addUserLoaded(cb: UserLoadedCallback): () => void {\n        return this._userLoaded.addHandler(cb);\n    }\n    /**\n     * Remove callback: Raised when a user session has been established (or re-established).\n     */\n    public removeUserLoaded(cb: UserLoadedCallback): void {\n        return this._userLoaded.removeHandler(cb);\n    }\n\n    /**\n     * Add callback: Raised when a user session has been terminated.\n     */\n    public addUserUnloaded(cb: UserUnloadedCallback): () => void {\n        return this._userUnloaded.addHandler(cb);\n    }\n    /**\n     * Remove callback: Raised when a user session has been terminated.\n     */\n    public removeUserUnloaded(cb: UserUnloadedCallback): void {\n        return this._userUnloaded.removeHandler(cb);\n    }\n\n    /**\n     * Add callback: Raised when the automatic silent renew has failed.\n     */\n    public addSilentRenewError(cb: SilentRenewErrorCallback): () => void {\n        return this._silentRenewError.addHandler(cb);\n    }\n    /**\n     * Remove callback: Raised when the automatic silent renew has failed.\n     */\n    public removeSilentRenewError(cb: SilentRenewErrorCallback): void {\n        return this._silentRenewError.removeHandler(cb);\n    }\n    /**\n     * @internal\n     */\n    public async _raiseSilentRenewError(e: Error): Promise<void> {\n        await this._silentRenewError.raise(e);\n    }\n\n    /**\n     * Add callback: Raised when the user is signed in (when `monitorSession` is set).\n     * @see {@link UserManagerSettings.monitorSession}\n     */\n    public addUserSignedIn(cb: UserSignedInCallback): () => void {\n        return this._userSignedIn.addHandler(cb);\n    }\n    /**\n     * Remove callback: Raised when the user is signed in (when `monitorSession` is set).\n     */\n    public removeUserSignedIn(cb: UserSignedInCallback): void {\n        this._userSignedIn.removeHandler(cb);\n    }\n    /**\n     * @internal\n     */\n    public async _raiseUserSignedIn(): Promise<void> {\n        await this._userSignedIn.raise();\n    }\n\n    /**\n     * Add callback: Raised when the user's sign-in status at the OP has changed (when `monitorSession` is set).\n     * @see {@link UserManagerSettings.monitorSession}\n     */\n    public addUserSignedOut(cb: UserSignedOutCallback): () => void {\n        return this._userSignedOut.addHandler(cb);\n    }\n    /**\n     * Remove callback: Raised when the user's sign-in status at the OP has changed (when `monitorSession` is set).\n     */\n    public removeUserSignedOut(cb: UserSignedOutCallback): void {\n        this._userSignedOut.removeHandler(cb);\n    }\n    /**\n     * @internal\n     */\n    public async _raiseUserSignedOut(): Promise<void> {\n        await this._userSignedOut.raise();\n    }\n\n    /**\n     * Add callback: Raised when the user session changed (when `monitorSession` is set).\n     * @see {@link UserManagerSettings.monitorSession}\n     */\n    public addUserSessionChanged(cb: UserSessionChangedCallback): () => void {\n        return this._userSessionChanged.addHandler(cb);\n    }\n    /**\n     * Remove callback: Raised when the user session changed (when `monitorSession` is set).\n     */\n    public removeUserSessionChanged(cb: UserSessionChangedCallback): void {\n        this._userSessionChanged.removeHandler(cb);\n    }\n    /**\n     * @internal\n     */\n    public async _raiseUserSessionChanged(): Promise<void> {\n        await this._userSessionChanged.raise();\n    }\n}\n", "// Copyright (c) Brock <PERSON> & Dominic<PERSON>. All rights reserved.\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\nimport { Logger, Timer } from \"./utils\";\nimport { ErrorTimeout } from \"./errors\";\nimport type { UserManager } from \"./UserManager\";\nimport type { AccessTokenCallback } from \"./AccessTokenEvents\";\n\n/**\n * @internal\n */\nexport class SilentRenewService {\n    protected _logger = new Logger(\"SilentRenewService\");\n    private _isStarted = false;\n    private readonly _retryTimer = new Timer(\"Retry Silent Renew\");\n\n    public constructor(private _userManager: UserManager) {}\n\n    public async start(): Promise<void> {\n        const logger = this._logger.create(\"start\");\n        if (!this._isStarted) {\n            this._isStarted = true;\n            this._userManager.events.addAccessTokenExpiring(this._tokenExpiring);\n            this._retryTimer.addHandler(this._tokenExpiring);\n\n            // this will trigger loading of the user so the expiring events can be initialized\n            try {\n                await this._userManager.getUser();\n                // deliberate nop\n            }\n            catch (err) {\n                // catch to suppress errors since we're in a ctor\n                logger.error(\"getUser error\", err);\n            }\n        }\n    }\n\n    public stop(): void {\n        if (this._isStarted) {\n            this._retryTimer.cancel();\n            this._retryTimer.removeHandler(this._tokenExpiring);\n            this._userManager.events.removeAccessTokenExpiring(this._tokenExpiring);\n            this._isStarted = false;\n        }\n    }\n\n    protected _tokenExpiring: AccessTokenCallback = async () => {\n        const logger = this._logger.create(\"_tokenExpiring\");\n        try {\n            await this._userManager.signinSilent();\n            logger.debug(\"silent token renewal successful\");\n        }\n        catch (err) {\n            if (err instanceof ErrorTimeout) {\n                // no response from authority server, e.g. IFrame timeout, ...\n                logger.warn(\"ErrorTimeout from signinSilent:\", err, \"retry in 5s\");\n                this._retryTimer.init(5);\n                return;\n            }\n\n            logger.error(\"Error from signinSilent:\", err);\n            await this._userManager.events._raiseSilentRenewError(err as Error);\n        }\n    };\n}\n", "// Copyright (C) AuthTS Contributors\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\nimport type { UserProfile } from \"./User\";\n\n/**\n * Fake state store implementation necessary for validating refresh token requests.\n *\n * @public\n */\nexport class RefreshState {\n    /** custom \"state\", which can be used by a caller to have \"data\" round tripped */\n    public readonly data?: unknown;\n\n    public readonly refresh_token: string;\n    public readonly id_token?: string;\n    public readonly session_state: string | null;\n    public readonly scope?: string;\n    public readonly profile: UserProfile;\n\n    constructor(args: {\n        refresh_token: string;\n        id_token?: string;\n        session_state: string | null;\n        scope?: string;\n        profile: UserProfile;\n\n        state?: unknown;\n    }) {\n        this.refresh_token = args.refresh_token;\n        this.id_token = args.id_token;\n        this.session_state = args.session_state;\n        this.scope = args.scope;\n        this.profile = args.profile;\n\n        this.data = args.state;\n\n    }\n}\n", "// Copyright (c) <PERSON> & Dominic<PERSON>. All rights reserved.\n// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.\n\nimport { CryptoUtils, Logger } from \"./utils\";\nimport { ErrorResponse } from \"./errors\";\nimport { type NavigateResponse, type PopupWindowParams, type IWindow, type IFrameWindowParams, type RedirectParams, RedirectNavigator, PopupNavigator, IFrameNavigator, type INavigator } from \"./navigators\";\nimport { OidcClient, type CreateSigninRequestArgs, type CreateSignoutRequestArgs, type ProcessResourceOwnerPasswordCredentialsArgs, type UseRefreshTokenArgs } from \"./OidcClient\";\nimport { type UserManagerSettings, UserManagerSettingsStore } from \"./UserManagerSettings\";\nimport { User } from \"./User\";\nimport { UserManagerEvents } from \"./UserManagerEvents\";\nimport { SilentRenewService } from \"./SilentRenewService\";\nimport { SessionMonitor } from \"./SessionMonitor\";\nimport type { SessionStatus } from \"./SessionStatus\";\nimport type { SignoutResponse } from \"./SignoutResponse\";\nimport type { MetadataService } from \"./MetadataService\";\nimport { RefreshState } from \"./RefreshState\";\nimport type { SigninResponse } from \"./SigninResponse\";\nimport type { ExtraHeader, DPoPSettings } from \"./OidcClientSettings\";\nimport { DPoPState } from \"./DPoPStore\";\n\n/**\n * @public\n */\nexport type ExtraSigninRequestArgs = Pick<CreateSigninRequestArgs, \"nonce\" | \"extraQueryParams\" | \"extraTokenParams\" | \"state\" | \"redirect_uri\" | \"prompt\" | \"acr_values\" | \"login_hint\" | \"scope\" | \"max_age\" | \"ui_locales\" | \"resource\" | \"url_state\">;\n/**\n * @public\n */\nexport type ExtraSignoutRequestArgs = Pick<CreateSignoutRequestArgs, \"extraQueryParams\" | \"state\" | \"id_token_hint\" | \"post_logout_redirect_uri\" | \"url_state\">;\n\n/**\n * @public\n */\nexport type RevokeTokensTypes = UserManagerSettings[\"revokeTokenTypes\"];\n\n/**\n * @public\n */\nexport type SigninRedirectArgs = RedirectParams & ExtraSigninRequestArgs;\n\n/**\n * @public\n */\nexport type SigninPopupArgs = PopupWindowParams & ExtraSigninRequestArgs;\n\n/**\n * @public\n */\nexport type SigninSilentArgs = IFrameWindowParams & ExtraSigninRequestArgs;\n\n/**\n * @public\n */\nexport type SigninResourceOwnerCredentialsArgs = ProcessResourceOwnerPasswordCredentialsArgs;\n\n/**\n * @public\n */\nexport type QuerySessionStatusArgs = IFrameWindowParams & ExtraSigninRequestArgs;\n\n/**\n * @public\n */\nexport type SignoutRedirectArgs = RedirectParams & ExtraSignoutRequestArgs;\n\n/**\n * @public\n */\nexport type SignoutPopupArgs = PopupWindowParams & ExtraSignoutRequestArgs;\n\n/**\n * @public\n */\nexport type SignoutSilentArgs = IFrameWindowParams & ExtraSignoutRequestArgs;\n\n/**\n * Provides a higher level API for signing a user in, signing out, managing the user's claims returned from the identity provider,\n * and managing an access token returned from the identity provider (OAuth2/OIDC).\n *\n * @public\n */\nexport class UserManager {\n    /** Get the settings used to configure the `UserManager`. */\n    public readonly settings: UserManagerSettingsStore;\n    protected readonly _logger = new Logger(\"UserManager\");\n\n    protected readonly _client: OidcClient;\n    protected readonly _redirectNavigator: INavigator;\n    protected readonly _popupNavigator: INavigator;\n    protected readonly _iframeNavigator: INavigator;\n    protected readonly _events: UserManagerEvents;\n    protected readonly _silentRenewService: SilentRenewService;\n    protected readonly _sessionMonitor: SessionMonitor | null;\n\n    public constructor(settings: UserManagerSettings, redirectNavigator?: INavigator, popupNavigator?: INavigator, iframeNavigator?: INavigator) {\n        this.settings = new UserManagerSettingsStore(settings);\n\n        this._client = new OidcClient(settings);\n\n        this._redirectNavigator = redirectNavigator ?? new RedirectNavigator(this.settings);\n        this._popupNavigator = popupNavigator ?? new PopupNavigator(this.settings);\n        this._iframeNavigator = iframeNavigator ?? new IFrameNavigator(this.settings);\n\n        this._events = new UserManagerEvents(this.settings);\n        this._silentRenewService = new SilentRenewService(this);\n\n        // order is important for the following properties; these services depend upon the events.\n        if (this.settings.automaticSilentRenew) {\n            this.startSilentRenew();\n        }\n\n        this._sessionMonitor = null;\n        if (this.settings.monitorSession) {\n            this._sessionMonitor = new SessionMonitor(this);\n        }\n    }\n\n    /**\n     * Get object used to register for events raised by the `UserManager`.\n     */\n    public get events(): UserManagerEvents {\n        return this._events;\n    }\n\n    /**\n     * Get object used to access the metadata configuration of the identity provider.\n     */\n    public get metadataService(): MetadataService {\n        return this._client.metadataService;\n    }\n\n    /**\n     * Load the `User` object for the currently authenticated user.\n     *\n     * @param raiseEvent - If `true`, the `UserLoaded` event will be raised. Defaults to false.\n     * @returns A promise\n     */\n    public async getUser(raiseEvent = false): Promise<User | null> {\n        const logger = this._logger.create(\"getUser\");\n        const user = await this._loadUser();\n        if (user) {\n            logger.info(\"user loaded\");\n            await this._events.load(user, raiseEvent);\n            return user;\n        }\n\n        logger.info(\"user not found in storage\");\n        return null;\n    }\n\n    /**\n     * Remove from any storage the currently authenticated user.\n     *\n     * @returns A promise\n     */\n    public async removeUser(): Promise<void> {\n        const logger = this._logger.create(\"removeUser\");\n        await this.storeUser(null);\n        logger.info(\"user removed from storage\");\n        await this._events.unload();\n    }\n\n    /**\n     * Trigger a redirect of the current window to the authorization endpoint.\n     *\n     * @returns A promise\n     *\n     * @throws `Error` In cases of wrong authentication.\n     */\n    public async signinRedirect(args: SigninRedirectArgs = {}): Promise<void> {\n        this._logger.create(\"signinRedirect\");\n        const {\n            redirectMethod,\n            ...requestArgs\n        } = args;\n\n        let dpopJkt: string | undefined;\n        if (this.settings.dpop?.bind_authorization_code) {\n            dpopJkt = await this.generateDPoPJkt(this.settings.dpop);\n        }\n\n        const handle = await this._redirectNavigator.prepare({ redirectMethod });\n        await this._signinStart({\n            request_type: \"si:r\",\n            dpopJkt,\n            ...requestArgs,\n        }, handle);\n    }\n\n    /**\n     * Process the response (callback) from the authorization endpoint.\n     * It is recommended to use {@link UserManager.signinCallback} instead.\n     *\n     * @returns A promise containing the authenticated `User`.\n     *\n     * @see {@link UserManager.signinCallback}\n     */\n    public async signinRedirectCallback(url = window.location.href): Promise<User> {\n        const logger = this._logger.create(\"signinRedirectCallback\");\n        const user = await this._signinEnd(url);\n        if (user.profile && user.profile.sub) {\n            logger.info(\"success, signed in subject\", user.profile.sub);\n        }\n        else {\n            logger.info(\"no subject\");\n        }\n\n        return user;\n    }\n\n    /**\n     * Trigger the signin with user/password.\n     *\n     * @returns A promise containing the authenticated `User`.\n     * @throws {@link ErrorResponse} In cases of wrong authentication.\n     */\n    public async signinResourceOwnerCredentials({\n        username,\n        password,\n        skipUserInfo = false,\n    }: SigninResourceOwnerCredentialsArgs): Promise<User> {\n        const logger = this._logger.create(\"signinResourceOwnerCredential\");\n\n        const signinResponse = await this._client.processResourceOwnerPasswordCredentials({\n            username,\n            password,\n            skipUserInfo,\n            extraTokenParams: this.settings.extraTokenParams,\n        });\n        logger.debug(\"got signin response\");\n\n        const user = await this._buildUser(signinResponse);\n        if (user.profile && user.profile.sub) {\n            logger.info(\"success, signed in subject\", user.profile.sub);\n        } else {\n            logger.info(\"no subject\");\n        }\n        return user;\n    }\n\n    /**\n     * Trigger a request (via a popup window) to the authorization endpoint.\n     *\n     * @returns A promise containing the authenticated `User`.\n     * @throws `Error` In cases of wrong authentication.\n     */\n    public async signinPopup(args: SigninPopupArgs = {}): Promise<User> {\n        const logger = this._logger.create(\"signinPopup\");\n\n        let dpopJkt: string | undefined;\n        if (this.settings.dpop?.bind_authorization_code) {\n            dpopJkt = await this.generateDPoPJkt(this.settings.dpop);\n        }\n\n        const {\n            popupWindowFeatures,\n            popupWindowTarget,\n            popupSignal,\n            ...requestArgs\n        } = args;\n        const url = this.settings.popup_redirect_uri;\n        if (!url) {\n            logger.throw(new Error(\"No popup_redirect_uri configured\"));\n        }\n\n        const handle = await this._popupNavigator.prepare({ popupWindowFeatures, popupWindowTarget, popupSignal });\n        const user = await this._signin({\n            request_type: \"si:p\",\n            redirect_uri: url,\n            display: \"popup\",\n            dpopJkt,\n            ...requestArgs,\n        }, handle);\n        if (user) {\n            if (user.profile && user.profile.sub) {\n                logger.info(\"success, signed in subject\", user.profile.sub);\n            } else {\n                logger.info(\"no subject\");\n            }\n        }\n\n        return user;\n    }\n\n    /**\n     * Notify the opening window of response (callback) from the authorization endpoint.\n     * It is recommended to use {@link UserManager.signinCallback} instead.\n     *\n     * @returns A promise\n     *\n     * @see {@link UserManager.signinCallback}\n     */\n    public async signinPopupCallback(url = window.location.href, keepOpen = false): Promise<void> {\n        const logger = this._logger.create(\"signinPopupCallback\");\n        await this._popupNavigator.callback(url, { keepOpen });\n        logger.info(\"success\");\n    }\n\n    /**\n     * Trigger a silent request (via refresh token or an iframe) to the authorization endpoint.\n     *\n     * @returns A promise that contains the authenticated `User`.\n     */\n    public async signinSilent(args: SigninSilentArgs = {}): Promise<User | null> {\n        const logger = this._logger.create(\"signinSilent\");\n        const {\n            silentRequestTimeoutInSeconds,\n            ...requestArgs\n        } = args;\n        // first determine if we have a refresh token, or need to use iframe\n        let user = await this._loadUser();\n        if (user?.refresh_token) {\n            logger.debug(\"using refresh token\");\n            const state = new RefreshState(user as Required<User>);\n            return await this._useRefreshToken({\n                state,\n                redirect_uri: requestArgs.redirect_uri,\n                resource: requestArgs.resource,\n                extraTokenParams: requestArgs.extraTokenParams,\n                timeoutInSeconds: silentRequestTimeoutInSeconds,\n            });\n        }\n\n        let dpopJkt: string | undefined;\n        if (this.settings.dpop?.bind_authorization_code) {\n            dpopJkt = await this.generateDPoPJkt(this.settings.dpop);\n        }\n\n        const url = this.settings.silent_redirect_uri;\n        if (!url) {\n            logger.throw(new Error(\"No silent_redirect_uri configured\"));\n        }\n\n        let verifySub: string | undefined;\n        if (user && this.settings.validateSubOnSilentRenew) {\n            logger.debug(\"subject prior to silent renew:\", user.profile.sub);\n            verifySub = user.profile.sub;\n        }\n\n        const handle = await this._iframeNavigator.prepare({ silentRequestTimeoutInSeconds });\n        user = await this._signin({\n            request_type: \"si:s\",\n            redirect_uri: url,\n            prompt: \"none\",\n            id_token_hint: this.settings.includeIdTokenInSilentRenew ? user?.id_token : undefined,\n            dpopJkt,\n            ...requestArgs,\n        }, handle, verifySub);\n        if (user) {\n            if (user.profile?.sub) {\n                logger.info(\"success, signed in subject\", user.profile.sub);\n            }\n            else {\n                logger.info(\"no subject\");\n            }\n        }\n\n        return user;\n    }\n\n    protected async _useRefreshToken(args: UseRefreshTokenArgs): Promise<User> {\n        const response = await this._client.useRefreshToken({\n            timeoutInSeconds: this.settings.silentRequestTimeoutInSeconds,\n            ...args,\n        });\n        const user = new User({ ...args.state, ...response });\n\n        await this.storeUser(user);\n        await this._events.load(user);\n        return user;\n    }\n\n    /**\n     *\n     * Notify the parent window of response (callback) from the authorization endpoint.\n     * It is recommended to use {@link UserManager.signinCallback} instead.\n     *\n     * @returns A promise\n     *\n     * @see {@link UserManager.signinCallback}\n     */\n    public async signinSilentCallback(url = window.location.href): Promise<void> {\n        const logger = this._logger.create(\"signinSilentCallback\");\n        await this._iframeNavigator.callback(url);\n        logger.info(\"success\");\n    }\n\n    /**\n     * Process any response (callback) from the authorization endpoint, by dispatching the request_type\n     * and executing one of the following functions:\n     * - {@link UserManager.signinRedirectCallback}\n     * - {@link UserManager.signinPopupCallback}\n     * - {@link UserManager.signinSilentCallback}\n     *\n     * @throws `Error` If request_type is unknown or signin cannot be processed.\n     */\n    public async signinCallback(url = window.location.href): Promise<User | undefined> {\n        const { state } = await this._client.readSigninResponseState(url);\n        switch (state.request_type) {\n            case \"si:r\":\n                return await this.signinRedirectCallback(url);\n            case \"si:p\":\n                await this.signinPopupCallback(url);\n                break;\n            case \"si:s\":\n                await this.signinSilentCallback(url);\n                break;\n            default:\n                throw new Error(\"invalid response_type in state\");\n        }\n        return undefined;\n    }\n\n    /**\n     * Process any response (callback) from the end session endpoint, by dispatching the request_type\n     * and executing one of the following functions:\n     * - {@link UserManager.signoutRedirectCallback}\n     * - {@link UserManager.signoutPopupCallback}\n     * - {@link UserManager.signoutSilentCallback}\n     *\n     * @throws `Error` If request_type is unknown or signout cannot be processed.\n     */\n    public async signoutCallback(url = window.location.href, keepOpen = false): Promise<SignoutResponse | undefined> {\n        const { state } = await this._client.readSignoutResponseState(url);\n        if (!state) {\n            return undefined;\n        }\n\n        switch (state.request_type) {\n            case \"so:r\":\n                return await this.signoutRedirectCallback(url);\n            case \"so:p\":\n                await this.signoutPopupCallback(url, keepOpen);\n                break;\n            case \"so:s\":\n                await this.signoutSilentCallback(url);\n                break;\n            default:\n                throw new Error(\"invalid response_type in state\");\n        }\n        return undefined;\n    }\n\n    /**\n     * Query OP for user's current signin status.\n     *\n     * @returns A promise object with session_state and subject identifier.\n     */\n    public async querySessionStatus(args: QuerySessionStatusArgs = {}): Promise<SessionStatus | null> {\n        const logger = this._logger.create(\"querySessionStatus\");\n        const {\n            silentRequestTimeoutInSeconds,\n            ...requestArgs\n        } = args;\n        const url = this.settings.silent_redirect_uri;\n        if (!url) {\n            logger.throw(new Error(\"No silent_redirect_uri configured\"));\n        }\n\n        const user = await this._loadUser();\n        const handle = await this._iframeNavigator.prepare({ silentRequestTimeoutInSeconds });\n        const navResponse = await this._signinStart({\n            request_type: \"si:s\", // this acts like a signin silent\n            redirect_uri: url,\n            prompt: \"none\",\n            id_token_hint: this.settings.includeIdTokenInSilentRenew ? user?.id_token : undefined,\n            response_type: this.settings.query_status_response_type,\n            scope: \"openid\",\n            skipUserInfo: true,\n            ...requestArgs,\n        }, handle);\n        try {\n            const extraHeaders: Record<string, ExtraHeader> = {};\n            const signinResponse = await this._client.processSigninResponse(navResponse.url, extraHeaders);\n            logger.debug(\"got signin response\");\n\n            if (signinResponse.session_state && signinResponse.profile.sub) {\n                logger.info(\"success for subject\", signinResponse.profile.sub);\n                return {\n                    session_state: signinResponse.session_state,\n                    sub: signinResponse.profile.sub,\n                };\n            }\n\n            logger.info(\"success, user not authenticated\");\n            return null;\n        } catch (err) {\n            if (this.settings.monitorAnonymousSession && err instanceof ErrorResponse) {\n                switch (err.error) {\n                    case \"login_required\":\n                    case \"consent_required\":\n                    case \"interaction_required\":\n                    case \"account_selection_required\":\n                        logger.info(\"success for anonymous user\");\n                        return {\n                            session_state: err.session_state!,\n                        };\n                }\n            }\n            throw err;\n        }\n    }\n\n    protected async _signin(args: CreateSigninRequestArgs, handle: IWindow, verifySub?: string): Promise<User> {\n        const navResponse = await this._signinStart(args, handle);\n        return await this._signinEnd(navResponse.url, verifySub);\n    }\n\n    protected async _signinStart(args: CreateSigninRequestArgs, handle: IWindow): Promise<NavigateResponse> {\n        const logger = this._logger.create(\"_signinStart\");\n\n        try {\n            const signinRequest = await this._client.createSigninRequest(args);\n            logger.debug(\"got signin request\");\n\n            return await handle.navigate({\n                url: signinRequest.url,\n                state: signinRequest.state.id,\n                response_mode: signinRequest.state.response_mode,\n                scriptOrigin: this.settings.iframeScriptOrigin,\n            });\n        } catch (err) {\n            logger.debug(\"error after preparing navigator, closing navigator window\");\n            handle.close();\n            throw err;\n        }\n    }\n\n    protected async _signinEnd(url: string, verifySub?: string): Promise<User> {\n        const logger = this._logger.create(\"_signinEnd\");\n        const extraHeaders: Record<string, ExtraHeader> = {};\n        const signinResponse = await this._client.processSigninResponse(url, extraHeaders);\n        logger.debug(\"got signin response\");\n\n        const user = await this._buildUser(signinResponse, verifySub);\n        return user;\n    }\n\n    protected async _buildUser(signinResponse: SigninResponse, verifySub?: string) {\n        const logger = this._logger.create(\"_buildUser\");\n        const user = new User(signinResponse);\n        if (verifySub) {\n            if (verifySub !== user.profile.sub) {\n                logger.debug(\"current user does not match user returned from signin. sub from signin:\", user.profile.sub);\n                throw new ErrorResponse({ ...signinResponse, error: \"login_required\" });\n            }\n            logger.debug(\"current user matches user returned from signin\");\n        }\n\n        await this.storeUser(user);\n        logger.debug(\"user stored\");\n        await this._events.load(user);\n\n        return user;\n    }\n\n    /**\n     * Trigger a redirect of the current window to the end session endpoint.\n     *\n     * @returns A promise\n     */\n    public async signoutRedirect(args: SignoutRedirectArgs = {}): Promise<void> {\n        const logger = this._logger.create(\"signoutRedirect\");\n        const {\n            redirectMethod,\n            ...requestArgs\n        } = args;\n        const handle = await this._redirectNavigator.prepare({ redirectMethod });\n        await this._signoutStart({\n            request_type: \"so:r\",\n            post_logout_redirect_uri: this.settings.post_logout_redirect_uri,\n            ...requestArgs,\n        }, handle);\n        logger.info(\"success\");\n    }\n\n    /**\n     * Process response (callback) from the end session endpoint.\n     * It is recommended to use {@link UserManager.signoutCallback} instead.\n     *\n     * @returns A promise containing signout response\n     *\n     * @see {@link UserManager.signoutCallback}\n     */\n    public async signoutRedirectCallback(url = window.location.href): Promise<SignoutResponse> {\n        const logger = this._logger.create(\"signoutRedirectCallback\");\n        const response = await this._signoutEnd(url);\n        logger.info(\"success\");\n        return response;\n    }\n\n    /**\n     * Trigger a redirect of a popup window to the end session endpoint.\n     *\n     * @returns A promise\n     */\n    public async signoutPopup(args: SignoutPopupArgs = {}): Promise<void> {\n        const logger = this._logger.create(\"signoutPopup\");\n        const {\n            popupWindowFeatures,\n            popupWindowTarget,\n            popupSignal,\n            ...requestArgs\n        } = args;\n        const url = this.settings.popup_post_logout_redirect_uri;\n\n        const handle = await this._popupNavigator.prepare({ popupWindowFeatures, popupWindowTarget, popupSignal });\n        await this._signout({\n            request_type: \"so:p\",\n            post_logout_redirect_uri: url,\n            // we're putting a dummy entry in here because we\n            // need a unique id from the state for notification\n            // to the parent window, which is necessary if we\n            // plan to return back to the client after signout\n            // and so we can close the popup after signout\n            state: url == null ? undefined : {},\n            ...requestArgs,\n        }, handle);\n        logger.info(\"success\");\n    }\n\n    /**\n     * Process response (callback) from the end session endpoint from a popup window.\n     * It is recommended to use {@link UserManager.signoutCallback} instead.\n     *\n     * @returns A promise\n     *\n     * @see {@link UserManager.signoutCallback}\n     */\n    public async signoutPopupCallback(url = window.location.href, keepOpen = false): Promise<void> {\n        const logger = this._logger.create(\"signoutPopupCallback\");\n        await this._popupNavigator.callback(url, { keepOpen });\n        logger.info(\"success\");\n    }\n\n    protected async _signout(args: CreateSignoutRequestArgs, handle: IWindow): Promise<SignoutResponse> {\n        const navResponse = await this._signoutStart(args, handle);\n        return await this._signoutEnd(navResponse.url);\n    }\n\n    protected async _signoutStart(args: CreateSignoutRequestArgs = {}, handle: IWindow): Promise<NavigateResponse> {\n        const logger = this._logger.create(\"_signoutStart\");\n\n        try {\n            const user = await this._loadUser();\n            logger.debug(\"loaded current user from storage\");\n\n            if (this.settings.revokeTokensOnSignout) {\n                await this._revokeInternal(user);\n            }\n\n            const id_token = args.id_token_hint || user && user.id_token;\n            if (id_token) {\n                logger.debug(\"setting id_token_hint in signout request\");\n                args.id_token_hint = id_token;\n            }\n\n            await this.removeUser();\n            logger.debug(\"user removed, creating signout request\");\n\n            const signoutRequest = await this._client.createSignoutRequest(args);\n            logger.debug(\"got signout request\");\n\n            return await handle.navigate({\n                url: signoutRequest.url,\n                state: signoutRequest.state?.id,\n                scriptOrigin: this.settings.iframeScriptOrigin,\n            });\n        } catch (err) {\n            logger.debug(\"error after preparing navigator, closing navigator window\");\n            handle.close();\n            throw err;\n        }\n    }\n\n    protected async _signoutEnd(url: string): Promise<SignoutResponse> {\n        const logger = this._logger.create(\"_signoutEnd\");\n        const signoutResponse = await this._client.processSignoutResponse(url);\n        logger.debug(\"got signout response\");\n\n        return signoutResponse;\n    }\n\n    /**\n     * Trigger a silent request (via an iframe) to the end session endpoint.\n     *\n     * @returns A promise\n     */\n    public async signoutSilent(args: SignoutSilentArgs = {}): Promise<void> {\n        const logger = this._logger.create(\"signoutSilent\");\n        const {\n            silentRequestTimeoutInSeconds,\n            ...requestArgs\n        } = args;\n\n        const id_token_hint = this.settings.includeIdTokenInSilentSignout\n            ? (await this._loadUser())?.id_token\n            : undefined;\n\n        const url = this.settings.popup_post_logout_redirect_uri;\n        const handle = await this._iframeNavigator.prepare({ silentRequestTimeoutInSeconds });\n        await this._signout({\n            request_type: \"so:s\",\n            post_logout_redirect_uri: url,\n            id_token_hint: id_token_hint,\n            ...requestArgs,\n        }, handle);\n\n        logger.info(\"success\");\n    }\n\n    /**\n     * Notify the parent window of response (callback) from the end session endpoint.\n     * It is recommended to use {@link UserManager.signoutCallback} instead.\n     *\n     * @returns A promise\n     *\n     * @see {@link UserManager.signoutCallback}\n     */\n    public async signoutSilentCallback(url = window.location.href): Promise<void> {\n        const logger = this._logger.create(\"signoutSilentCallback\");\n        await this._iframeNavigator.callback(url);\n        logger.info(\"success\");\n    }\n\n    public async revokeTokens(types?: RevokeTokensTypes): Promise<void> {\n        const user = await this._loadUser();\n        await this._revokeInternal(user, types);\n    }\n\n    protected async _revokeInternal(user: User | null, types = this.settings.revokeTokenTypes): Promise<void> {\n        const logger = this._logger.create(\"_revokeInternal\");\n        if (!user) return;\n\n        const typesPresent = types.filter(type => typeof user[type] === \"string\");\n\n        if (!typesPresent.length) {\n            logger.debug(\"no need to revoke due to no token(s)\");\n            return;\n        }\n\n        // don't Promise.all, order matters\n        for (const type of typesPresent) {\n            await this._client.revokeToken(\n                user[type]!,\n                type,\n            );\n            logger.info(`${type} revoked successfully`);\n            if (type !== \"access_token\") {\n                user[type] = null as never;\n            }\n        }\n\n        await this.storeUser(user);\n        logger.debug(\"user stored\");\n        await this._events.load(user);\n    }\n\n    /**\n     * Enables silent renew for the `UserManager`.\n     */\n    public startSilentRenew(): void {\n        this._logger.create(\"startSilentRenew\");\n        void this._silentRenewService.start();\n    }\n\n    /**\n     * Disables silent renew for the `UserManager`.\n     */\n    public stopSilentRenew(): void {\n        this._silentRenewService.stop();\n    }\n\n    protected get _userStoreKey(): string {\n        return `user:${this.settings.authority}:${this.settings.client_id}`;\n    }\n\n    protected async _loadUser(): Promise<User | null> {\n        const logger = this._logger.create(\"_loadUser\");\n        const storageString = await this.settings.userStore.get(this._userStoreKey);\n        if (storageString) {\n            logger.debug(\"user storageString loaded\");\n            return User.fromStorageString(storageString);\n        }\n\n        logger.debug(\"no user storageString\");\n        return null;\n    }\n\n    public async storeUser(user: User | null): Promise<void> {\n        const logger = this._logger.create(\"storeUser\");\n        if (user) {\n            logger.debug(\"storing user\");\n            const storageString = user.toStorageString();\n            await this.settings.userStore.set(this._userStoreKey, storageString);\n        } else {\n            this._logger.debug(\"removing user\");\n            await this.settings.userStore.remove(this._userStoreKey);\n            if (this.settings.dpop) {\n                await this.settings.dpop.store.remove(this.settings.client_id);\n            }\n        }\n    }\n\n    /**\n     * Removes stale state entries in storage for incomplete authorize requests.\n     */\n    public async clearStaleState(): Promise<void> {\n        await this._client.clearStaleState();\n    }\n\n    /**\n     * Dynamically generates a DPoP proof for a given user, URL and optional Http method.\n     * This method is useful when you need to make a request to a resource server\n     * with fetch or similar, and you need to include a DPoP proof in a DPoP header.\n     * @param url - The URL to generate the DPoP proof for\n     * @param user - The user to generate the DPoP proof for\n     * @param httpMethod - Optional, defaults to \"GET\"\n     * @param nonce - Optional nonce provided by the resource server\n     *\n     * @returns A promise containing the DPoP proof or undefined if DPoP is not enabled/no user is found.\n     */\n    public async dpopProof(url: string, user: User, httpMethod?: string, nonce?: string): Promise<string | undefined> {\n        const dpopState = await this.settings.dpop?.store?.get(this.settings.client_id);\n        if (dpopState) {\n            return await CryptoUtils.generateDPoPProof({\n                url,\n                accessToken: user?.access_token,\n                httpMethod: httpMethod,\n                keyPair: dpopState.keys,\n                nonce,\n            });\n        }\n        return undefined;\n    }\n\n    async generateDPoPJkt(dpopSettings: DPoPSettings): Promise<string | undefined> {\n        let dpopState = await dpopSettings.store.get(this.settings.client_id);\n        if (!dpopState) {\n            const dpopKeys = await CryptoUtils.generateDPoPKeys();\n            dpopState = new DPoPState(dpopKeys);\n            await dpopSettings.store.set(this.settings.client_id, dpopState);\n        }\n        return await CryptoUtils.generateDPoPJkt(dpopState.keys);\n    }\n}\n", "{\n  \"name\": \"oidc-client-ts\",\n  \"version\": \"3.2.0\",\n  \"description\": \"OpenID Connect (OIDC) & OAuth2 client library\",\n  \"repository\": {\n    \"type\": \"git\",\n    \"url\": \"git+https://github.com/authts/oidc-client-ts.git\"\n  },\n  \"homepage\": \"https://github.com/authts/oidc-client-ts#readme\",\n  \"license\": \"Apache-2.0\",\n  \"main\": \"dist/umd/oidc-client-ts.js\",\n  \"types\": \"dist/types/oidc-client-ts.d.ts\",\n  \"exports\": {\n    \".\": {\n      \"types\": \"./dist/types/oidc-client-ts.d.ts\",\n      \"import\": \"./dist/esm/oidc-client-ts.js\",\n      \"require\": \"./dist/umd/oidc-client-ts.js\"\n    },\n    \"./package.json\": \"./package.json\"\n  },\n  \"files\": [\n    \"dist\"\n  ],\n  \"keywords\": [\n    \"authentication\",\n    \"oauth2\",\n    \"oidc\",\n    \"openid\",\n    \"OpenID Connect\"\n  ],\n  \"scripts\": {\n    \"build\": \"node scripts/build.js && npm run build-types\",\n    \"build-types\": \"tsc -p tsconfig.build.json && api-extractor run\",\n    \"clean\": \"git clean -fdX dist lib *.tsbuildinfo\",\n    \"prepack\": \"npm run build\",\n    \"test\": \"tsc && jest\",\n    \"typedoc\": \"typedoc\",\n    \"lint\": \"eslint --max-warnings=0 --cache .\",\n    \"prepare\": \"husky\"\n  },\n  \"dependencies\": {\n    \"jwt-decode\": \"^4.0.0\"\n  },\n  \"devDependencies\": {\n    \"@eslint/eslintrc\": \"^3.2.0\",\n    \"@eslint/js\": \"^9.18.0\",\n    \"@microsoft/api-extractor\": \"^7.49.1\",\n    \"@stylistic/eslint-plugin\": \"^2.13.0\",\n    \"@testing-library/jest-dom\": \"^6.6.3\",\n    \"@types/jest\": \"^29.5.14\",\n    \"@types/node\": \"^22.10.1\",\n    \"@typescript-eslint/eslint-plugin\": \"^8.20.0\",\n    \"@typescript-eslint/parser\": \"^8.20.0\",\n    \"esbuild\": \"^0.25.0\",\n    \"eslint\": \"^9.18.0\",\n    \"eslint-plugin-testing-library\": \"^7.1.1\",\n    \"fake-indexeddb\": \"^6.0.0\",\n    \"globals\": \"^16.0.0\",\n    \"http-proxy-middleware\": \"^3.0.3\",\n    \"husky\": \"^9.1.7\",\n    \"jest\": \"^29.7.0\",\n    \"jest-environment-jsdom\": \"^29.7.0\",\n    \"jest-mock\": \"^29.7.0\",\n    \"jose\": \"^5.9.6\",\n    \"lint-staged\": \"^15.2.10\",\n    \"ts-jest\": \"^29.2.5\",\n    \"typedoc\": \"^0.27.4\",\n    \"typescript\": \"~5.8.2\",\n    \"yn\": \"^5.0.0\"\n  },\n  \"engines\": {\n    \"node\": \">=18\"\n  },\n  \"lint-staged\": {\n    \"*.{js,jsx,ts,tsx}\": \"eslint --cache --fix\"\n  }\n}\n", "// @ts-expect-error avoid enabling resolveJsonModule to keep build process simple\nimport { version } from \"../package.json\";\n\n/**\n * @public\n */\nexport const Version: string = version;\n", "import { DPoPState, type DPoPStore } from \"./DPoPStore\";\n\n/**\n * Provides a default implementation of the DPoP store using IndexedDB.\n *\n * @public\n */\nexport class IndexedDbDPoPStore implements DPoPStore {\n    readonly _dbName: string = \"oidc\";\n    readonly _storeName: string = \"dpop\";\n\n    public async set(key: string, value: DPoPState): Promise<void> {\n        const store = await this.createStore(this._dbName, this._storeName);\n        await store(\"readwrite\", (str: IDBObjectStore) => {\n            str.put(value, key);\n            return this.promisifyRequest(str.transaction);\n        });\n    }\n\n    public async get(key: string): Promise<DPoPState> {\n        const store = await this.createStore(this._dbName, this._storeName);\n        return await store(\"readonly\", (str) => {\n            return this.promisifyRequest(str.get(key));\n        }) as DPoPState;\n    }\n\n    public async remove(key: string): Promise<DPoPState> {\n        const item = await this.get(key);\n        const store = await this.createStore(this._dbName, this._storeName);\n        await store(\"readwrite\", (str) => {\n            return this.promisifyRequest(str.delete(key));\n        });\n        return item;\n    }\n\n    public async getAllKeys(): Promise<string[]> {\n        const store = await this.createStore(this._dbName, this._storeName);\n        return await store(\"readonly\", (str) => {\n            return this.promisifyRequest(str.getAllKeys());\n        }) as string[];\n    }\n\n    promisifyRequest<T = undefined>(\n        request: IDBRequest<T> | IDBTransaction): Promise<T> {\n        return new Promise<T>((resolve, reject) => {\n            (request as IDBTransaction).oncomplete = (request as IDBRequest<T>).onsuccess = () => resolve((request as IDBRequest<T>).result);\n            (request as IDBTransaction).onabort = (request as IDBRequest<T>).onerror = () => reject((request as IDBRequest<T>).error as Error);\n        });\n    }\n\n    async createStore<T>(\n        dbName: string,\n        storeName: string,\n    ): Promise<(txMode: IDBTransactionMode, callback: (store: IDBObjectStore) => T | PromiseLike<T>) => Promise<T>> {\n        const request = indexedDB.open(dbName);\n        request.onupgradeneeded = () => request.result.createObjectStore(storeName);\n        const db = await this.promisifyRequest<IDBDatabase>(request);\n\n        return async (\n            txMode: IDBTransactionMode,\n            callback: (store: IDBObjectStore) => T | PromiseLike<T>,\n        ) => {\n            const tx = db.transaction(storeName, txMode);\n            const store = tx.objectStore(storeName);\n            return await callback(store);\n        };\n    }\n}\n"], "mappings": ";AAeA,IAAMA,SAAA,GAAqB;EACvBC,KAAA,EAAOA,CAAA,KAAM;EACbC,IAAA,EAAMA,CAAA,KAAM;EACZC,IAAA,EAAMA,CAAA,KAAM;EACZC,KAAA,EAAOA,CAAA,KAAM;AACjB;AAEA,IAAIC,KAAA;AACJ,IAAIC,MAAA;AAOG,IAAKC,GAAA,GAAL,gBAAKC,IAAA,IAAL;EACHA,IAAA,CAAAA,IAAA;EACAA,IAAA,CAAAA,IAAA;EACAA,IAAA,CAAAA,IAAA;EACAA,IAAA,CAAAA,IAAA;EACAA,IAAA,CAAAA,IAAA;EALQ,OAAAA,IAAA;AAAA,GAAAD,GAAA;AAAA,CAaKC,IAAA,IAAV;EACI,SAASC,MAAA,EAAc;IAC1BJ,KAAA,GAAQ;IACRC,MAAA,GAASN,SAAA;EACb;EAHOQ,IAAA,CAASC,KAAA,GAAAA,KAAA;EAKT,SAASC,SAASC,KAAA,EAAkB;IACvC,IAAI,EAAE,gBAAYA,KAAA,IAASA,KAAA,IAAS,gBAAY;MAC5C,MAAM,IAAIC,KAAA,CAAM,mBAAmB;IACvC;IACAP,KAAA,GAAQM,KAAA;EACZ;EALOH,IAAA,CAASE,QAAA,GAAAA,QAAA;EAOT,SAASG,UAAUF,KAAA,EAAsB;IAC5CL,MAAA,GAASK,KAAA;EACb;EAFOH,IAAA,CAASK,SAAA,GAAAA,SAAA;AAAA,GAbHN,GAAA,KAAAA,GAAA;AAuBV,IAAMO,MAAA,GAAN,MAAMC,OAAA,CAAO;EAETC,YAAoBC,KAAA,EAAe;IAAf,KAAAA,KAAA,GAAAA,KAAA;EAAgB;EAAA;EAGpChB,MAAA,GAASiB,IAAA,EAAuB;IACnC,IAAIb,KAAA,IAAS,eAAW;MACpBC,MAAA,CAAOL,KAAA,CAAMc,OAAA,CAAOI,OAAA,CAAQ,KAAKF,KAAA,EAAO,KAAKG,OAAO,GAAG,GAAGF,IAAI;IAClE;EACJ;EACOhB,KAAA,GAAQgB,IAAA,EAAuB;IAClC,IAAIb,KAAA,IAAS,cAAU;MACnBC,MAAA,CAAOJ,IAAA,CAAKa,OAAA,CAAOI,OAAA,CAAQ,KAAKF,KAAA,EAAO,KAAKG,OAAO,GAAG,GAAGF,IAAI;IACjE;EACJ;EACOf,KAAA,GAAQe,IAAA,EAAuB;IAClC,IAAIb,KAAA,IAAS,cAAU;MACnBC,MAAA,CAAOH,IAAA,CAAKY,OAAA,CAAOI,OAAA,CAAQ,KAAKF,KAAA,EAAO,KAAKG,OAAO,GAAG,GAAGF,IAAI;IACjE;EACJ;EACOd,MAAA,GAASc,IAAA,EAAuB;IACnC,IAAIb,KAAA,IAAS,eAAW;MACpBC,MAAA,CAAOF,KAAA,CAAMW,OAAA,CAAOI,OAAA,CAAQ,KAAKF,KAAA,EAAO,KAAKG,OAAO,GAAG,GAAGF,IAAI;IAClE;EACJ;EAAA;EAGOG,MAAMC,GAAA,EAAmB;IAC5B,KAAKlB,KAAA,CAAMkB,GAAG;IACd,MAAMA,GAAA;EACV;EAEOC,OAAOC,MAAA,EAAwB;IAClC,MAAMC,YAAA,GAAuBC,MAAA,CAAOH,MAAA,CAAO,IAAI;IAC/CE,YAAA,CAAaL,OAAA,GAAUI,MAAA;IACvBC,YAAA,CAAaxB,KAAA,CAAM,OAAO;IAC1B,OAAOwB,YAAA;EACX;EAEA,OAAcE,aAAaC,IAAA,EAAcC,YAAA,EAA8B;IACnE,MAAMC,YAAA,GAAe,IAAIf,OAAA,CAAO,GAAGa,IAAI,IAAIC,YAAY,EAAE;IACzDC,YAAA,CAAa7B,KAAA,CAAM,OAAO;IAC1B,OAAO6B,YAAA;EACX;EAEA,OAAeX,QAAQS,IAAA,EAAcJ,MAAA,EAAiB;IAClD,MAAMO,MAAA,GAAS,IAAIH,IAAI;IACvB,OAAOJ,MAAA,GAAS,GAAGO,MAAM,IAAIP,MAAM,MAAMO,MAAA;EAC7C;EAAA;EAAA;EAIA,OAAc9B,MAAM2B,IAAA,KAAiBV,IAAA,EAAuB;IACxD,IAAIb,KAAA,IAAS,eAAW;MACpBC,MAAA,CAAOL,KAAA,CAAMc,OAAA,CAAOI,OAAA,CAAQS,IAAI,GAAG,GAAGV,IAAI;IAC9C;EACJ;EACA,OAAchB,KAAK0B,IAAA,KAAiBV,IAAA,EAAuB;IACvD,IAAIb,KAAA,IAAS,cAAU;MACnBC,MAAA,CAAOJ,IAAA,CAAKa,OAAA,CAAOI,OAAA,CAAQS,IAAI,GAAG,GAAGV,IAAI;IAC7C;EACJ;EACA,OAAcf,KAAKyB,IAAA,KAAiBV,IAAA,EAAuB;IACvD,IAAIb,KAAA,IAAS,cAAU;MACnBC,MAAA,CAAOH,IAAA,CAAKY,OAAA,CAAOI,OAAA,CAAQS,IAAI,GAAG,GAAGV,IAAI;IAC7C;EACJ;EACA,OAAcd,MAAMwB,IAAA,KAAiBV,IAAA,EAAuB;IACxD,IAAIb,KAAA,IAAS,eAAW;MACpBC,MAAA,CAAOF,KAAA,CAAMW,OAAA,CAAOI,OAAA,CAAQS,IAAI,GAAG,GAAGV,IAAI;IAC9C;EACJ;EAAA;AAEJ;AAEAX,GAAA,CAAIE,KAAA,CAAM;;;AC7IV,SAASuB,SAAA,QAAiB;AASnB,IAAMC,QAAA,GAAN,MAAe;EAAA;EAElB,OAAcC,OAAOC,KAAA,EAA0B;IAC3C,IAAI;MACA,OAAOH,SAAA,CAAqBG,KAAK;IACrC,SACOb,GAAA,EAAK;MACRR,MAAA,CAAOV,KAAA,CAAM,mBAAmBkB,GAAG;MACnC,MAAMA,GAAA;IACV;EACJ;EAEA,aAAoBc,kBAAkBC,MAAA,EAAgBC,OAAA,EAAiBC,UAAA,EAAyC;IAC5G,MAAMC,aAAA,GAAgBC,WAAA,CAAYC,eAAA,CAAgB,IAAIC,WAAA,CAAY,EAAEC,MAAA,CAAOC,IAAA,CAAKC,SAAA,CAAUT,MAAM,CAAC,CAAC;IAClG,MAAMU,cAAA,GAAiBN,WAAA,CAAYC,eAAA,CAAgB,IAAIC,WAAA,CAAY,EAAEC,MAAA,CAAOC,IAAA,CAAKC,SAAA,CAAUR,OAAO,CAAC,CAAC;IACpG,MAAMU,YAAA,GAAe,GAAGR,aAAa,IAAIO,cAAc;IAEvD,MAAME,SAAA,GAAY,MAAMC,MAAA,CAAOC,MAAA,CAAOC,MAAA,CAAOC,IAAA,CACzC;MACIzB,IAAA,EAAM;MACN0B,IAAA,EAAM;QAAE1B,IAAA,EAAM;MAAU;IAC5B,GACAW,UAAA,EACA,IAAII,WAAA,CAAY,EAAEC,MAAA,CAAOI,YAAY,CACzC;IAEA,MAAMO,gBAAA,GAAmBd,WAAA,CAAYC,eAAA,CAAgB,IAAIc,UAAA,CAAWP,SAAS,CAAC;IAC9E,OAAO,GAAGD,YAAY,IAAIO,gBAAgB;EAC9C;AACJ;;;AC3BA,IAAME,gBAAA,GAAmB;AAEzB,IAAMC,QAAA,GAAYC,GAAA,IACdC,IAAA,CAAK,CAAC,GAAG,IAAIJ,UAAA,CAAWG,GAAG,CAAC,EACvBE,GAAA,CAAKC,GAAA,IAAQC,MAAA,CAAOC,YAAA,CAAaF,GAAG,CAAC,EACrCG,IAAA,CAAK,EAAE,CAAC;AAKV,IAAMC,YAAA,GAAN,MAAMA,YAAA,CAAY;EACrB,OAAeC,YAAA,EAAsB;IACjC,MAAMC,GAAA,GAAM,IAAIC,WAAA,CAAY,CAAC;IAC7BlB,MAAA,CAAOmB,eAAA,CAAgBF,GAAG;IAC1B,OAAOA,GAAA,CAAI,CAAC;EAChB;EAAA;AAAA;AAAA;EAKA,OAAcG,eAAA,EAAyB;IACnC,MAAMC,IAAA,GAAOf,gBAAA,CAAiBgB,OAAA,CAAQ,UAAUC,CAAA,KAC3C,CAACA,CAAA,GAAIR,YAAA,CAAYC,WAAA,CAAY,IAAI,MAAM,CAACO,CAAA,GAAI,GAAGC,QAAA,CAAS,EAAE,CAC/D;IACA,OAAOH,IAAA,CAAKC,OAAA,CAAQ,MAAM,EAAE;EAChC;EAAA;AAAA;AAAA;EAKA,OAAcG,qBAAA,EAA+B;IACzC,OAAOV,YAAA,CAAYK,cAAA,CAAe,IAAIL,YAAA,CAAYK,cAAA,CAAe,IAAIL,YAAA,CAAYK,cAAA,CAAe;EACpG;EAAA;AAAA;AAAA;EAKA,aAAoBM,sBAAsBC,aAAA,EAAwC;IAC9E,IAAI,CAAC3B,MAAA,CAAOC,MAAA,EAAQ;MAChB,MAAM,IAAIxC,KAAA,CAAM,6DAA6D;IACjF;IAEA,IAAI;MACA,MAAMmE,OAAA,GAAU,IAAIpC,WAAA,CAAY;MAChC,MAAMqC,IAAA,GAAOD,OAAA,CAAQnC,MAAA,CAAOkC,aAAa;MACzC,MAAMG,MAAA,GAAS,MAAM9B,MAAA,CAAOC,MAAA,CAAO8B,MAAA,CAAO,WAAWF,IAAI;MACzD,OAAOtB,QAAA,CAASuB,MAAM,EAAER,OAAA,CAAQ,OAAO,GAAG,EAAEA,OAAA,CAAQ,OAAO,GAAG,EAAEA,OAAA,CAAQ,OAAO,EAAE;IACrF,SACOnD,GAAA,EAAK;MACRR,MAAA,CAAOV,KAAA,CAAM,qCAAqCkB,GAAG;MACrD,MAAMA,GAAA;IACV;EACJ;EAAA;AAAA;AAAA;EAKA,OAAc6D,kBAAkBC,SAAA,EAAmBC,aAAA,EAA+B;IAC9E,MAAMN,OAAA,GAAU,IAAIpC,WAAA,CAAY;IAChC,MAAMqC,IAAA,GAAOD,OAAA,CAAQnC,MAAA,CAAO,CAACwC,SAAA,EAAWC,aAAa,EAAEpB,IAAA,CAAK,GAAG,CAAC;IAChE,OAAOP,QAAA,CAASsB,IAAI;EACxB;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA,aAAoB1B,KAAKgC,GAAA,EAAaC,OAAA,EAAuC;IACzE,MAAMC,QAAA,GAAW,IAAI7C,WAAA,CAAY,EAAEC,MAAA,CAAO2C,OAAO;IACjD,MAAME,UAAA,GAAa,MAAMtC,MAAA,CAAOC,MAAA,CAAO8B,MAAA,CAAOI,GAAA,EAAKE,QAAQ;IAC3D,OAAO,IAAIhC,UAAA,CAAWiC,UAAU;EACpC;EAAA;AAAA;AAAA;AAAA;EAaA,aAAoBC,6BAA6BC,GAAA,EAAkC;IAC/E,IAAIC,UAAA;IACJ,QAAQD,GAAA,CAAIE,GAAA;MACR,KAAK;QACDD,UAAA,GAAa;UACT,KAAKD,GAAA,CAAIG,CAAA;UACT,OAAOH,GAAA,CAAIE,GAAA;UACX,KAAKF,GAAA,CAAII;QACb;QACA;MACJ,KAAK;QACDH,UAAA,GAAa;UACT,OAAOD,GAAA,CAAIK,GAAA;UACX,OAAOL,GAAA,CAAIE,GAAA;UACX,KAAKF,GAAA,CAAIM,CAAA;UACT,KAAKN,GAAA,CAAIO;QACb;QACA;MACJ,KAAK;QACDN,UAAA,GAAa;UACT,OAAOD,GAAA,CAAIK,GAAA;UACX,OAAOL,GAAA,CAAIE,GAAA;UACX,KAAKF,GAAA,CAAIM;QACb;QACA;MACJ,KAAK;QACDL,UAAA,GAAa;UACT,OAAOD,GAAA,CAAIQ,CAAA;UACX,OAAOR,GAAA,CAAIE;QACf;QACA;MACJ;QACI,MAAM,IAAIjF,KAAA,CAAM,kBAAkB;IAC1C;IACA,MAAMwF,oBAAA,GAAuB,MAAMlC,YAAA,CAAYZ,IAAA,CAAK,WAAWT,IAAA,CAAKC,SAAA,CAAU8C,UAAU,CAAC;IACzF,OAAO1B,YAAA,CAAYxB,eAAA,CAAgB0D,oBAAoB;EAC3D;EAEA,aAAoBC,kBAAkB;IAClCC,GAAA;IACAC,WAAA;IACAC,UAAA;IACAC,OAAA;IACAC;EACJ,GAA2C;IACvC,IAAIC,WAAA;IACJ,IAAIC,WAAA;IAEJ,MAAMtE,OAAA,GAA2C;MAC7C,OAAOY,MAAA,CAAOC,MAAA,CAAO0D,UAAA,CAAW;MAChC,OAAOL,UAAA,WAAAA,UAAA,GAAc;MACrB,OAAOF,GAAA;MACP,OAAOQ,IAAA,CAAKC,KAAA,CAAMC,IAAA,CAAKC,GAAA,CAAI,IAAI,GAAI;IACvC;IAEA,IAAIV,WAAA,EAAa;MACbI,WAAA,GAAc,MAAMzC,YAAA,CAAYZ,IAAA,CAAK,WAAWiD,WAAW;MAC3DK,WAAA,GAAc1C,YAAA,CAAYxB,eAAA,CAAgBiE,WAAW;MACrDrE,OAAA,CAAQ4E,GAAA,GAAMN,WAAA;IAClB;IAEA,IAAIF,KAAA,EAAO;MACPpE,OAAA,CAAQoE,KAAA,GAAQA,KAAA;IACpB;IAEA,IAAI;MACA,MAAMS,SAAA,GAAY,MAAMhE,MAAA,CAAOC,MAAA,CAAOgE,SAAA,CAAU,OAAOX,OAAA,CAAQY,SAAS;MACxE,MAAMhF,MAAA,GAAS;QACX,OAAO;QACP,OAAO;QACP,OAAO;UACH,OAAO8E,SAAA,CAAUnB,GAAA;UACjB,OAAOmB,SAAA,CAAUtB,GAAA;UACjB,KAAKsB,SAAA,CAAUlB,CAAA;UACf,KAAKkB,SAAA,CAAUjB;QACnB;MACJ;MACA,OAAO,MAAMjE,QAAA,CAASG,iBAAA,CAAkBC,MAAA,EAAQC,OAAA,EAASmE,OAAA,CAAQlE,UAAU;IAC/E,SAASjB,GAAA,EAAK;MACV,IAAIA,GAAA,YAAegG,SAAA,EAAW;QAC1B,MAAM,IAAI1G,KAAA,CAAM,oCAAoCU,GAAA,CAAIiE,OAAO,EAAE;MACrE,OAAO;QACH,MAAMjE,GAAA;MACV;IACJ;EACJ;EAEA,aAAoBiG,gBAAgBd,OAAA,EAA0C;IAC1E,IAAI;MACA,MAAMU,SAAA,GAAY,MAAMhE,MAAA,CAAOC,MAAA,CAAOgE,SAAA,CAAU,OAAOX,OAAA,CAAQY,SAAS;MACxE,OAAO,MAAMnD,YAAA,CAAYwB,4BAAA,CAA6ByB,SAAS;IACnE,SAAS7F,GAAA,EAAK;MACV,IAAIA,GAAA,YAAegG,SAAA,EAAW;QAC1B,MAAM,IAAI1G,KAAA,CAAM,8CAA8CU,GAAA,CAAIiE,OAAO,EAAE;MAC/E,OAAO;QACH,MAAMjE,GAAA;MACV;IACJ;EACJ;EAEA,aAAoBkG,iBAAA,EAA4C;IAC5D,OAAO,MAAMtE,MAAA,CAAOC,MAAA,CAAOC,MAAA,CAAOqE,WAAA,CAC9B;MACI7F,IAAA,EAAM;MACN8F,UAAA,EAAY;IAChB,GACA,OACA,CAAC,QAAQ,QAAQ,CACrB;EACJ;AACJ;AAAA;AAAA;AAAA;AAzLaxD,YAAA,CAmEKxB,eAAA,GAAmBiF,KAAA,IAAsB;EACnD,OAAOjE,QAAA,CAASiE,KAAK,EAAElD,OAAA,CAAQ,MAAM,EAAE,EAAEA,OAAA,CAAQ,OAAO,GAAG,EAAEA,OAAA,CAAQ,OAAO,GAAG;AACnF;AArEG,IAAMhC,WAAA,GAANyB,YAAA;;;ACRA,IAAM0D,KAAA,GAAN,MAAyC;EAKrC5G,YAA+BC,KAAA,EAAe;IAAf,KAAAA,KAAA,GAAAA,KAAA;IAFtC,KAAiB4G,UAAA,GAAyC,EAAC;IAGvD,KAAKC,OAAA,GAAU,IAAIhH,MAAA,CAAO,UAAU,KAAKG,KAAK,IAAI;EACtD;EAEO8G,WAAWC,EAAA,EAAqC;IACnD,KAAKH,UAAA,CAAWI,IAAA,CAAKD,EAAE;IACvB,OAAO,MAAM,KAAKE,aAAA,CAAcF,EAAE;EACtC;EAEOE,cAAcF,EAAA,EAA+B;IAChD,MAAMG,GAAA,GAAM,KAAKN,UAAA,CAAWO,WAAA,CAAYJ,EAAE;IAC1C,IAAIG,GAAA,IAAO,GAAG;MACV,KAAKN,UAAA,CAAWQ,MAAA,CAAOF,GAAA,EAAK,CAAC;IACjC;EACJ;EAEA,MAAaG,MAAA,GAASC,EAAA,EAA8B;IAChD,KAAKT,OAAA,CAAQ7H,KAAA,CAAM,UAAU,GAAGsI,EAAE;IAClC,WAAWP,EAAA,IAAM,KAAKH,UAAA,EAAY;MAC9B,MAAMG,EAAA,CAAG,GAAGO,EAAE;IAClB;EACJ;AACJ;;;AClBO,IAAMC,UAAA,GAAN,MAAiB;EAAA;AAAA;AAAA;AAAA;AAAA;EAMpB,OAAOC,OAAO;IAAE,GAAGC;EAAS,GAA6C;IA5B7E,IAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA;IA6BQ,IAAIH,QAAA,CAASI,KAAA,IAAS,MAClBJ,QAAA,CAASI,KAAA,IAAQH,EAAA,IAAC,KAAK,KAAK,KAAK,GAAG,EAAEI,IAAA,CAAKD,KAAA,IAASA,KAAA,IAAS5F,MAAA,CAAO8F,UAAA,GAAa,KAAK,MAArE,OAAAL,EAAA,GAA0E;IAC/F,CAAAC,EAAA,GAAAF,QAAA,CAASO,IAAA,KAAT,OAAAL,EAAA,GAAAF,QAAA,CAASO,IAAA,GAASnC,IAAA,CAAKoC,GAAA,CAAI,GAAGpC,IAAA,CAAKqC,KAAA,CAAMjG,MAAA,CAAOkG,OAAA,IAAWlG,MAAA,CAAO8F,UAAA,GAAaN,QAAA,CAASI,KAAA,IAAS,CAAC,CAAC;IACnG,IAAIJ,QAAA,CAASW,MAAA,IAAU,MACnB,CAAAR,EAAA,GAAAH,QAAA,CAASY,GAAA,KAAT,OAAAT,EAAA,GAAAH,QAAA,CAASY,GAAA,GAAQxC,IAAA,CAAKoC,GAAA,CAAI,GAAGpC,IAAA,CAAKqC,KAAA,CAAMjG,MAAA,CAAOqG,OAAA,IAAWrG,MAAA,CAAOsG,WAAA,GAAcd,QAAA,CAASW,MAAA,IAAU,CAAC,CAAC;IACxG,OAAOX,QAAA;EACX;EAEA,OAAOe,UAAUf,QAAA,EAAuC;IACpD,OAAOhH,MAAA,CAAOgI,OAAA,CAAQhB,QAAQ,EACzBiB,MAAA,CAAO,CAAC,GAAGhJ,KAAK,MAAMA,KAAA,IAAS,IAAI,EACnCkD,GAAA,CAAI,CAAC,CAAC+F,GAAA,EAAKjJ,KAAK,MAAM,GAAGiJ,GAAG,IAAI,OAAOjJ,KAAA,KAAU,YAAYA,KAAA,GAAkBA,KAAA,GAAQ,QAAQ,IAAI,EAAE,EACrGsD,IAAA,CAAK,GAAG;EACjB;AACJ;;;AClCO,IAAM4F,KAAA,GAAN,MAAMC,MAAA,SAAclC,KAAA,CAAc;EAAlC5G,YAAA;IAAA,SAAA+I,SAAA;IACH,KAAmBjC,OAAA,GAAU,IAAIhH,MAAA,CAAO,UAAU,KAAKG,KAAK,IAAI;IAChE,KAAQ+I,YAAA,GAAsD;IAC9D,KAAQC,WAAA,GAAc;IAyCtB,KAAUC,SAAA,GAAY,MAAY;MAC9B,MAAMC,IAAA,GAAO,KAAKF,WAAA,GAAcH,MAAA,CAAMM,YAAA,CAAa;MACnD,KAAKtC,OAAA,CAAQ7H,KAAA,CAAM,sBAAsBkK,IAAI;MAE7C,IAAI,KAAKF,WAAA,IAAeH,MAAA,CAAMM,YAAA,CAAa,GAAG;QAC1C,KAAKC,MAAA,CAAO;QACZ,KAAK,MAAM/B,KAAA,CAAM;MACrB;IACJ;EAAA;EAAA;EA9CA,OAAc8B,aAAA,EAAuB;IACjC,OAAOtD,IAAA,CAAKC,KAAA,CAAMC,IAAA,CAAKC,GAAA,CAAI,IAAI,GAAI;EACvC;EAEOqD,KAAKC,iBAAA,EAAiC;IACzC,MAAMC,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,MAAM;IACzCgJ,iBAAA,GAAoBzD,IAAA,CAAKoC,GAAA,CAAIpC,IAAA,CAAKC,KAAA,CAAMwD,iBAAiB,GAAG,CAAC;IAC7D,MAAME,UAAA,GAAaX,MAAA,CAAMM,YAAA,CAAa,IAAIG,iBAAA;IAC1C,IAAI,KAAKE,UAAA,KAAeA,UAAA,IAAc,KAAKT,YAAA,EAAc;MAErDQ,OAAA,CAAOvK,KAAA,CAAM,wDAAwD,KAAKwK,UAAU;MACpF;IACJ;IAEA,KAAKJ,MAAA,CAAO;IAEZG,OAAA,CAAOvK,KAAA,CAAM,kBAAkBsK,iBAAiB;IAChD,KAAKN,WAAA,GAAcQ,UAAA;IAKnB,MAAMC,sBAAA,GAAyB5D,IAAA,CAAK6D,GAAA,CAAIJ,iBAAA,EAAmB,CAAC;IAC5D,KAAKP,YAAA,GAAeY,WAAA,CAAY,KAAKV,SAAA,EAAWQ,sBAAA,GAAyB,GAAI;EACjF;EAEA,IAAWD,WAAA,EAAqB;IAC5B,OAAO,KAAKR,WAAA;EAChB;EAEOI,OAAA,EAAe;IAClB,KAAKvC,OAAA,CAAQvG,MAAA,CAAO,QAAQ;IAC5B,IAAI,KAAKyI,YAAA,EAAc;MACnBa,aAAA,CAAc,KAAKb,YAAY;MAC/B,KAAKA,YAAA,GAAe;IACxB;EACJ;AAWJ;;;ACxDO,IAAMc,QAAA,GAAN,MAAe;EAClB,OAAcC,WAAWzE,GAAA,EAAa0E,YAAA,GAAqC,SAA0B;IACjG,IAAI,CAAC1E,GAAA,EAAK,MAAM,IAAIgB,SAAA,CAAU,aAAa;IAE3C,MAAM2D,SAAA,GAAY,IAAIC,GAAA,CAAI5E,GAAA,EAAK,kBAAkB;IACjD,MAAM6E,MAAA,GAASF,SAAA,CAAUD,YAAA,KAAiB,aAAa,SAAS,QAAQ;IACxE,OAAO,IAAII,eAAA,CAAgBD,MAAA,CAAOE,KAAA,CAAM,CAAC,CAAC;EAC9C;AACJ;AAKO,IAAMC,mBAAA,GAAsB;;;ACR5B,IAAMC,aAAA,GAAN,cAA4B3K,KAAA,CAAM;EAqB9BI,YACHE,IAAA,EAKgBsK,IAAA,EAClB;IAvCN,IAAA7C,EAAA,EAAAC,EAAA,EAAAC,EAAA;IAwCQ,MAAM3H,IAAA,CAAKuK,iBAAA,IAAqBvK,IAAA,CAAKd,KAAA,IAAS,EAAE;IAFhC,KAAAoL,IAAA,GAAAA,IAAA;IAzBpB;IAAA,KAAgB5J,IAAA,GAAe;IA6B3B,IAAI,CAACV,IAAA,CAAKd,KAAA,EAAO;MACbU,MAAA,CAAOV,KAAA,CAAM,iBAAiB,iBAAiB;MAC/C,MAAM,IAAIQ,KAAA,CAAM,iBAAiB;IACrC;IAEA,KAAKR,KAAA,GAAQc,IAAA,CAAKd,KAAA;IAClB,KAAKqL,iBAAA,IAAoB9C,EAAA,GAAAzH,IAAA,CAAKuK,iBAAA,KAAL,OAAA9C,EAAA,GAA0B;IACnD,KAAK+C,SAAA,IAAY9C,EAAA,GAAA1H,IAAA,CAAKwK,SAAA,KAAL,OAAA9C,EAAA,GAAkB;IAEnC,KAAK+C,KAAA,GAAQzK,IAAA,CAAK0K,SAAA;IAClB,KAAKC,aAAA,IAAgBhD,EAAA,GAAA3H,IAAA,CAAK2K,aAAA,KAAL,OAAAhD,EAAA,GAAsB;IAC3C,KAAKiD,SAAA,GAAY5K,IAAA,CAAK4K,SAAA;EAC1B;AACJ;;;AC/CO,IAAMC,YAAA,GAAN,cAA2BnL,KAAA,CAAM;EAI7BI,YAAYuE,OAAA,EAAkB;IACjC,MAAMA,OAAO;IAHjB;IAAA,KAAgB3D,IAAA,GAAe;EAI/B;AACJ;;;ACDO,IAAMoK,iBAAA,GAAN,MAAwB;EAOpBhL,YAAYE,IAAA,EAAqD;IANxE,KAAmB4G,OAAA,GAAU,IAAIhH,MAAA,CAAO,mBAAmB;IAE3D,KAAiBmL,cAAA,GAAiB,IAAIpC,KAAA,CAAM,uBAAuB;IACnE,KAAiBqC,aAAA,GAAgB,IAAIrC,KAAA,CAAM,sBAAsB;IAI7D,KAAKsC,kCAAA,GAAqCjL,IAAA,CAAKkL,iCAAA;EACnD;EAEA,MAAaC,KAAKC,SAAA,EAAgC;IAC9C,MAAM9B,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,MAAM;IAEzC,IAAI+K,SAAA,CAAUC,YAAA,IAAgBD,SAAA,CAAUE,UAAA,KAAe,QAAW;MAC9D,MAAMC,QAAA,GAAWH,SAAA,CAAUE,UAAA;MAC3BhC,OAAA,CAAOvK,KAAA,CAAM,6CAA6CwM,QAAQ;MAElE,IAAIA,QAAA,GAAW,GAAG;QAEd,IAAIC,QAAA,GAAWD,QAAA,GAAW,KAAKN,kCAAA;QAC/B,IAAIO,QAAA,IAAY,GAAG;UACfA,QAAA,GAAW;QACf;QAEAlC,OAAA,CAAOvK,KAAA,CAAM,0CAA0CyM,QAAA,EAAU,SAAS;QAC1E,KAAKT,cAAA,CAAe3B,IAAA,CAAKoC,QAAQ;MACrC,OACK;QACDlC,OAAA,CAAOvK,KAAA,CAAM,kEAAkE;QAC/E,KAAKgM,cAAA,CAAe5B,MAAA,CAAO;MAC/B;MAGA,MAAMsC,OAAA,GAAUF,QAAA,GAAW;MAC3BjC,OAAA,CAAOvK,KAAA,CAAM,yCAAyC0M,OAAA,EAAS,SAAS;MACxE,KAAKT,aAAA,CAAc5B,IAAA,CAAKqC,OAAO;IACnC,OACK;MACD,KAAKV,cAAA,CAAe5B,MAAA,CAAO;MAC3B,KAAK6B,aAAA,CAAc7B,MAAA,CAAO;IAC9B;EACJ;EAEA,MAAauC,OAAA,EAAwB;IACjC,KAAK9E,OAAA,CAAQ7H,KAAA,CAAM,gDAAgD;IACnE,KAAKgM,cAAA,CAAe5B,MAAA,CAAO;IAC3B,KAAK6B,aAAA,CAAc7B,MAAA,CAAO;EAC9B;EAAA;AAAA;AAAA;EAKOwC,uBAAuB7E,EAAA,EAAqC;IAC/D,OAAO,KAAKiE,cAAA,CAAelE,UAAA,CAAWC,EAAE;EAC5C;EAAA;AAAA;AAAA;EAIO8E,0BAA0B9E,EAAA,EAA+B;IAC5D,KAAKiE,cAAA,CAAe/D,aAAA,CAAcF,EAAE;EACxC;EAAA;AAAA;AAAA;EAKO+E,sBAAsB/E,EAAA,EAAqC;IAC9D,OAAO,KAAKkE,aAAA,CAAcnE,UAAA,CAAWC,EAAE;EAC3C;EAAA;AAAA;AAAA;EAIOgF,yBAAyBhF,EAAA,EAA+B;IAC3D,KAAKkE,aAAA,CAAchE,aAAA,CAAcF,EAAE;EACvC;AACJ;;;ACjFO,IAAMiF,kBAAA,GAAN,MAAyB;EAOrBjM,YACKkJ,SAAA,EACAgD,UAAA,EACR5G,GAAA,EACQ6G,kBAAA,EACAC,YAAA,EACV;IALU,KAAAlD,SAAA,GAAAA,SAAA;IACA,KAAAgD,UAAA,GAAAA,UAAA;IAEA,KAAAC,kBAAA,GAAAA,kBAAA;IACA,KAAAC,YAAA,GAAAA,YAAA;IAXZ,KAAiBtF,OAAA,GAAU,IAAIhH,MAAA,CAAO,oBAAoB;IAG1D,KAAQuM,MAAA,GAAgD;IACxD,KAAQC,cAAA,GAAgC;IAmCxC,KAAQC,QAAA,GAAYzH,CAAA,IAAkC;MAClD,IAAIA,CAAA,CAAE0H,MAAA,KAAW,KAAKC,aAAA,IAClB3H,CAAA,CAAE4H,MAAA,KAAW,KAAKC,MAAA,CAAOC,aAAA,EAC3B;QACE,IAAI9H,CAAA,CAAEd,IAAA,KAAS,SAAS;UACpB,KAAK8C,OAAA,CAAQ1H,KAAA,CAAM,4CAA4C;UAC/D,IAAI,KAAKgN,YAAA,EAAc;YACnB,KAAKS,IAAA,CAAK;UACd;QACJ,WACS/H,CAAA,CAAEd,IAAA,KAAS,WAAW;UAC3B,KAAK8C,OAAA,CAAQ7H,KAAA,CAAM,8CAA8C;UACjE,KAAK4N,IAAA,CAAK;UACV,KAAK,KAAK3D,SAAA,CAAU;QACxB,OACK;UACD,KAAKpC,OAAA,CAAQ7H,KAAA,CAAM6F,CAAA,CAAEd,IAAA,GAAO,uCAAuC;QACvE;MACJ;IACJ;IA7CI,MAAMiG,SAAA,GAAY,IAAIC,GAAA,CAAI5E,GAAG;IAC7B,KAAKmH,aAAA,GAAgBxC,SAAA,CAAUuC,MAAA;IAE/B,KAAKG,MAAA,GAASzK,MAAA,CAAO4K,QAAA,CAASC,aAAA,CAAc,QAAQ;IAGpD,KAAKJ,MAAA,CAAOK,KAAA,CAAMC,UAAA,GAAa;IAC/B,KAAKN,MAAA,CAAOK,KAAA,CAAME,QAAA,GAAW;IAC7B,KAAKP,MAAA,CAAOK,KAAA,CAAM/E,IAAA,GAAO;IACzB,KAAK0E,MAAA,CAAOK,KAAA,CAAM1E,GAAA,GAAM;IACxB,KAAKqE,MAAA,CAAO7E,KAAA,GAAQ;IACpB,KAAK6E,MAAA,CAAOtE,MAAA,GAAS;IACrB,KAAKsE,MAAA,CAAOQ,GAAA,GAAMlD,SAAA,CAAUmD,IAAA;EAChC;EAEO/B,KAAA,EAAsB;IACzB,OAAO,IAAIgC,OAAA,CAAeC,OAAA,IAAY;MAClC,KAAKX,MAAA,CAAOY,MAAA,GAAS,MAAM;QACvBD,OAAA,CAAQ;MACZ;MAEApL,MAAA,CAAO4K,QAAA,CAASU,IAAA,CAAKC,WAAA,CAAY,KAAKd,MAAM;MAC5CzK,MAAA,CAAOwL,gBAAA,CAAiB,WAAW,KAAKnB,QAAA,EAAU,KAAK;IAC3D,CAAC;EACL;EAuBOoB,MAAM9C,aAAA,EAA6B;IACtC,IAAI,KAAKyB,cAAA,KAAmBzB,aAAA,EAAe;MACvC;IACJ;IAEA,KAAK/D,OAAA,CAAQvG,MAAA,CAAO,OAAO;IAE3B,KAAKsM,IAAA,CAAK;IAEV,KAAKP,cAAA,GAAiBzB,aAAA;IAEtB,MAAM+C,IAAA,GAAOA,CAAA,KAAM;MACf,IAAI,CAAC,KAAKjB,MAAA,CAAOC,aAAA,IAAiB,CAAC,KAAKN,cAAA,EAAgB;QACpD;MACJ;MAEA,KAAKK,MAAA,CAAOC,aAAA,CAAciB,WAAA,CAAY,KAAK3B,UAAA,GAAa,MAAM,KAAKI,cAAA,EAAgB,KAAKG,aAAa;IACzG;IAGAmB,IAAA,CAAK;IAGL,KAAKvB,MAAA,GAASzC,WAAA,CAAYgE,IAAA,EAAM,KAAKzB,kBAAA,GAAqB,GAAI;EAClE;EAEOU,KAAA,EAAa;IAChB,KAAK/F,OAAA,CAAQvG,MAAA,CAAO,MAAM;IAC1B,KAAK+L,cAAA,GAAiB;IAEtB,IAAI,KAAKD,MAAA,EAAQ;MAEbxC,aAAA,CAAc,KAAKwC,MAAM;MACzB,KAAKA,MAAA,GAAS;IAClB;EACJ;AACJ;;;ACjGO,IAAMyB,kBAAA,GAAN,MAA4C;EAA5C9N,YAAA;IACH,KAAiB8G,OAAA,GAAU,IAAIhH,MAAA,CAAO,oBAAoB;IAC1D,KAAQiO,KAAA,GAAgC,CAAC;EAAA;EAElCC,MAAA,EAAc;IACjB,KAAKlH,OAAA,CAAQvG,MAAA,CAAO,OAAO;IAC3B,KAAKwN,KAAA,GAAQ,CAAC;EAClB;EAEOE,QAAQrF,GAAA,EAAqB;IAChC,KAAK9B,OAAA,CAAQvG,MAAA,CAAO,YAAYqI,GAAG,IAAI;IACvC,OAAO,KAAKmF,KAAA,CAAMnF,GAAG;EACzB;EAEOsF,QAAQtF,GAAA,EAAajJ,KAAA,EAAqB;IAC7C,KAAKmH,OAAA,CAAQvG,MAAA,CAAO,YAAYqI,GAAG,IAAI;IACvC,KAAKmF,KAAA,CAAMnF,GAAG,IAAIjJ,KAAA;EACtB;EAEOwO,WAAWvF,GAAA,EAAmB;IACjC,KAAK9B,OAAA,CAAQvG,MAAA,CAAO,eAAeqI,GAAG,IAAI;IAC1C,OAAO,KAAKmF,KAAA,CAAMnF,GAAG;EACzB;EAEA,IAAWwF,OAAA,EAAiB;IACxB,OAAO1N,MAAA,CAAO2N,mBAAA,CAAoB,KAAKN,KAAK,EAAEK,MAAA;EAClD;EAEOxF,IAAI0F,KAAA,EAAuB;IAC9B,OAAO5N,MAAA,CAAO2N,mBAAA,CAAoB,KAAKN,KAAK,EAAEO,KAAK;EACvD;AACJ;;;ACvCO,IAAMC,cAAA,GAAN,cAA6B3O,KAAA,CAAM;EAK/BI,YAAY0F,KAAA,EAAenB,OAAA,EAAkB;IAChD,MAAMA,OAAO;IAJjB;IAAA,KAAgB3D,IAAA,GAAe;IAK3B,KAAK8E,KAAA,GAAQA,KAAA;EACjB;AACJ;;;AC2BO,IAAM8I,WAAA,GAAN,MAAkB;EAKdxO,YACHyO,sBAAA,GAAmC,EAAC,EAC5BC,WAAA,GAAiC,MACjCC,aAAA,GAA6C,CAAC,GACxD;IAFU,KAAAD,WAAA,GAAAA,WAAA;IACA,KAAAC,aAAA,GAAAA,aAAA;IAPZ,KAAiB7H,OAAA,GAAU,IAAIhH,MAAA,CAAO,aAAa;IAEnD,KAAQ8O,aAAA,GAA0B,EAAC;IAO/B,KAAKA,aAAA,CAAc3H,IAAA,CAAK,GAAGwH,sBAAA,EAAwB,kBAAkB;IACrE,IAAIC,WAAA,EAAa;MACb,KAAKE,aAAA,CAAc3H,IAAA,CAAK,iBAAiB;IAC7C;EACJ;EAEA,MAAgB4H,iBAAiBlI,KAAA,EAAoB2C,IAAA,GAAoD,CAAC,GAAG;IACzG,MAAM;MAAEwF,gBAAA;MAAkB,GAAGC;IAAU,IAAIzF,IAAA;IAC3C,IAAI,CAACwF,gBAAA,EAAkB;MACnB,OAAO,MAAME,KAAA,CAAMrI,KAAA,EAAOoI,SAAS;IACvC;IAEA,MAAME,UAAA,GAAa,IAAIC,eAAA,CAAgB;IACvC,MAAMC,SAAA,GAAYC,UAAA,CAAW,MAAMH,UAAA,CAAWI,KAAA,CAAM,GAAGP,gBAAA,GAAmB,GAAI;IAE9E,IAAI;MACA,MAAMQ,QAAA,GAAW,MAAMN,KAAA,CAAMrI,KAAA,EAAO;QAChC,GAAG2C,IAAA;QACHiG,MAAA,EAAQN,UAAA,CAAWM;MACvB,CAAC;MACD,OAAOD,QAAA;IACX,SACOhP,GAAA,EAAK;MACR,IAAIA,GAAA,YAAekP,YAAA,IAAgBlP,GAAA,CAAIM,IAAA,KAAS,cAAc;QAC1D,MAAM,IAAImK,YAAA,CAAa,mBAAmB;MAC9C;MACA,MAAMzK,GAAA;IACV,UACA;MACImP,YAAA,CAAaN,SAAS;IAC1B;EACJ;EAEA,MAAaO,QAAQpK,GAAA,EAAa;IAC9BnE,KAAA;IACAwO,WAAA;IACAb;EACJ,IAAiB,CAAC,GAAqC;IACnD,MAAMtF,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,SAAS;IAC5C,MAAMqP,OAAA,GAAuB;MACzB,UAAU,KAAKhB,aAAA,CAAc3L,IAAA,CAAK,IAAI;IAC1C;IACA,IAAI9B,KAAA,EAAO;MACPqI,OAAA,CAAOvK,KAAA,CAAM,4CAA4C;MACzD2Q,OAAA,CAAQ,eAAe,IAAI,YAAYzO,KAAA;IAC3C;IAEA,KAAK0O,mBAAA,CAAoBD,OAAO;IAEhC,IAAIN,QAAA;IACJ,IAAI;MACA9F,OAAA,CAAOvK,KAAA,CAAM,QAAQqG,GAAG;MACxBgK,QAAA,GAAW,MAAM,KAAKT,gBAAA,CAAiBvJ,GAAA,EAAK;QAAE9E,MAAA,EAAQ;QAAOoP,OAAA;QAASd,gBAAA;QAAkBa;MAAY,CAAC;IACzG,SACOrP,GAAA,EAAK;MACRkJ,OAAA,CAAOpK,KAAA,CAAM,eAAe;MAC5B,MAAMkB,GAAA;IACV;IAEAkJ,OAAA,CAAOvK,KAAA,CAAM,kCAAkCqQ,QAAA,CAASQ,MAAM;IAC9D,MAAMC,WAAA,GAAcT,QAAA,CAASM,OAAA,CAAQI,GAAA,CAAI,cAAc;IACvD,IAAID,WAAA,IAAe,CAAC,KAAKnB,aAAA,CAAc7G,IAAA,CAAKkI,IAAA,IAAQF,WAAA,CAAYG,UAAA,CAAWD,IAAI,CAAC,GAAG;MAC/EzG,OAAA,CAAOnJ,KAAA,CAAM,IAAIT,KAAA,CAAM,kCAAmCmQ,WAAA,WAAAA,WAAA,GAAe,WAAY,eAAezK,GAAG,EAAE,CAAC;IAC9G;IACA,IAAIgK,QAAA,CAASa,EAAA,IAAM,KAAKzB,WAAA,KAAeqB,WAAA,oBAAAA,WAAA,CAAaG,UAAA,CAAW,qBAAoB;MAC/E,OAAO,MAAM,KAAKxB,WAAA,CAAY,MAAMY,QAAA,CAASc,IAAA,CAAK,CAAC;IACvD;IACA,IAAIC,IAAA;IACJ,IAAI;MACAA,IAAA,GAAO,MAAMf,QAAA,CAASe,IAAA,CAAK;IAC/B,SACO/P,GAAA,EAAK;MACRkJ,OAAA,CAAOpK,KAAA,CAAM,+BAA+BkB,GAAG;MAC/C,IAAIgP,QAAA,CAASa,EAAA,EAAI,MAAM7P,GAAA;MACvB,MAAM,IAAIV,KAAA,CAAM,GAAG0P,QAAA,CAASgB,UAAU,KAAKhB,QAAA,CAASQ,MAAM,GAAG;IACjE;IACA,IAAI,CAACR,QAAA,CAASa,EAAA,EAAI;MACd3G,OAAA,CAAOpK,KAAA,CAAM,sBAAsBiR,IAAI;MACvC,IAAIA,IAAA,CAAKjR,KAAA,EAAO;QACZ,MAAM,IAAImL,aAAA,CAAc8F,IAAI;MAChC;MACA,MAAM,IAAIzQ,KAAA,CAAM,GAAG0P,QAAA,CAASgB,UAAU,KAAKhB,QAAA,CAASQ,MAAM,MAAMjO,IAAA,CAAKC,SAAA,CAAUuO,IAAI,CAAC,EAAE;IAC1F;IACA,OAAOA,IAAA;EACX;EAEA,MAAaE,SAASjL,GAAA,EAAa;IAC/BkI,IAAA;IACAgD,SAAA;IACA1B,gBAAA;IACA2B,eAAA;IACAC;EACJ,GAAmD;IAC/C,MAAMlH,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,UAAU;IAC7C,MAAMqP,OAAA,GAAuB;MACzB,UAAU,KAAKhB,aAAA,CAAc3L,IAAA,CAAK,IAAI;MACtC,gBAAgB;MAChB,GAAGyN;IACP;IACA,IAAIF,SAAA,KAAc,QAAW;MACzBZ,OAAA,CAAQ,eAAe,IAAI,WAAWY,SAAA;IAC1C;IAEA,KAAKX,mBAAA,CAAoBD,OAAO;IAEhC,IAAIN,QAAA;IACJ,IAAI;MACA9F,OAAA,CAAOvK,KAAA,CAAM,QAAQqG,GAAG;MACxBgK,QAAA,GAAW,MAAM,KAAKT,gBAAA,CAAiBvJ,GAAA,EAAK;QAAE9E,MAAA,EAAQ;QAAQoP,OAAA;QAASpC,IAAA;QAAMsB,gBAAA;QAAkBa,WAAA,EAAac;MAAgB,CAAC;IACjI,SACOnQ,GAAA,EAAK;MACRkJ,OAAA,CAAOpK,KAAA,CAAM,eAAe;MAC5B,MAAMkB,GAAA;IACV;IAEAkJ,OAAA,CAAOvK,KAAA,CAAM,kCAAkCqQ,QAAA,CAASQ,MAAM;IAC9D,MAAMC,WAAA,GAAcT,QAAA,CAASM,OAAA,CAAQI,GAAA,CAAI,cAAc;IACvD,IAAID,WAAA,IAAe,CAAC,KAAKnB,aAAA,CAAc7G,IAAA,CAAKkI,IAAA,IAAQF,WAAA,CAAYG,UAAA,CAAWD,IAAI,CAAC,GAAG;MAC/E,MAAM,IAAIrQ,KAAA,CAAM,kCAAmCmQ,WAAA,WAAAA,WAAA,GAAe,WAAY,eAAezK,GAAG,EAAE;IACtG;IAEA,MAAMqL,YAAA,GAAe,MAAMrB,QAAA,CAASc,IAAA,CAAK;IAEzC,IAAIC,IAAA,GAAgC,CAAC;IACrC,IAAIM,YAAA,EAAc;MACd,IAAI;QACAN,IAAA,GAAOxO,IAAA,CAAK+O,KAAA,CAAMD,YAAY;MAClC,SACOrQ,GAAA,EAAK;QACRkJ,OAAA,CAAOpK,KAAA,CAAM,+BAA+BkB,GAAG;QAC/C,IAAIgP,QAAA,CAASa,EAAA,EAAI,MAAM7P,GAAA;QACvB,MAAM,IAAIV,KAAA,CAAM,GAAG0P,QAAA,CAASgB,UAAU,KAAKhB,QAAA,CAASQ,MAAM,GAAG;MACjE;IACJ;IAEA,IAAI,CAACR,QAAA,CAASa,EAAA,EAAI;MACd3G,OAAA,CAAOpK,KAAA,CAAM,sBAAsBiR,IAAI;MACvC,IAAIf,QAAA,CAASM,OAAA,CAAQiB,GAAA,CAAI,YAAY,GAAG;QACpC,MAAMnL,KAAA,GAAQ4J,QAAA,CAASM,OAAA,CAAQI,GAAA,CAAI,YAAY;QAC/C,MAAM,IAAIzB,cAAA,CAAe7I,KAAA,EAAO,GAAG7D,IAAA,CAAKC,SAAA,CAAUuO,IAAI,CAAC,EAAE;MAC7D;MACA,IAAIA,IAAA,CAAKjR,KAAA,EAAO;QACZ,MAAM,IAAImL,aAAA,CAAc8F,IAAA,EAAM7C,IAAI;MACtC;MACA,MAAM,IAAI5N,KAAA,CAAM,GAAG0P,QAAA,CAASgB,UAAU,KAAKhB,QAAA,CAASQ,MAAM,MAAMjO,IAAA,CAAKC,SAAA,CAAUuO,IAAI,CAAC,EAAE;IAC1F;IAEA,OAAOA,IAAA;EACX;EAEQR,oBACJD,OAAA,EACI;IACJ,MAAMpG,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,oBAAoB;IACvD,MAAMuQ,UAAA,GAAapQ,MAAA,CAAOqQ,IAAA,CAAK,KAAKpC,aAAa;IACjD,MAAMqC,gBAAA,GAAmB,CACrB,UACA,eACJ;IACA,MAAMC,eAAA,GAAkB,CACpB,gBACJ;IACA,IAAIH,UAAA,CAAW1C,MAAA,KAAW,GAAG;MACzB;IACJ;IACA0C,UAAA,CAAWI,OAAA,CAASC,UAAA,IAAe;MAC/B,IAAIH,gBAAA,CAAiBI,QAAA,CAASD,UAAA,CAAWE,iBAAA,CAAkB,CAAC,GAAG;QAC3D7H,OAAA,CAAOrK,IAAA,CAAK,qCAAqCgS,UAAA,EAAYH,gBAAgB;QAC7E;MACJ;MACA,IAAIC,eAAA,CAAgBG,QAAA,CAASD,UAAA,CAAWE,iBAAA,CAAkB,CAAC,KACvD3Q,MAAA,CAAOqQ,IAAA,CAAKnB,OAAO,EAAEwB,QAAA,CAASD,UAAU,GAAG;QAC3C3H,OAAA,CAAOrK,IAAA,CAAK,kCAAkCgS,UAAA,EAAYF,eAAe;QACzE;MACJ;MACA,MAAMK,OAAA,GAAW,OAAO,KAAK3C,aAAA,CAAcwC,UAAU,MAAM,aACtD,KAAKxC,aAAA,CAAcwC,UAAU,EAAiB,IAC/C,KAAKxC,aAAA,CAAcwC,UAAU;MACjC,IAAIG,OAAA,IAAWA,OAAA,KAAY,IAAI;QAC3B1B,OAAA,CAAQuB,UAAU,IAAIG,OAAA;MAC1B;IACJ,CAAC;EACL;AACJ;;;ACzNO,IAAMC,eAAA,GAAN,MAAsB;EAUlBvR,YAA6BwR,SAAA,EAAoC;IAApC,KAAAA,SAAA,GAAAA,SAAA;IATpC,KAAiB1K,OAAA,GAAU,IAAIhH,MAAA,CAAO,iBAAiB;IAKvD,KAAQ2R,YAAA,GAAoC;IAC5C,KAAQC,SAAA,GAA0C;IAI9C,KAAKC,YAAA,GAAe,KAAKH,SAAA,CAAUI,WAAA;IACnC,KAAKC,YAAA,GAAe,IAAIrD,WAAA,CACpB,CAAC,0BAA0B,GAC3B,MACA,KAAKgD,SAAA,CAAUd,YACnB;IACA,IAAI,KAAKc,SAAA,CAAUM,WAAA,EAAa;MAC5B,KAAKhL,OAAA,CAAQ7H,KAAA,CAAM,iCAAiC;MACpD,KAAKwS,YAAA,GAAe,KAAKD,SAAA,CAAUM,WAAA;IACvC;IAEA,IAAI,KAAKN,SAAA,CAAUO,QAAA,EAAU;MACzB,KAAKjL,OAAA,CAAQ7H,KAAA,CAAM,8BAA8B;MACjD,KAAKyS,SAAA,GAAY,KAAKF,SAAA,CAAUO,QAAA;IACpC;IAEA,IAAI,KAAKP,SAAA,CAAUQ,uBAAA,EAAyB;MACxC,KAAKlL,OAAA,CAAQ7H,KAAA,CAAM,6CAA6C;MAChE,KAAKgT,wBAAA,GAA2B,KAAKT,SAAA,CAAUQ,uBAAA;IACnD;EACJ;EAEOE,iBAAA,EAAyB;IAC5B,KAAKT,YAAA,GAAe;EACxB;EAEA,MAAaU,YAAA,EAA8C;IACvD,MAAM3I,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,aAAa;IAChD,IAAI,KAAKmR,SAAA,EAAW;MAChBlI,OAAA,CAAOvK,KAAA,CAAM,qBAAqB;MAClC,OAAO,KAAKyS,SAAA;IAChB;IAEA,IAAI,CAAC,KAAKC,YAAA,EAAc;MACpBnI,OAAA,CAAOnJ,KAAA,CAAM,IAAIT,KAAA,CAAM,oDAAoD,CAAC;MAE5E,MAAM;IACV;IAEA4J,OAAA,CAAOvK,KAAA,CAAM,yBAAyB,KAAK0S,YAAY;IACvD,MAAMI,QAAA,GAAW,MAAM,KAAKF,YAAA,CAAanC,OAAA,CAAQ,KAAKiC,YAAA,EAAc;MAAEhC,WAAA,EAAa,KAAKsC,wBAAA;MAA0BnD,gBAAA,EAAkB,KAAK0C,SAAA,CAAUY;IAAwB,CAAC;IAE5K5I,OAAA,CAAOvK,KAAA,CAAM,wCAAwC;IACrD,KAAKyS,SAAA,GAAYhR,MAAA,CAAO2R,MAAA,CAAO,CAAC,GAAGN,QAAA,EAAU,KAAKP,SAAA,CAAUc,YAAY;IACxE,OAAO,KAAKZ,SAAA;EAChB;EAEOa,UAAA,EAA6B;IAChC,OAAO,KAAKC,oBAAA,CAAqB,QAAQ;EAC7C;EAEOC,yBAAA,EAA4C;IAC/C,OAAO,KAAKD,oBAAA,CAAqB,wBAAwB;EAC7D;EAEOE,oBAAA,EAAuC;IAC1C,OAAO,KAAKF,oBAAA,CAAqB,mBAAmB;EACxD;EAIOG,iBAAiBC,QAAA,GAAW,MAAmC;IAClE,OAAO,KAAKJ,oBAAA,CAAqB,kBAAkBI,QAAQ;EAC/D;EAEOC,sBAAA,EAAqD;IACxD,OAAO,KAAKL,oBAAA,CAAqB,wBAAwB,IAAI;EACjE;EAEOM,sBAAA,EAAqD;IACxD,OAAO,KAAKN,oBAAA,CAAqB,wBAAwB,IAAI;EACjE;EAIOO,sBAAsBH,QAAA,GAAW,MAAmC;IACvE,OAAO,KAAKJ,oBAAA,CAAqB,uBAAuBI,QAAQ;EACpE;EAIOI,gBAAgBJ,QAAA,GAAW,MAAmC;IACjE,OAAO,KAAKJ,oBAAA,CAAqB,YAAYI,QAAQ;EACzD;EAEA,MAAgBJ,qBAAqB5R,IAAA,EAA0BgS,QAAA,GAAS,OAAyD;IAC7H,MAAMpJ,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,yBAAyBK,IAAI,IAAI;IAEpE,MAAMmR,QAAA,GAAW,MAAM,KAAKI,WAAA,CAAY;IACxC3I,OAAA,CAAOvK,KAAA,CAAM,UAAU;IAEvB,IAAI8S,QAAA,CAASnR,IAAI,MAAM,QAAW;MAC9B,IAAIgS,QAAA,KAAa,MAAM;QACnBpJ,OAAA,CAAOrK,IAAA,CAAK,6CAA6C;QACzD,OAAO;MACX;MAEAqK,OAAA,CAAOnJ,KAAA,CAAM,IAAIT,KAAA,CAAM,wCAAwCgB,IAAI,CAAC;IACxE;IAEA,OAAOmR,QAAA,CAASnR,IAAI;EACxB;EAEA,MAAaqS,eAAA,EAA+C;IACxD,MAAMzJ,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,gBAAgB;IACnD,IAAI,KAAKkR,YAAA,EAAc;MACnBjI,OAAA,CAAOvK,KAAA,CAAM,kCAAkC;MAC/C,OAAO,KAAKwS,YAAA;IAChB;IAEA,MAAMyB,QAAA,GAAW,MAAM,KAAKF,eAAA,CAAgB,KAAK;IACjDxJ,OAAA,CAAOvK,KAAA,CAAM,gBAAgBiU,QAAQ;IAErC,MAAMC,MAAA,GAAS,MAAM,KAAKtB,YAAA,CAAanC,OAAA,CAAQwD,QAAA,EAAU;MAAEpE,gBAAA,EAAkB,KAAK0C,SAAA,CAAUY;IAAwB,CAAC;IACrH5I,OAAA,CAAOvK,KAAA,CAAM,eAAekU,MAAM;IAElC,IAAI,CAACC,KAAA,CAAMC,OAAA,CAAQF,MAAA,CAAOpC,IAAI,GAAG;MAC7BvH,OAAA,CAAOnJ,KAAA,CAAM,IAAIT,KAAA,CAAM,wBAAwB,CAAC;MAEhD,MAAM;IACV;IAEA,KAAK6R,YAAA,GAAe0B,MAAA,CAAOpC,IAAA;IAC3B,OAAO,KAAKU,YAAA;EAChB;AACJ;;;AC1IO,IAAM6B,oBAAA,GAAN,MAAiD;EAM7CtT,YAAY;IACfe,MAAA,GAAS;IACTwS,KAAA,GAAQC;EACZ,IAAyD,CAAC,GAAG;IAR7D,KAAiB1M,OAAA,GAAU,IAAIhH,MAAA,CAAO,sBAAsB;IASxD,KAAK2T,MAAA,GAASF,KAAA;IACd,KAAKG,OAAA,GAAU3S,MAAA;EACnB;EAEA,MAAa4S,IAAI/K,GAAA,EAAajJ,KAAA,EAA8B;IACxD,KAAKmH,OAAA,CAAQvG,MAAA,CAAO,QAAQqI,GAAG,IAAI;IAEnCA,GAAA,GAAM,KAAK8K,OAAA,GAAU9K,GAAA;IACrB,MAAM,KAAK6K,MAAA,CAAOvF,OAAA,CAAQtF,GAAA,EAAKjJ,KAAK;EACxC;EAEA,MAAaqQ,IAAIpH,GAAA,EAAqC;IAClD,KAAK9B,OAAA,CAAQvG,MAAA,CAAO,QAAQqI,GAAG,IAAI;IAEnCA,GAAA,GAAM,KAAK8K,OAAA,GAAU9K,GAAA;IACrB,MAAMqH,IAAA,GAAO,MAAM,KAAKwD,MAAA,CAAOxF,OAAA,CAAQrF,GAAG;IAC1C,OAAOqH,IAAA;EACX;EAEA,MAAa2D,OAAOhL,GAAA,EAAqC;IACrD,KAAK9B,OAAA,CAAQvG,MAAA,CAAO,WAAWqI,GAAG,IAAI;IAEtCA,GAAA,GAAM,KAAK8K,OAAA,GAAU9K,GAAA;IACrB,MAAMqH,IAAA,GAAO,MAAM,KAAKwD,MAAA,CAAOxF,OAAA,CAAQrF,GAAG;IAC1C,MAAM,KAAK6K,MAAA,CAAOtF,UAAA,CAAWvF,GAAG;IAChC,OAAOqH,IAAA;EACX;EAEA,MAAa4D,WAAA,EAAgC;IACzC,KAAK/M,OAAA,CAAQvG,MAAA,CAAO,YAAY;IAChC,MAAMuT,GAAA,GAAM,MAAM,KAAKL,MAAA,CAAOrF,MAAA;IAE9B,MAAM2C,IAAA,GAAO,EAAC;IACd,SAASzC,KAAA,GAAQ,GAAGA,KAAA,GAAQwF,GAAA,EAAKxF,KAAA,IAAS;MACtC,MAAM1F,GAAA,GAAM,MAAM,KAAK6K,MAAA,CAAO7K,GAAA,CAAI0F,KAAK;MACvC,IAAI1F,GAAA,IAAOA,GAAA,CAAImL,OAAA,CAAQ,KAAKL,OAAO,MAAM,GAAG;QACxC3C,IAAA,CAAK9J,IAAA,CAAK2B,GAAA,CAAIoL,MAAA,CAAO,KAAKN,OAAA,CAAQtF,MAAM,CAAC;MAC7C;IACJ;IACA,OAAO2C,IAAA;EACX;AACJ;;;ACpDA,IAAMkD,mBAAA,GAAsB;AAC5B,IAAMC,YAAA,GAAe;AACrB,IAAMC,2BAAA,GAA8B;AACpC,IAAMC,6BAAA,GAAgC,KAAK;AAiKpC,IAAMC,uBAAA,GAAN,MAA8B;EA+C1BrU,YAAY;IAAA;IAEfsU,SAAA;IAAW1C,WAAA;IAAaG,QAAA;IAAUD,WAAA;IAAaQ,YAAA;IAAA;IAE/ClO,SAAA;IAAWC,aAAA;IAAekQ,aAAA,GAAgBN,mBAAA;IAAqBO,KAAA,GAAQN,YAAA;IACvEO,YAAA;IAAcC,wBAAA;IACdC,qBAAA,GAAwBR,2BAAA;IAAA;IAExBS,MAAA;IAAQC,OAAA;IAASC,OAAA;IAASC,UAAA;IAAYC,UAAA;IAAYC,QAAA;IAAUC,aAAA;IAAA;IAE5DC,oBAAA,GAAuB;IACvBC,YAAA,GAAe;IACfhD,uBAAA;IACAiD,sBAAA,GAAyBjB,6BAAA;IACzBkB,mBAAA,GAAsB;MAAEC,KAAA,EAAO;IAAU;IACzCC,WAAA,GAAc;IAAA;IAEdC,UAAA;IACAC,iCAAA;IACA1D,uBAAA;IACA2D,wBAAA;IAAA;IAEAC,gBAAA,GAAmB,CAAC;IACpBC,gBAAA,GAAmB,CAAC;IACpBnF,YAAA,GAAe,CAAC;IAChBoF,IAAA;IACAC,uBAAA,GAA0B;EAC9B,GAAuB;IAvP3B,IAAApO,EAAA;IAyPQ,KAAK2M,SAAA,GAAYA,SAAA;IAEjB,IAAI1C,WAAA,EAAa;MACb,KAAKA,WAAA,GAAcA,WAAA;IACvB,OAAO;MACH,KAAKA,WAAA,GAAc0C,SAAA;MACnB,IAAIA,SAAA,EAAW;QACX,IAAI,CAAC,KAAK1C,WAAA,CAAYoE,QAAA,CAAS,GAAG,GAAG;UACjC,KAAKpE,WAAA,IAAe;QACxB;QACA,KAAKA,WAAA,IAAe;MACxB;IACJ;IAEA,KAAKG,QAAA,GAAWA,QAAA;IAChB,KAAKO,YAAA,GAAeA,YAAA;IACpB,KAAKR,WAAA,GAAcA,WAAA;IAEnB,KAAK1N,SAAA,GAAYA,SAAA;IACjB,KAAKC,aAAA,GAAgBA,aAAA;IACrB,KAAKkQ,aAAA,GAAgBA,aAAA;IACrB,KAAKC,KAAA,GAAQA,KAAA;IACb,KAAKC,YAAA,GAAeA,YAAA;IACpB,KAAKC,wBAAA,GAA2BA,wBAAA;IAChC,KAAKC,qBAAA,GAAwBA,qBAAA;IAE7B,KAAKC,MAAA,GAASA,MAAA;IACd,KAAKC,OAAA,GAAUA,OAAA;IACf,KAAKC,OAAA,GAAUA,OAAA;IACf,KAAKC,UAAA,GAAaA,UAAA;IAClB,KAAKC,UAAA,GAAaA,UAAA;IAClB,KAAKC,QAAA,GAAWA,QAAA;IAChB,KAAKC,aAAA,GAAgBA,aAAA;IAErB,KAAKC,oBAAA,GAAuBA,oBAAA,WAAAA,oBAAA,GAAwB;IACpD,KAAKC,YAAA,GAAe,CAAC,CAACA,YAAA;IACtB,KAAKC,sBAAA,GAAyBA,sBAAA;IAC9B,KAAKC,mBAAA,GAAsBA,mBAAA;IAC3B,KAAKS,uBAAA,GAA0BA,uBAAA;IAC/B,KAAKP,WAAA,GAAc,CAAC,CAACA,WAAA;IACrB,KAAKE,iCAAA,GAAoCA,iCAAA;IAEzC,KAAK1D,uBAAA,GAA0BA,uBAAA,GAA0BA,uBAAA,GAA0B;IACnF,KAAKI,uBAAA,GAA0BA,uBAAA;IAE/B,IAAIqD,UAAA,EAAY;MACZ,KAAKA,UAAA,GAAaA,UAAA;IACtB,OACK;MACD,MAAMlC,KAAA,GAAQ,OAAOrR,MAAA,KAAW,cAAcA,MAAA,CAAOsR,YAAA,GAAe,IAAI1F,kBAAA,CAAmB;MAC3F,KAAK2H,UAAA,GAAa,IAAInC,oBAAA,CAAqB;QAAEC;MAAM,CAAC;IACxD;IAEA,KAAKoC,wBAAA,GAA2BA,wBAAA;IAEhC,KAAKC,gBAAA,GAAmBA,gBAAA;IACxB,KAAKC,gBAAA,GAAmBA,gBAAA;IACxB,KAAKnF,YAAA,GAAeA,YAAA;IAEpB,KAAKoF,IAAA,GAAOA,IAAA;IACZ,IAAI,KAAKA,IAAA,IAAQ,GAACnO,EAAA,QAAKmO,IAAA,KAAL,gBAAAnO,EAAA,CAAW4L,KAAA,GAAO;MAChC,MAAM,IAAI3T,KAAA,CAAM,8CAA8C;IAClE;EACJ;AACJ;;;AC7SO,IAAMqW,eAAA,GAAN,MAAsB;EAIlBjW,YAA6BwR,SAAA,EACf0E,gBAAA,EACnB;IAFkC,KAAA1E,SAAA,GAAAA,SAAA;IACf,KAAA0E,gBAAA,GAAAA,gBAAA;IAJrB,KAAmBpP,OAAA,GAAU,IAAIhH,MAAA,CAAO,iBAAiB;IAgCzD,KAAUqW,iBAAA,GAAoB,MAAOxF,YAAA,IAA6C;MAC9E,MAAMnH,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,mBAAmB;MACtD,IAAI;QACA,MAAMe,OAAA,GAAUL,QAAA,CAASC,MAAA,CAAOyP,YAAY;QAC5CnH,OAAA,CAAOvK,KAAA,CAAM,yBAAyB;QAEtC,OAAOqC,OAAA;MACX,SAAShB,GAAA,EAAK;QACVkJ,OAAA,CAAOpK,KAAA,CAAM,4BAA4B;QACzC,MAAMkB,GAAA;MACV;IACJ;IArCI,KAAKuR,YAAA,GAAe,IAAIrD,WAAA,CACpB,QACA,KAAK2H,iBAAA,EACL,KAAK3E,SAAA,CAAUd,YACnB;EACJ;EAEA,MAAa0F,UAAUjV,KAAA,EAAmC;IACtD,MAAMqI,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,WAAW;IAC9C,IAAI,CAACY,KAAA,EAAO;MACR,KAAK2F,OAAA,CAAQzG,KAAA,CAAM,IAAIT,KAAA,CAAM,iBAAiB,CAAC;IACnD;IAEA,MAAM0F,GAAA,GAAM,MAAM,KAAK4Q,gBAAA,CAAiBxD,mBAAA,CAAoB;IAC5DlJ,OAAA,CAAOvK,KAAA,CAAM,oBAAoBqG,GAAG;IAEpC,MAAM+Q,MAAA,GAAS,MAAM,KAAKxE,YAAA,CAAanC,OAAA,CAAQpK,GAAA,EAAK;MAChDnE,KAAA;MACAwO,WAAA,EAAa,KAAK6B,SAAA,CAAUQ,uBAAA;MAC5BlD,gBAAA,EAAkB,KAAK0C,SAAA,CAAUY;IACrC,CAAC;IACD5I,OAAA,CAAOvK,KAAA,CAAM,cAAcoX,MAAM;IAEjC,OAAOA,MAAA;EACX;AAcJ;;;ACSO,IAAMC,WAAA,GAAN,MAAkB;EAIdtW,YACcwR,SAAA,EACA0E,gBAAA,EACnB;IAFmB,KAAA1E,SAAA,GAAAA,SAAA;IACA,KAAA0E,gBAAA,GAAAA,gBAAA;IALrB,KAAiBpP,OAAA,GAAU,IAAIhH,MAAA,CAAO,aAAa;IAO/C,KAAK+R,YAAA,GAAe,IAAIrD,WAAA,CACpB,KAAKgD,SAAA,CAAUkE,iCAAA,EACf,MACA,KAAKlE,SAAA,CAAUd,YACnB;EACJ;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA,MAAa6F,aAAa;IACtBC,UAAA,GAAa;IACb/B,YAAA,GAAe,KAAKjD,SAAA,CAAUiD,YAAA;IAC9BrQ,SAAA,GAAY,KAAKoN,SAAA,CAAUpN,SAAA;IAC3BC,aAAA,GAAgB,KAAKmN,SAAA,CAAUnN,aAAA;IAC/BqM,YAAA;IACA,GAAGxQ;EACP,GAAuD;IACnD,MAAMsJ,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,cAAc;IACjD,IAAI,CAAC6D,SAAA,EAAW;MACZoF,OAAA,CAAOnJ,KAAA,CAAM,IAAIT,KAAA,CAAM,yBAAyB,CAAC;IACrD;IACA,IAAI,CAAC6U,YAAA,EAAc;MACfjL,OAAA,CAAOnJ,KAAA,CAAM,IAAIT,KAAA,CAAM,4BAA4B,CAAC;IACxD;IACA,IAAI,CAACM,IAAA,CAAKuW,IAAA,EAAM;MACZjN,OAAA,CAAOnJ,KAAA,CAAM,IAAIT,KAAA,CAAM,oBAAoB,CAAC;IAChD;IAEA,MAAMuK,MAAA,GAAS,IAAIC,eAAA,CAAgB;MAAEoM,UAAA;MAAY/B;IAAa,CAAC;IAC/D,WAAW,CAAC7L,GAAA,EAAKjJ,KAAK,KAAKe,MAAA,CAAOgI,OAAA,CAAQxI,IAAI,GAAG;MAC7C,IAAIP,KAAA,IAAS,MAAM;QACfwK,MAAA,CAAOwJ,GAAA,CAAI/K,GAAA,EAAKjJ,KAAK;MACzB;IACJ;IACA,IAAI6Q,SAAA;IACJ,QAAQ,KAAKgB,SAAA,CAAUmD,qBAAA;MACnB,KAAK;QACD,IAAItQ,aAAA,KAAkB,UAAaA,aAAA,KAAkB,MAAM;UACvDmF,OAAA,CAAOnJ,KAAA,CAAM,IAAIT,KAAA,CAAM,6BAA6B,CAAC;UAErD,MAAM;QACV;QACA4Q,SAAA,GAAY/O,WAAA,CAAY0C,iBAAA,CAAkBC,SAAA,EAAWC,aAAa;QAClE;MACJ,KAAK;QACD8F,MAAA,CAAOuM,MAAA,CAAO,aAAatS,SAAS;QACpC,IAAIC,aAAA,EAAe;UACf8F,MAAA,CAAOuM,MAAA,CAAO,iBAAiBrS,aAAa;QAChD;QACA;IACR;IAEA,MAAMiB,GAAA,GAAM,MAAM,KAAK4Q,gBAAA,CAAiBvD,gBAAA,CAAiB,KAAK;IAC9DnJ,OAAA,CAAOvK,KAAA,CAAM,oBAAoB;IAEjC,MAAMqQ,QAAA,GAAW,MAAM,KAAKuC,YAAA,CAAatB,QAAA,CAASjL,GAAA,EAAK;MACnDkI,IAAA,EAAMrD,MAAA;MACNqG,SAAA;MACA1B,gBAAA,EAAkB,KAAK0C,SAAA,CAAUY,uBAAA;MACjC3B,eAAA,EAAiB,KAAKe,SAAA,CAAUQ,uBAAA;MAChCtB;IACJ,CAAC;IAEDlH,OAAA,CAAOvK,KAAA,CAAM,cAAc;IAE3B,OAAOqQ,QAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA,MAAaqH,oBAAoB;IAC7BH,UAAA,GAAa;IACbpS,SAAA,GAAY,KAAKoN,SAAA,CAAUpN,SAAA;IAC3BC,aAAA,GAAgB,KAAKmN,SAAA,CAAUnN,aAAA;IAC/BmQ,KAAA,GAAQ,KAAKhD,SAAA,CAAUgD,KAAA;IACvB,GAAGtU;EACP,GAA8D;IAC1D,MAAMsJ,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,qBAAqB;IAExD,IAAI,CAAC6D,SAAA,EAAW;MACZoF,OAAA,CAAOnJ,KAAA,CAAM,IAAIT,KAAA,CAAM,yBAAyB,CAAC;IACrD;IAEA,MAAMuK,MAAA,GAAS,IAAIC,eAAA,CAAgB;MAAEoM;IAAW,CAAC;IACjD,IAAI,CAAC,KAAKhF,SAAA,CAAUuE,uBAAA,EAAyB;MACzC5L,MAAA,CAAOwJ,GAAA,CAAI,SAASa,KAAK;IAC7B;IACA,WAAW,CAAC5L,GAAA,EAAKjJ,KAAK,KAAKe,MAAA,CAAOgI,OAAA,CAAQxI,IAAI,GAAG;MAC7C,IAAIP,KAAA,IAAS,MAAM;QACfwK,MAAA,CAAOwJ,GAAA,CAAI/K,GAAA,EAAKjJ,KAAK;MACzB;IACJ;IAEA,IAAI6Q,SAAA;IACJ,QAAQ,KAAKgB,SAAA,CAAUmD,qBAAA;MACnB,KAAK;QACD,IAAItQ,aAAA,KAAkB,UAAaA,aAAA,KAAkB,MAAM;UACvDmF,OAAA,CAAOnJ,KAAA,CAAM,IAAIT,KAAA,CAAM,6BAA6B,CAAC;UAErD,MAAM;QACV;QACA4Q,SAAA,GAAY/O,WAAA,CAAY0C,iBAAA,CAAkBC,SAAA,EAAWC,aAAa;QAClE;MACJ,KAAK;QACD8F,MAAA,CAAOuM,MAAA,CAAO,aAAatS,SAAS;QACpC,IAAIC,aAAA,EAAe;UACf8F,MAAA,CAAOuM,MAAA,CAAO,iBAAiBrS,aAAa;QAChD;QACA;IACR;IAEA,MAAMiB,GAAA,GAAM,MAAM,KAAK4Q,gBAAA,CAAiBvD,gBAAA,CAAiB,KAAK;IAC9DnJ,OAAA,CAAOvK,KAAA,CAAM,oBAAoB;IAEjC,MAAMqQ,QAAA,GAAW,MAAM,KAAKuC,YAAA,CAAatB,QAAA,CAASjL,GAAA,EAAK;MAAEkI,IAAA,EAAMrD,MAAA;MAAQqG,SAAA;MAAW1B,gBAAA,EAAkB,KAAK0C,SAAA,CAAUY,uBAAA;MAAyB3B,eAAA,EAAiB,KAAKe,SAAA,CAAUQ;IAAwB,CAAC;IACrMxI,OAAA,CAAOvK,KAAA,CAAM,cAAc;IAE3B,OAAOqQ,QAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA,MAAasH,qBAAqB;IAC9BJ,UAAA,GAAa;IACbpS,SAAA,GAAY,KAAKoN,SAAA,CAAUpN,SAAA;IAC3BC,aAAA,GAAgB,KAAKmN,SAAA,CAAUnN,aAAA;IAC/ByK,gBAAA;IACA4B,YAAA;IACA,GAAGxQ;EACP,GAA+D;IAC3D,MAAMsJ,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,sBAAsB;IACzD,IAAI,CAAC6D,SAAA,EAAW;MACZoF,OAAA,CAAOnJ,KAAA,CAAM,IAAIT,KAAA,CAAM,yBAAyB,CAAC;IACrD;IACA,IAAI,CAACM,IAAA,CAAK2W,aAAA,EAAe;MACrBrN,OAAA,CAAOnJ,KAAA,CAAM,IAAIT,KAAA,CAAM,6BAA6B,CAAC;IACzD;IAEA,MAAMuK,MAAA,GAAS,IAAIC,eAAA,CAAgB;MAAEoM;IAAW,CAAC;IACjD,WAAW,CAAC5N,GAAA,EAAKjJ,KAAK,KAAKe,MAAA,CAAOgI,OAAA,CAAQxI,IAAI,GAAG;MAC7C,IAAIkT,KAAA,CAAMC,OAAA,CAAQ1T,KAAK,GAAG;QACtBA,KAAA,CAAMuR,OAAA,CAAQ4F,KAAA,IAAS3M,MAAA,CAAOuM,MAAA,CAAO9N,GAAA,EAAKkO,KAAK,CAAC;MACpD,WACSnX,KAAA,IAAS,MAAM;QACpBwK,MAAA,CAAOwJ,GAAA,CAAI/K,GAAA,EAAKjJ,KAAK;MACzB;IACJ;IACA,IAAI6Q,SAAA;IACJ,QAAQ,KAAKgB,SAAA,CAAUmD,qBAAA;MACnB,KAAK;QACD,IAAItQ,aAAA,KAAkB,UAAaA,aAAA,KAAkB,MAAM;UACvDmF,OAAA,CAAOnJ,KAAA,CAAM,IAAIT,KAAA,CAAM,6BAA6B,CAAC;UAErD,MAAM;QACV;QACA4Q,SAAA,GAAY/O,WAAA,CAAY0C,iBAAA,CAAkBC,SAAA,EAAWC,aAAa;QAClE;MACJ,KAAK;QACD8F,MAAA,CAAOuM,MAAA,CAAO,aAAatS,SAAS;QACpC,IAAIC,aAAA,EAAe;UACf8F,MAAA,CAAOuM,MAAA,CAAO,iBAAiBrS,aAAa;QAChD;QACA;IACR;IAEA,MAAMiB,GAAA,GAAM,MAAM,KAAK4Q,gBAAA,CAAiBvD,gBAAA,CAAiB,KAAK;IAC9DnJ,OAAA,CAAOvK,KAAA,CAAM,oBAAoB;IAEjC,MAAMqQ,QAAA,GAAW,MAAM,KAAKuC,YAAA,CAAatB,QAAA,CAASjL,GAAA,EAAK;MAAEkI,IAAA,EAAMrD,MAAA;MAAQqG,SAAA;MAAW1B,gBAAA;MAAkB2B,eAAA,EAAiB,KAAKe,SAAA,CAAUQ,uBAAA;MAAyBtB;IAAa,CAAC;IAC3KlH,OAAA,CAAOvK,KAAA,CAAM,cAAc;IAE3B,OAAOqQ,QAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA,MAAayH,OAAO7W,IAAA,EAAiC;IAtQzD,IAAAyH,EAAA;IAuQQ,MAAM6B,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,QAAQ;IAC3C,IAAI,CAACL,IAAA,CAAKiB,KAAA,EAAO;MACbqI,OAAA,CAAOnJ,KAAA,CAAM,IAAIT,KAAA,CAAM,qBAAqB,CAAC;IACjD;IAEA,MAAM0F,GAAA,GAAM,MAAM,KAAK4Q,gBAAA,CAAiBnD,qBAAA,CAAsB,KAAK;IAEnEvJ,OAAA,CAAOvK,KAAA,CAAM,sCAAqC0I,EAAA,GAAAzH,IAAA,CAAK8W,eAAA,KAAL,OAAArP,EAAA,GAAwB,oBAAoB,EAAE;IAEhG,MAAMwC,MAAA,GAAS,IAAIC,eAAA,CAAgB;IACnC,WAAW,CAACxB,GAAA,EAAKjJ,KAAK,KAAKe,MAAA,CAAOgI,OAAA,CAAQxI,IAAI,GAAG;MAC7C,IAAIP,KAAA,IAAS,MAAM;QACfwK,MAAA,CAAOwJ,GAAA,CAAI/K,GAAA,EAAKjJ,KAAK;MACzB;IACJ;IACAwK,MAAA,CAAOwJ,GAAA,CAAI,aAAa,KAAKnC,SAAA,CAAUpN,SAAS;IAChD,IAAI,KAAKoN,SAAA,CAAUnN,aAAA,EAAe;MAC9B8F,MAAA,CAAOwJ,GAAA,CAAI,iBAAiB,KAAKnC,SAAA,CAAUnN,aAAa;IAC5D;IAEA,MAAM,KAAKwN,YAAA,CAAatB,QAAA,CAASjL,GAAA,EAAK;MAAEkI,IAAA,EAAMrD,MAAA;MAAQ2E,gBAAA,EAAkB,KAAK0C,SAAA,CAAUY;IAAwB,CAAC;IAChH5I,OAAA,CAAOvK,KAAA,CAAM,cAAc;EAC/B;AACJ;;;ACzQO,IAAMgY,iBAAA,GAAN,MAAwB;EAKpBjX,YACgBwR,SAAA,EACA0E,gBAAA,EACAgB,cAAA,EACrB;IAHqB,KAAA1F,SAAA,GAAAA,SAAA;IACA,KAAA0E,gBAAA,GAAAA,gBAAA;IACA,KAAAgB,cAAA,GAAAA,cAAA;IAPvB,KAAmBpQ,OAAA,GAAU,IAAIhH,MAAA,CAAO,mBAAmB;IASvD,KAAKqX,gBAAA,GAAmB,IAAIlB,eAAA,CAAgB,KAAKzE,SAAA,EAAW,KAAK0E,gBAAgB;IACjF,KAAKkB,YAAA,GAAe,IAAId,WAAA,CAAY,KAAK9E,SAAA,EAAW,KAAK0E,gBAAgB;EAC7E;EAEA,MAAamB,uBAAuB/H,QAAA,EAA0B3E,KAAA,EAAoB+F,YAAA,EAA2D;IACzI,MAAMlH,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,wBAAwB;IAE3D,KAAK+W,mBAAA,CAAoBhI,QAAA,EAAU3E,KAAK;IACxCnB,OAAA,CAAOvK,KAAA,CAAM,iBAAiB;IAE9B,MAAM,KAAKsY,YAAA,CAAajI,QAAA,EAAU3E,KAAA,EAAO+F,YAAY;IACrDlH,OAAA,CAAOvK,KAAA,CAAM,gBAAgB;IAE7B,IAAIqQ,QAAA,CAASkI,QAAA,EAAU;MACnB,KAAKC,0BAAA,CAA2BnI,QAAQ;IAC5C;IACA9F,OAAA,CAAOvK,KAAA,CAAM,kBAAkB;IAE/B,MAAM,KAAKyY,cAAA,CAAepI,QAAA,EAAU3E,KAAA,oBAAAA,KAAA,CAAOgN,YAAA,EAAcrI,QAAA,CAASkI,QAAQ;IAC1EhO,OAAA,CAAOvK,KAAA,CAAM,kBAAkB;EACnC;EAEA,MAAa2Y,4BAA4BtI,QAAA,EAA0BqI,YAAA,EAAsC;IACrG,MAAMnO,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,6BAA6B;IAEhE,IAAI+O,QAAA,CAASkI,QAAA,IAAY,CAAC,CAAClI,QAAA,CAASuI,QAAA,EAAU;MAC1C,KAAKJ,0BAAA,CAA2BnI,QAAQ;IAC5C;IACA9F,OAAA,CAAOvK,KAAA,CAAM,kBAAkB;IAE/B,MAAM,KAAKyY,cAAA,CAAepI,QAAA,EAAUqI,YAAA,EAAcrI,QAAA,CAASkI,QAAQ;IACnEhO,OAAA,CAAOvK,KAAA,CAAM,kBAAkB;EACnC;EAEA,MAAa6Y,wBAAwBxI,QAAA,EAA0B3E,KAAA,EAAoC;IAjEvG,IAAAhD,EAAA,EAAAC,EAAA;IAkEQ,MAAM4B,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,yBAAyB;IAE5D+O,QAAA,CAAS1E,SAAA,GAAYD,KAAA,CAAM3G,IAAA;IAE3B,CAAA2D,EAAA,GAAA2H,QAAA,CAASzE,aAAA,KAAT,OAAAlD,EAAA,GAAA2H,QAAA,CAASzE,aAAA,GAAkBF,KAAA,CAAME,aAAA;IAEjC,CAAAjD,EAAA,GAAA0H,QAAA,CAASkF,KAAA,KAAT,OAAA5M,EAAA,GAAA0H,QAAA,CAASkF,KAAA,GAAU7J,KAAA,CAAM6J,KAAA;IAIzB,IAAIlF,QAAA,CAASkI,QAAA,IAAY,CAAC,CAAClI,QAAA,CAASuI,QAAA,EAAU;MAC1C,KAAKJ,0BAAA,CAA2BnI,QAAA,EAAU3E,KAAA,CAAMkN,QAAQ;MACxDrO,OAAA,CAAOvK,KAAA,CAAM,oBAAoB;IACrC;IAEA,IAAI,CAACqQ,QAAA,CAASuI,QAAA,EAAU;MAEpBvI,QAAA,CAASuI,QAAA,GAAWlN,KAAA,CAAMkN,QAAA;MAE1BvI,QAAA,CAASyI,OAAA,GAAUpN,KAAA,CAAMoN,OAAA;IAC7B;IAEA,MAAMC,UAAA,GAAa1I,QAAA,CAASkI,QAAA,IAAY,CAAC,CAAClI,QAAA,CAASuI,QAAA;IACnD,MAAM,KAAKH,cAAA,CAAepI,QAAA,EAAU,OAAO0I,UAAU;IACrDxO,OAAA,CAAOvK,KAAA,CAAM,kBAAkB;EACnC;EAEOgZ,wBAAwB3I,QAAA,EAA2B3E,KAAA,EAAoB;IAC1E,MAAMnB,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,yBAAyB;IAC5D,IAAIoK,KAAA,CAAMuN,EAAA,KAAO5I,QAAA,CAAS3E,KAAA,EAAO;MAC7BnB,OAAA,CAAOnJ,KAAA,CAAM,IAAIT,KAAA,CAAM,sBAAsB,CAAC;IAClD;IAKA4J,OAAA,CAAOvK,KAAA,CAAM,iBAAiB;IAC9BqQ,QAAA,CAAS1E,SAAA,GAAYD,KAAA,CAAM3G,IAAA;IAE3B,IAAIsL,QAAA,CAASlQ,KAAA,EAAO;MAChBoK,OAAA,CAAOrK,IAAA,CAAK,sBAAsBmQ,QAAA,CAASlQ,KAAK;MAChD,MAAM,IAAImL,aAAA,CAAc+E,QAAQ;IACpC;EACJ;EAEUgI,oBAAoBhI,QAAA,EAA0B3E,KAAA,EAA0B;IA/GtF,IAAAhD,EAAA;IAgHQ,MAAM6B,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,qBAAqB;IACxD,IAAIoK,KAAA,CAAMuN,EAAA,KAAO5I,QAAA,CAAS3E,KAAA,EAAO;MAC7BnB,OAAA,CAAOnJ,KAAA,CAAM,IAAIT,KAAA,CAAM,sBAAsB,CAAC;IAClD;IAEA,IAAI,CAAC+K,KAAA,CAAMvG,SAAA,EAAW;MAClBoF,OAAA,CAAOnJ,KAAA,CAAM,IAAIT,KAAA,CAAM,uBAAuB,CAAC;IACnD;IAEA,IAAI,CAAC+K,KAAA,CAAM2J,SAAA,EAAW;MAClB9K,OAAA,CAAOnJ,KAAA,CAAM,IAAIT,KAAA,CAAM,uBAAuB,CAAC;IACnD;IAGA,IAAI,KAAK4R,SAAA,CAAU8C,SAAA,KAAc3J,KAAA,CAAM2J,SAAA,EAAW;MAC9C9K,OAAA,CAAOnJ,KAAA,CAAM,IAAIT,KAAA,CAAM,iDAAiD,CAAC;IAC7E;IACA,IAAI,KAAK4R,SAAA,CAAUpN,SAAA,IAAa,KAAKoN,SAAA,CAAUpN,SAAA,KAAcuG,KAAA,CAAMvG,SAAA,EAAW;MAC1EoF,OAAA,CAAOnJ,KAAA,CAAM,IAAIT,KAAA,CAAM,iDAAiD,CAAC;IAC7E;IAKA4J,OAAA,CAAOvK,KAAA,CAAM,iBAAiB;IAC9BqQ,QAAA,CAAS1E,SAAA,GAAYD,KAAA,CAAM3G,IAAA;IAC3BsL,QAAA,CAASxE,SAAA,GAAYH,KAAA,CAAMG,SAAA;IAE3B,CAAAnD,EAAA,GAAA2H,QAAA,CAASkF,KAAA,KAAT,OAAA7M,EAAA,GAAA2H,QAAA,CAASkF,KAAA,GAAU7J,KAAA,CAAM6J,KAAA;IAEzB,IAAIlF,QAAA,CAASlQ,KAAA,EAAO;MAChBoK,OAAA,CAAOrK,IAAA,CAAK,sBAAsBmQ,QAAA,CAASlQ,KAAK;MAChD,MAAM,IAAImL,aAAA,CAAc+E,QAAQ;IACpC;IAEA,IAAI3E,KAAA,CAAM7G,aAAA,IAAiB,CAACwL,QAAA,CAASmH,IAAA,EAAM;MACvCjN,OAAA,CAAOnJ,KAAA,CAAM,IAAIT,KAAA,CAAM,2BAA2B,CAAC;IACvD;EAEJ;EAEA,MAAgB8X,eAAepI,QAAA,EAA0BqI,YAAA,GAAe,OAAOQ,WAAA,GAAc,MAAqB;IAC9G,MAAM3O,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,gBAAgB;IACnD+O,QAAA,CAASyI,OAAA,GAAU,KAAKb,cAAA,CAAe/B,oBAAA,CAAqB7F,QAAA,CAASyI,OAAO;IAE5E,IAAIJ,YAAA,IAAgB,CAAC,KAAKnG,SAAA,CAAU4D,YAAA,IAAgB,CAAC9F,QAAA,CAAS/D,YAAA,EAAc;MACxE/B,OAAA,CAAOvK,KAAA,CAAM,uBAAuB;MACpC;IACJ;IAEAuK,OAAA,CAAOvK,KAAA,CAAM,mBAAmB;IAChC,MAAMoX,MAAA,GAAS,MAAM,KAAKc,gBAAA,CAAiBf,SAAA,CAAU9G,QAAA,CAAS/D,YAAY;IAC1E/B,OAAA,CAAOvK,KAAA,CAAM,mDAAmD;IAEhE,IAAIkZ,WAAA,IAAe9B,MAAA,CAAO+B,GAAA,KAAQ9I,QAAA,CAASyI,OAAA,CAAQK,GAAA,EAAK;MACpD5O,OAAA,CAAOnJ,KAAA,CAAM,IAAIT,KAAA,CAAM,mEAAmE,CAAC;IAC/F;IAEA0P,QAAA,CAASyI,OAAA,GAAU,KAAKb,cAAA,CAAemB,WAAA,CAAY/I,QAAA,CAASyI,OAAA,EAAS,KAAKb,cAAA,CAAe/B,oBAAA,CAAqBkB,MAAuB,CAAC;IACtI7M,OAAA,CAAOvK,KAAA,CAAM,+CAA+CqQ,QAAA,CAASyI,OAAO;EAChF;EAEA,MAAgBR,aAAajI,QAAA,EAA0B3E,KAAA,EAAoB+F,YAAA,EAA2D;IAClI,MAAMlH,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,cAAc;IACjD,IAAI+O,QAAA,CAASmH,IAAA,EAAM;MACfjN,OAAA,CAAOvK,KAAA,CAAM,iBAAiB;MAC9B,MAAMqZ,aAAA,GAAgB,MAAM,KAAKlB,YAAA,CAAab,YAAA,CAAa;QACvDnS,SAAA,EAAWuG,KAAA,CAAMvG,SAAA;QACjBC,aAAA,EAAesG,KAAA,CAAMtG,aAAA;QACrBoS,IAAA,EAAMnH,QAAA,CAASmH,IAAA;QACfhC,YAAA,EAAc9J,KAAA,CAAM8J,YAAA;QACpB3Q,aAAA,EAAe6G,KAAA,CAAM7G,aAAA;QACrB4M,YAAA;QACA,GAAG/F,KAAA,CAAMkL;MACb,CAAC;MACDnV,MAAA,CAAO2R,MAAA,CAAO/C,QAAA,EAAUgJ,aAAa;IACzC,OAAO;MACH9O,OAAA,CAAOvK,KAAA,CAAM,oBAAoB;IACrC;EACJ;EAEUwY,2BAA2BnI,QAAA,EAA0BiJ,aAAA,EAA8B;IAjMjG,IAAA5Q,EAAA;IAkMQ,MAAM6B,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,4BAA4B;IAE/DiJ,OAAA,CAAOvK,KAAA,CAAM,uBAAuB;IACpC,MAAMuZ,QAAA,GAAWvX,QAAA,CAASC,MAAA,EAAOyG,EAAA,GAAA2H,QAAA,CAASuI,QAAA,KAAT,OAAAlQ,EAAA,GAAqB,EAAE;IAExD,IAAI,CAAC6Q,QAAA,CAASJ,GAAA,EAAK;MACf5O,OAAA,CAAOnJ,KAAA,CAAM,IAAIT,KAAA,CAAM,qCAAqC,CAAC;IACjE;IAEA,IAAI2Y,aAAA,EAAe;MACf,MAAME,QAAA,GAAWxX,QAAA,CAASC,MAAA,CAAOqX,aAAa;MAC9C,IAAIC,QAAA,CAASJ,GAAA,KAAQK,QAAA,CAASL,GAAA,EAAK;QAC/B5O,OAAA,CAAOnJ,KAAA,CAAM,IAAIT,KAAA,CAAM,4CAA4C,CAAC;MACxE;MACA,IAAI4Y,QAAA,CAASE,SAAA,IAAaF,QAAA,CAASE,SAAA,KAAcD,QAAA,CAASC,SAAA,EAAW;QACjElP,OAAA,CAAOnJ,KAAA,CAAM,IAAIT,KAAA,CAAM,yDAAyD,CAAC;MACrF;MACA,IAAI4Y,QAAA,CAASG,GAAA,IAAOH,QAAA,CAASG,GAAA,KAAQF,QAAA,CAASE,GAAA,EAAK;QAC/CnP,OAAA,CAAOnJ,KAAA,CAAM,IAAIT,KAAA,CAAM,6CAA6C,CAAC;MACzE;MACA,IAAI,CAAC4Y,QAAA,CAASG,GAAA,IAAOF,QAAA,CAASE,GAAA,EAAK;QAC/BnP,OAAA,CAAOnJ,KAAA,CAAM,IAAIT,KAAA,CAAM,uDAAuD,CAAC;MACnF;IACJ;IAEA0P,QAAA,CAASyI,OAAA,GAAUS,QAAA;EACvB;AACJ;;;ACpNO,IAAMI,KAAA,GAAN,MAAMC,MAAA,CAAM;EASR7Y,YAAYE,IAAA,EAMhB;IACC,KAAKgY,EAAA,GAAKhY,IAAA,CAAKgY,EAAA,IAAMzW,WAAA,CAAY8B,cAAA,CAAe;IAChD,KAAKS,IAAA,GAAO9D,IAAA,CAAK8D,IAAA;IAEjB,IAAI9D,IAAA,CAAK4Y,OAAA,IAAW5Y,IAAA,CAAK4Y,OAAA,GAAU,GAAG;MAClC,KAAKA,OAAA,GAAU5Y,IAAA,CAAK4Y,OAAA;IACxB,OACK;MACD,KAAKA,OAAA,GAAUjQ,KAAA,CAAMO,YAAA,CAAa;IACtC;IACA,KAAK2P,YAAA,GAAe7Y,IAAA,CAAK6Y,YAAA;IACzB,KAAKjO,SAAA,GAAY5K,IAAA,CAAK4K,SAAA;EAC1B;EAEOkO,gBAAA,EAA0B;IAC7B,IAAIlZ,MAAA,CAAO,OAAO,EAAES,MAAA,CAAO,iBAAiB;IAC5C,OAAOsB,IAAA,CAAKC,SAAA,CAAU;MAClBoW,EAAA,EAAI,KAAKA,EAAA;MACTlU,IAAA,EAAM,KAAKA,IAAA;MACX8U,OAAA,EAAS,KAAKA,OAAA;MACdC,YAAA,EAAc,KAAKA,YAAA;MACnBjO,SAAA,EAAW,KAAKA;IACpB,CAAC;EACL;EAEA,OAAcmO,kBAAkBC,aAAA,EAAuC;IACnEpZ,MAAA,CAAOa,YAAA,CAAa,SAAS,mBAAmB;IAChD,OAAO0M,OAAA,CAAQC,OAAA,CAAQ,IAAIuL,MAAA,CAAMhX,IAAA,CAAK+O,KAAA,CAAMsI,aAAa,CAAC,CAAC;EAC/D;EAEA,aAAoBC,gBAAgBC,OAAA,EAAqBC,GAAA,EAA4B;IACjF,MAAM7P,OAAA,GAAS1J,MAAA,CAAOa,YAAA,CAAa,SAAS,iBAAiB;IAC7D,MAAM2Y,MAAA,GAASzQ,KAAA,CAAMO,YAAA,CAAa,IAAIiQ,GAAA;IAEtC,MAAMtI,IAAA,GAAO,MAAMqI,OAAA,CAAQvF,UAAA,CAAW;IACtCrK,OAAA,CAAOvK,KAAA,CAAM,YAAY8R,IAAI;IAE7B,SAASwI,CAAA,GAAI,GAAGA,CAAA,GAAIxI,IAAA,CAAK3C,MAAA,EAAQmL,CAAA,IAAK;MAClC,MAAM3Q,GAAA,GAAMmI,IAAA,CAAKwI,CAAC;MAClB,MAAMtJ,IAAA,GAAO,MAAMmJ,OAAA,CAAQpJ,GAAA,CAAIpH,GAAG;MAClC,IAAIgL,MAAA,GAAS;MAEb,IAAI3D,IAAA,EAAM;QACN,IAAI;UACA,MAAMtF,KAAA,GAAQ,MAAMkO,MAAA,CAAMI,iBAAA,CAAkBhJ,IAAI;UAEhDzG,OAAA,CAAOvK,KAAA,CAAM,sBAAsB2J,GAAA,EAAK+B,KAAA,CAAMmO,OAAO;UACrD,IAAInO,KAAA,CAAMmO,OAAA,IAAWQ,MAAA,EAAQ;YACzB1F,MAAA,GAAS;UACb;QACJ,SACOtT,GAAA,EAAK;UACRkJ,OAAA,CAAOpK,KAAA,CAAM,gCAAgCwJ,GAAA,EAAKtI,GAAG;UACrDsT,MAAA,GAAS;QACb;MACJ,OACK;QACDpK,OAAA,CAAOvK,KAAA,CAAM,+BAA+B2J,GAAG;QAC/CgL,MAAA,GAAS;MACb;MAEA,IAAIA,MAAA,EAAQ;QACRpK,OAAA,CAAOvK,KAAA,CAAM,yBAAyB2J,GAAG;QACzC,KAAKwQ,OAAA,CAAQxF,MAAA,CAAOhL,GAAG;MAC3B;IACJ;EACJ;AACJ;;;ACzDO,IAAM4Q,WAAA,GAAN,MAAMC,YAAA,SAAoBb,KAAA,CAAM;EAyB3B5Y,YAAYE,IAAA,EAAuB;IACvC,MAAMA,IAAI;IAEV,KAAK4D,aAAA,GAAgB5D,IAAA,CAAK4D,aAAA;IAC1B,KAAK4V,cAAA,GAAiBxZ,IAAA,CAAKwZ,cAAA;IAC3B,KAAKpF,SAAA,GAAYpU,IAAA,CAAKoU,SAAA;IACtB,KAAKlQ,SAAA,GAAYlE,IAAA,CAAKkE,SAAA;IACtB,KAAKqQ,YAAA,GAAevU,IAAA,CAAKuU,YAAA;IACzB,KAAKD,KAAA,GAAQtU,IAAA,CAAKsU,KAAA;IAClB,KAAKnQ,aAAA,GAAgBnE,IAAA,CAAKmE,aAAA;IAC1B,KAAKwR,gBAAA,GAAmB3V,IAAA,CAAK2V,gBAAA;IAE7B,KAAKX,aAAA,GAAgBhV,IAAA,CAAKgV,aAAA;IAC1B,KAAKyC,YAAA,GAAezX,IAAA,CAAKyX,YAAA;EAC7B;EAEA,aAAoBpX,OAAOL,IAAA,EAAmD;IAC1E,MAAM4D,aAAA,GAAgB5D,IAAA,CAAK4D,aAAA,KAAkB,OAAOrC,WAAA,CAAYmC,oBAAA,CAAqB,IAAK1D,IAAA,CAAK4D,aAAA,IAAiB;IAChH,MAAM4V,cAAA,GAAiB5V,aAAA,GAAiB,MAAMrC,WAAA,CAAYoC,qBAAA,CAAsBC,aAAa,IAAK;IAElG,OAAO,IAAI2V,YAAA,CAAY;MACnB,GAAGvZ,IAAA;MACH4D,aAAA;MACA4V;IACJ,CAAC;EACL;EAEOV,gBAAA,EAA0B;IAC7B,IAAIlZ,MAAA,CAAO,aAAa,EAAES,MAAA,CAAO,iBAAiB;IAClD,OAAOsB,IAAA,CAAKC,SAAA,CAAU;MAClBoW,EAAA,EAAI,KAAKA,EAAA;MACTlU,IAAA,EAAM,KAAKA,IAAA;MACX8U,OAAA,EAAS,KAAKA,OAAA;MACdC,YAAA,EAAc,KAAKA,YAAA;MACnBjO,SAAA,EAAW,KAAKA,SAAA;MAEhBhH,aAAA,EAAe,KAAKA,aAAA;MACpBwQ,SAAA,EAAW,KAAKA,SAAA;MAChBlQ,SAAA,EAAW,KAAKA,SAAA;MAChBqQ,YAAA,EAAc,KAAKA,YAAA;MACnBD,KAAA,EAAO,KAAKA,KAAA;MACZnQ,aAAA,EAAe,KAAKA,aAAA;MACpBwR,gBAAA,EAAmB,KAAKA,gBAAA;MACxBX,aAAA,EAAe,KAAKA,aAAA;MACpByC,YAAA,EAAc,KAAKA;IACvB,CAAC;EACL;EAEA,OAAcsB,kBAAkBC,aAAA,EAA6C;IACzEpZ,MAAA,CAAOa,YAAA,CAAa,eAAe,mBAAmB;IACtD,MAAMqD,IAAA,GAAOnC,IAAA,CAAK+O,KAAA,CAAMsI,aAAa;IACrC,OAAOO,YAAA,CAAYlZ,MAAA,CAAOyD,IAAI;EAClC;AACJ;;;AC5DO,IAAM2V,cAAA,GAAN,MAAMA,cAAA,CAAc;EAMf3Z,YAAYE,IAAA,EAGjB;IACC,KAAKoF,GAAA,GAAMpF,IAAA,CAAKoF,GAAA;IAChB,KAAKqF,KAAA,GAAQzK,IAAA,CAAKyK,KAAA;EACtB;EAEA,aAAoBpK,OAAO;IAAA;IAEvB+E,GAAA;IAAKgP,SAAA;IAAWlQ,SAAA;IAAWqQ,YAAA;IAAcF,aAAA;IAAeC,KAAA;IAAA;IAExDoF,UAAA;IAAY1E,aAAA;IAAe6D,YAAA;IAAc1U,aAAA;IAAeqB,KAAA;IAAOoF,SAAA;IAC/DmK,QAAA;IACA0C,YAAA;IACA/B,gBAAA;IACAC,gBAAA;IACAL,WAAA;IACAqE,OAAA;IACA9D,uBAAA;IACA,GAAG+D;EACP,GAAoD;IAChD,IAAI,CAACxU,GAAA,EAAK;MACN,KAAKwB,OAAA,CAAQ1H,KAAA,CAAM,uBAAuB;MAC1C,MAAM,IAAIQ,KAAA,CAAM,KAAK;IACzB;IACA,IAAI,CAACwE,SAAA,EAAW;MACZ,KAAK0C,OAAA,CAAQ1H,KAAA,CAAM,6BAA6B;MAChD,MAAM,IAAIQ,KAAA,CAAM,WAAW;IAC/B;IACA,IAAI,CAAC6U,YAAA,EAAc;MACf,KAAK3N,OAAA,CAAQ1H,KAAA,CAAM,gCAAgC;MACnD,MAAM,IAAIQ,KAAA,CAAM,cAAc;IAClC;IACA,IAAI,CAAC2U,aAAA,EAAe;MAChB,KAAKzN,OAAA,CAAQ1H,KAAA,CAAM,iCAAiC;MACpD,MAAM,IAAIQ,KAAA,CAAM,eAAe;IACnC;IACA,IAAI,CAAC4U,KAAA,EAAO;MACR,KAAK1N,OAAA,CAAQ1H,KAAA,CAAM,yBAAyB;MAC5C,MAAM,IAAIQ,KAAA,CAAM,OAAO;IAC3B;IACA,IAAI,CAAC0U,SAAA,EAAW;MACZ,KAAKxN,OAAA,CAAQ1H,KAAA,CAAM,6BAA6B;MAChD,MAAM,IAAIQ,KAAA,CAAM,WAAW;IAC/B;IAEA,MAAM+K,KAAA,GAAQ,MAAM6O,WAAA,CAAYjZ,MAAA,CAAO;MACnCyD,IAAA,EAAM4V,UAAA;MACNb,YAAA;MACAjO,SAAA;MACAhH,aAAA,EAAe,CAAC0R,WAAA;MAChBpR,SAAA;MAAWkQ,SAAA;MAAWG,YAAA;MACtBS,aAAA;MACA7Q,aAAA;MAAemQ,KAAA;MAAOqB,gBAAA;MACtB8B;IACJ,CAAC;IAED,MAAM1N,SAAA,GAAY,IAAIC,GAAA,CAAI5E,GAAG;IAC7B2E,SAAA,CAAU8P,YAAA,CAAarD,MAAA,CAAO,aAAatS,SAAS;IACpD6F,SAAA,CAAU8P,YAAA,CAAarD,MAAA,CAAO,gBAAgBjC,YAAY;IAC1DxK,SAAA,CAAU8P,YAAA,CAAarD,MAAA,CAAO,iBAAiBnC,aAAa;IAC5D,IAAI,CAACwB,uBAAA,EAAyB;MAC1B9L,SAAA,CAAU8P,YAAA,CAAarD,MAAA,CAAO,SAASlC,KAAK;IAChD;IACA,IAAI9O,KAAA,EAAO;MACPuE,SAAA,CAAU8P,YAAA,CAAarD,MAAA,CAAO,SAAShR,KAAK;IAChD;IAEA,IAAImU,OAAA,EAAS;MACT5P,SAAA,CAAU8P,YAAA,CAAarD,MAAA,CAAO,YAAYmD,OAAO;IACrD;IAEA,IAAIG,UAAA,GAAarP,KAAA,CAAMuN,EAAA;IACvB,IAAIpN,SAAA,EAAW;MACXkP,UAAA,GAAa,GAAGA,UAAU,GAAG1P,mBAAmB,GAAGQ,SAAS;IAChE;IACAb,SAAA,CAAU8P,YAAA,CAAarD,MAAA,CAAO,SAASsD,UAAU;IACjD,IAAIrP,KAAA,CAAM+O,cAAA,EAAgB;MACtBzP,SAAA,CAAU8P,YAAA,CAAarD,MAAA,CAAO,kBAAkB/L,KAAA,CAAM+O,cAAc;MACpEzP,SAAA,CAAU8P,YAAA,CAAarD,MAAA,CAAO,yBAAyB,MAAM;IACjE;IAEA,IAAIzB,QAAA,EAAU;MAEV,MAAMgF,SAAA,GAAY7G,KAAA,CAAMC,OAAA,CAAQ4B,QAAQ,IAAIA,QAAA,GAAW,CAACA,QAAQ;MAChEgF,SAAA,CACK/I,OAAA,CAAQgJ,CAAA,IAAKjQ,SAAA,CAAU8P,YAAA,CAAarD,MAAA,CAAO,YAAYwD,CAAC,CAAC;IAClE;IAEA,WAAW,CAACtR,GAAA,EAAKjJ,KAAK,KAAKe,MAAA,CAAOgI,OAAA,CAAQ;MAAEwM,aAAA;MAAe,GAAG4E,cAAA;MAAgB,GAAGlE;IAAiB,CAAC,GAAG;MAClG,IAAIjW,KAAA,IAAS,MAAM;QACfsK,SAAA,CAAU8P,YAAA,CAAarD,MAAA,CAAO9N,GAAA,EAAKjJ,KAAA,CAAMgE,QAAA,CAAS,CAAC;MACvD;IACJ;IAEA,OAAO,IAAIgW,cAAA,CAAc;MACrBrU,GAAA,EAAK2E,SAAA,CAAUmD,IAAA;MACfzC;IACJ,CAAC;EACL;AACJ;AA3GagP,cAAA,CACe7S,OAAA,GAAU,IAAIhH,MAAA,CAAO,eAAe;AADzD,IAAMqa,aAAA,GAANR,cAAA;;;AC9CP,IAAMS,SAAA,GAAY;AAOX,IAAMC,cAAA,GAAN,MAAqB;EAsCjBra,YAAYmK,MAAA,EAAyB;IAjB5C;IAAA,KAAOoB,YAAA,GAAe;IAEtB;IAAA,KAAO+O,UAAA,GAAa;IAapB;IAAA,KAAOvC,OAAA,GAAuB,CAAC;IAG3B,KAAKpN,KAAA,GAAQR,MAAA,CAAO6F,GAAA,CAAI,OAAO;IAC/B,KAAKnF,aAAA,GAAgBV,MAAA,CAAO6F,GAAA,CAAI,eAAe;IAC/C,IAAI,KAAKrF,KAAA,EAAO;MACZ,MAAM4P,UAAA,GAAaC,kBAAA,CAAmB,KAAK7P,KAAK,EAAE8P,KAAA,CAAMnQ,mBAAmB;MAC3E,KAAKK,KAAA,GAAQ4P,UAAA,CAAW,CAAC;MACzB,IAAIA,UAAA,CAAWnM,MAAA,GAAS,GAAG;QACvB,KAAKtD,SAAA,GAAYyP,UAAA,CAAWlQ,KAAA,CAAM,CAAC,EAAEpH,IAAA,CAAKqH,mBAAmB;MACjE;IACJ;IAEA,KAAKlL,KAAA,GAAQ+K,MAAA,CAAO6F,GAAA,CAAI,OAAO;IAC/B,KAAKvF,iBAAA,GAAoBN,MAAA,CAAO6F,GAAA,CAAI,mBAAmB;IACvD,KAAKtF,SAAA,GAAYP,MAAA,CAAO6F,GAAA,CAAI,WAAW;IAEvC,KAAKyG,IAAA,GAAOtM,MAAA,CAAO6F,GAAA,CAAI,MAAM;EACjC;EAEA,IAAWxE,WAAA,EAAiC;IACxC,IAAI,KAAKkP,UAAA,KAAe,QAAW;MAC/B,OAAO;IACX;IACA,OAAO,KAAKA,UAAA,GAAa7R,KAAA,CAAMO,YAAA,CAAa;EAChD;EACA,IAAWoC,WAAW7L,KAAA,EAA2B;IAE7C,IAAI,OAAOA,KAAA,KAAU,UAAUA,KAAA,GAAQgb,MAAA,CAAOhb,KAAK;IACnD,IAAIA,KAAA,KAAU,UAAaA,KAAA,IAAS,GAAG;MACnC,KAAK+a,UAAA,GAAa5U,IAAA,CAAKC,KAAA,CAAMpG,KAAK,IAAIkJ,KAAA,CAAMO,YAAA,CAAa;IAC7D;EACJ;EAEA,IAAWoO,SAAA,EAAoB;IAnFnC,IAAA7P,EAAA;IAoFQ,SAAOA,EAAA,QAAK6M,KAAA,KAAL,gBAAA7M,EAAA,CAAY8S,KAAA,CAAM,KAAKrJ,QAAA,CAASgJ,SAAA,MAAc,CAAC,CAAC,KAAKvC,QAAA;EAChE;AACJ;;;ACxDO,IAAM+C,cAAA,GAAN,MAAqB;EAMjB5a,YAAY;IACfsF,GAAA;IACAsU,UAAA;IAAYiB,aAAA;IAAenG,wBAAA;IAA0BkB,gBAAA;IAAkBmD,YAAA;IAAc3U,SAAA;IAAW0G;EACpG,GAAuB;IARvB,KAAiBhE,OAAA,GAAU,IAAIhH,MAAA,CAAO,gBAAgB;IASlD,IAAI,CAACwF,GAAA,EAAK;MACN,KAAKwB,OAAA,CAAQ1H,KAAA,CAAM,qBAAqB;MACxC,MAAM,IAAIQ,KAAA,CAAM,KAAK;IACzB;IAEA,MAAMqK,SAAA,GAAY,IAAIC,GAAA,CAAI5E,GAAG;IAC7B,IAAIuV,aAAA,EAAe;MACf5Q,SAAA,CAAU8P,YAAA,CAAarD,MAAA,CAAO,iBAAiBmE,aAAa;IAChE;IACA,IAAIzW,SAAA,EAAW;MACX6F,SAAA,CAAU8P,YAAA,CAAarD,MAAA,CAAO,aAAatS,SAAS;IACxD;IAEA,IAAIsQ,wBAAA,EAA0B;MAC1BzK,SAAA,CAAU8P,YAAA,CAAarD,MAAA,CAAO,4BAA4BhC,wBAAwB;MAGlF,IAAIkF,UAAA,IAAc9O,SAAA,EAAW;QACzB,KAAKH,KAAA,GAAQ,IAAIiO,KAAA,CAAM;UAAE5U,IAAA,EAAM4V,UAAA;UAAYb,YAAA;UAAcjO;QAAU,CAAC;QAEpE,IAAIkP,UAAA,GAAa,KAAKrP,KAAA,CAAMuN,EAAA;QAC5B,IAAIpN,SAAA,EAAW;UACXkP,UAAA,GAAa,GAAGA,UAAU,GAAG1P,mBAAmB,GAAGQ,SAAS;QAChE;QACAb,SAAA,CAAU8P,YAAA,CAAarD,MAAA,CAAO,SAASsD,UAAU;MACrD;IACJ;IAEA,WAAW,CAACpR,GAAA,EAAKjJ,KAAK,KAAKe,MAAA,CAAOgI,OAAA,CAAQ;MAAE,GAAGkN;IAAiB,CAAC,GAAG;MAChE,IAAIjW,KAAA,IAAS,MAAM;QACfsK,SAAA,CAAU8P,YAAA,CAAarD,MAAA,CAAO9N,GAAA,EAAKjJ,KAAA,CAAMgE,QAAA,CAAS,CAAC;MACvD;IACJ;IAEA,KAAK2B,GAAA,GAAM2E,SAAA,CAAUmD,IAAA;EACzB;AACJ;;;ACnEO,IAAM0N,eAAA,GAAN,MAAsB;EAelB9a,YAAYmK,MAAA,EAAyB;IACxC,KAAKQ,KAAA,GAAQR,MAAA,CAAO6F,GAAA,CAAI,OAAO;IAC/B,IAAI,KAAKrF,KAAA,EAAO;MACZ,MAAM4P,UAAA,GAAaC,kBAAA,CAAmB,KAAK7P,KAAK,EAAE8P,KAAA,CAAMnQ,mBAAmB;MAC3E,KAAKK,KAAA,GAAQ4P,UAAA,CAAW,CAAC;MACzB,IAAIA,UAAA,CAAWnM,MAAA,GAAS,GAAG;QACvB,KAAKtD,SAAA,GAAYyP,UAAA,CAAWlQ,KAAA,CAAM,CAAC,EAAEpH,IAAA,CAAKqH,mBAAmB;MACjE;IACJ;IAEA,KAAKlL,KAAA,GAAQ+K,MAAA,CAAO6F,GAAA,CAAI,OAAO;IAC/B,KAAKvF,iBAAA,GAAoBN,MAAA,CAAO6F,GAAA,CAAI,mBAAmB;IACvD,KAAKtF,SAAA,GAAYP,MAAA,CAAO6F,GAAA,CAAI,WAAW;EAC3C;AACJ;;;ACrBA,IAAM+K,qBAAA,GAAwB,CAC1B,OACA,OACA,aACA,SACA,OACA,OACA,OACA;AAAA;AAAA,CACJ;AAQA,IAAMC,8BAAA,GAAiC,CAAC,OAAO,OAAO,OAAO,OAAO,KAAK;AAKlE,IAAMC,aAAA,GAAN,MAAoB;EAEhBjb,YACgBwR,SAAA,EACrB;IADqB,KAAAA,SAAA,GAAAA,SAAA;IAFvB,KAAmB1K,OAAA,GAAU,IAAIhH,MAAA,CAAO,eAAe;EAGpD;EAEIqV,qBAAqBkB,MAAA,EAAkC;IAC1D,MAAM6E,MAAA,GAAS;MAAE,GAAG7E;IAAO;IAE3B,IAAI,KAAK7E,SAAA,CAAU2D,oBAAA,EAAsB;MACrC,IAAIgG,cAAA;MACJ,IAAI/H,KAAA,CAAMC,OAAA,CAAQ,KAAK7B,SAAA,CAAU2D,oBAAoB,GAAG;QACpDgG,cAAA,GAAiB,KAAK3J,SAAA,CAAU2D,oBAAA;MACpC,OAAO;QACHgG,cAAA,GAAiBJ,qBAAA;MACrB;MAEA,WAAWK,KAAA,IAASD,cAAA,EAAgB;QAChC,IAAI,CAACH,8BAAA,CAA+B5J,QAAA,CAASgK,KAAK,GAAG;UACjD,OAAOF,MAAA,CAAOE,KAAK;QACvB;MACJ;IACJ;IAEA,OAAOF,MAAA;EACX;EAGO7C,YAAYgD,OAAA,EAAsBC,OAAA,EAAiC;IACtE,MAAMJ,MAAA,GAAS;MAAE,GAAGG;IAAQ;IAC5B,WAAW,CAACD,KAAA,EAAOG,MAAM,KAAK7a,MAAA,CAAOgI,OAAA,CAAQ4S,OAAO,GAAG;MACnD,IAAIJ,MAAA,CAAOE,KAAK,MAAMG,MAAA,EAAQ;QAC1B,IAAInI,KAAA,CAAMC,OAAA,CAAQ6H,MAAA,CAAOE,KAAK,CAAC,KAAKhI,KAAA,CAAMC,OAAA,CAAQkI,MAAM,GAAG;UACvD,IAAI,KAAK/J,SAAA,CAAU8D,mBAAA,CAAoBC,KAAA,IAAS,WAAW;YACvD2F,MAAA,CAAOE,KAAK,IAAIG,MAAA;UACpB,OAAO;YACH,MAAMC,YAAA,GAAepI,KAAA,CAAMC,OAAA,CAAQ6H,MAAA,CAAOE,KAAK,CAAC,IAAIF,MAAA,CAAOE,KAAK,IAAiB,CAACF,MAAA,CAAOE,KAAK,CAAC;YAC/F,WAAWzb,KAAA,IAASyT,KAAA,CAAMC,OAAA,CAAQkI,MAAM,IAAIA,MAAA,GAAS,CAACA,MAAM,GAAG;cAC3D,IAAI,CAACC,YAAA,CAAapK,QAAA,CAASzR,KAAK,GAAG;gBAC/B6b,YAAA,CAAavU,IAAA,CAAKtH,KAAK;cAC3B;YACJ;YACAub,MAAA,CAAOE,KAAK,IAAII,YAAA;UACpB;QACJ,WAAW,OAAON,MAAA,CAAOE,KAAK,MAAM,YAAY,OAAOG,MAAA,KAAW,UAAU;UACxEL,MAAA,CAAOE,KAAK,IAAI,KAAK/C,WAAA,CAAY6C,MAAA,CAAOE,KAAK,GAAgBG,MAAmB;QACpF,OAAO;UACHL,MAAA,CAAOE,KAAK,IAAIG,MAAA;QACpB;MACJ;IACJ;IAEA,OAAOL,MAAA;EACX;AACJ;;;AChFO,IAAMO,SAAA,GAAN,MAAgB;EACZzb,YACa+Q,IAAA,EACTrL,KAAA,EACT;IAFkB,KAAAqL,IAAA,GAAAA,IAAA;IACT,KAAArL,KAAA,GAAAA,KAAA;EACP;AACR;;;ACwDO,IAAMgW,UAAA,GAAN,MAAiB;EAWb1b,YAAY2b,QAAA,EAAwDC,eAAA,EAAmC;IAT9G,KAAmB9U,OAAA,GAAU,IAAIhH,MAAA,CAAO,YAAY;IAUhD,KAAK6b,QAAA,GAAWA,QAAA,YAAoBtH,uBAAA,GAA0BsH,QAAA,GAAW,IAAItH,uBAAA,CAAwBsH,QAAQ;IAE7G,KAAKC,eAAA,GAAkBA,eAAA,WAAAA,eAAA,GAAmB,IAAIrK,eAAA,CAAgB,KAAKoK,QAAQ;IAC3E,KAAKzE,cAAA,GAAiB,IAAI+D,aAAA,CAAc,KAAKU,QAAQ;IACrD,KAAKE,UAAA,GAAa,IAAI5E,iBAAA,CAAkB,KAAK0E,QAAA,EAAU,KAAKC,eAAA,EAAiB,KAAK1E,cAAc;IAChG,KAAKE,YAAA,GAAe,IAAId,WAAA,CAAY,KAAKqF,QAAA,EAAU,KAAKC,eAAe;EAC3E;EAEA,MAAaE,oBAAoB;IAC7BnR,KAAA;IACAoR,OAAA;IACAC,WAAA;IACAjD,YAAA;IACA8B,aAAA;IACAoB,UAAA;IACAtE,YAAA;IACAjS,KAAA;IACAoF,SAAA;IACAyJ,aAAA,GAAgB,KAAKoH,QAAA,CAASpH,aAAA;IAC9BC,KAAA,GAAQ,KAAKmH,QAAA,CAASnH,KAAA;IACtBC,YAAA,GAAe,KAAKkH,QAAA,CAASlH,YAAA;IAC7BG,MAAA,GAAS,KAAK+G,QAAA,CAAS/G,MAAA;IACvBC,OAAA,GAAU,KAAK8G,QAAA,CAAS9G,OAAA;IACxBC,OAAA,GAAU,KAAK6G,QAAA,CAAS7G,OAAA;IACxBC,UAAA,GAAa,KAAK4G,QAAA,CAAS5G,UAAA;IAC3BC,UAAA,GAAa,KAAK2G,QAAA,CAAS3G,UAAA;IAC3BC,QAAA,GAAW,KAAK0G,QAAA,CAAS1G,QAAA;IACzBC,aAAA,GAAgB,KAAKyG,QAAA,CAASzG,aAAA;IAC9BU,gBAAA,GAAmB,KAAK+F,QAAA,CAAS/F,gBAAA;IACjCC,gBAAA,GAAmB,KAAK8F,QAAA,CAAS9F,gBAAA;IACjCgE,OAAA;IACA9D,uBAAA,GAA0B,KAAK4F,QAAA,CAAS5F;EAC5C,GAAoD;IAChD,MAAMvM,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,qBAAqB;IAExD,IAAIgU,aAAA,KAAkB,QAAQ;MAC1B,MAAM,IAAI3U,KAAA,CAAM,2DAA2D;IAC/E;IAEA,MAAM0F,GAAA,GAAM,MAAM,KAAKsW,eAAA,CAAgBnJ,wBAAA,CAAyB;IAChEjJ,OAAA,CAAOvK,KAAA,CAAM,mCAAmCqG,GAAG;IAEnD,MAAM4W,aAAA,GAAgB,MAAM/B,aAAA,CAAc5Z,MAAA,CAAO;MAC7C+E,GAAA;MACAgP,SAAA,EAAW,KAAKqH,QAAA,CAASrH,SAAA;MACzBlQ,SAAA,EAAW,KAAKuX,QAAA,CAASvX,SAAA;MACzBqQ,YAAA;MACAF,aAAA;MACAC,KAAA;MACAoF,UAAA,EAAYjP,KAAA;MACZG,SAAA;MACA8J,MAAA;MAAQC,OAAA;MAASC,OAAA;MAASC,UAAA;MAAY8F,aAAA;MAAeoB,UAAA;MAAYjH,UAAA;MAAY6E,OAAA;MAC7E5E,QAAA;MAAU8G,OAAA;MAASC,WAAA;MAAapG,gBAAA;MAAkBC,gBAAA;MAAkBkD,YAAA;MAAc7D,aAAA;MAClF7Q,aAAA,EAAe,KAAKsX,QAAA,CAAStX,aAAA;MAC7BsT,YAAA;MACAjS,KAAA;MACA8P,WAAA,EAAa,KAAKmG,QAAA,CAASnG,WAAA;MAC3BO;IACJ,CAAC;IAGD,MAAM,KAAKoD,eAAA,CAAgB;IAE3B,MAAMgD,WAAA,GAAcD,aAAA,CAAcvR,KAAA;IAClC,MAAM,KAAKgR,QAAA,CAASlG,UAAA,CAAW9B,GAAA,CAAIwI,WAAA,CAAYjE,EAAA,EAAIiE,WAAA,CAAYnD,eAAA,CAAgB,CAAC;IAChF,OAAOkD,aAAA;EACX;EAEA,MAAaE,wBAAwB9W,GAAA,EAAa+W,WAAA,GAAc,OAAkE;IAC9H,MAAM7S,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,yBAAyB;IAE5D,MAAM+O,QAAA,GAAW,IAAI+K,cAAA,CAAevQ,QAAA,CAASC,UAAA,CAAWzE,GAAA,EAAK,KAAKqW,QAAA,CAASzG,aAAa,CAAC;IACzF,IAAI,CAAC5F,QAAA,CAAS3E,KAAA,EAAO;MACjBnB,OAAA,CAAOnJ,KAAA,CAAM,IAAIT,KAAA,CAAM,sBAAsB,CAAC;MAG9C,MAAM;IACV;IAEA,MAAM0c,iBAAA,GAAoB,MAAM,KAAKX,QAAA,CAASlG,UAAA,CAAW4G,WAAA,GAAc,WAAW,KAAK,EAAE/M,QAAA,CAAS3E,KAAK;IACvG,IAAI,CAAC2R,iBAAA,EAAmB;MACpB9S,OAAA,CAAOnJ,KAAA,CAAM,IAAIT,KAAA,CAAM,oCAAoC,CAAC;MAE5D,MAAM;IACV;IAEA,MAAM+K,KAAA,GAAQ,MAAM6O,WAAA,CAAYP,iBAAA,CAAkBqD,iBAAiB;IACnE,OAAO;MAAE3R,KAAA;MAAO2E;IAAS;EAC7B;EAEA,MAAaiN,sBAAsBjX,GAAA,EAAaoL,YAAA,EAA4C2L,WAAA,GAAc,MAA+B;IACrI,MAAM7S,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,uBAAuB;IAE1D,MAAM;MAAEoK,KAAA;MAAO2E;IAAS,IAAI,MAAM,KAAK8M,uBAAA,CAAwB9W,GAAA,EAAK+W,WAAW;IAC/E7S,OAAA,CAAOvK,KAAA,CAAM,kDAAkD;IAE/D,IAAI,KAAK0c,QAAA,CAAS7F,IAAA,IAAQ,KAAK6F,QAAA,CAAS7F,IAAA,CAAKvC,KAAA,EAAO;MAChD,MAAMiJ,SAAA,GAAY,MAAM,KAAKC,YAAA,CAAa,KAAKd,QAAA,CAAS7F,IAAA,CAAKvC,KAAK;MAClE7C,YAAA,GAAe;QAAE,GAAGA,YAAA;QAAc,QAAQ8L;MAAU;IACxD;IAUA,IAAI;MACA,MAAM,KAAKX,UAAA,CAAWxE,sBAAA,CAAuB/H,QAAA,EAAU3E,KAAA,EAAO+F,YAAY;IAC9E,SACOpQ,GAAA,EAAK;MACR,IAAIA,GAAA,YAAeiO,cAAA,IAAkB,KAAKoN,QAAA,CAAS7F,IAAA,EAAM;QACrD,MAAM0G,SAAA,GAAY,MAAM,KAAKC,YAAA,CAAa,KAAKd,QAAA,CAAS7F,IAAA,CAAKvC,KAAA,EAAOjT,GAAA,CAAIoF,KAAK;QAC7EgL,YAAA,CAAc,MAAM,IAAI8L,SAAA;QACxB,MAAM,KAAKX,UAAA,CAAWxE,sBAAA,CAAuB/H,QAAA,EAAU3E,KAAA,EAAO+F,YAAY;MAC9E,OAAO;QACH,MAAMpQ,GAAA;MACV;IACJ;IAEA,OAAOgP,QAAA;EACX;EAEA,MAAMmN,aAAaC,SAAA,EAAsBhX,KAAA,EAAiC;IACtE,IAAID,OAAA;IACJ,IAAIkX,SAAA;IAEJ,IAAI,EAAE,MAAMD,SAAA,CAAU7I,UAAA,CAAW,GAAGzC,QAAA,CAAS,KAAKuK,QAAA,CAASvX,SAAS,GAAG;MACnEqB,OAAA,GAAU,MAAMhE,WAAA,CAAY+E,gBAAA,CAAiB;MAC7CmW,SAAA,GAAY,IAAIlB,SAAA,CAAUhW,OAAA,EAASC,KAAK;MACxC,MAAMgX,SAAA,CAAU/I,GAAA,CAAI,KAAKgI,QAAA,CAASvX,SAAA,EAAWuY,SAAS;IAC1D,OAAO;MACHA,SAAA,GAAY,MAAMD,SAAA,CAAU1M,GAAA,CAAI,KAAK2L,QAAA,CAASvX,SAAS;MAGvD,IAAIuY,SAAA,CAAUjX,KAAA,KAAUA,KAAA,IAASA,KAAA,EAAO;QACpCiX,SAAA,CAAUjX,KAAA,GAAQA,KAAA;QAClB,MAAMgX,SAAA,CAAU/I,GAAA,CAAI,KAAKgI,QAAA,CAASvX,SAAA,EAAWuY,SAAS;MAC1D;IACJ;IAEA,OAAO,MAAMlb,WAAA,CAAY4D,iBAAA,CAAkB;MACvCC,GAAA,EAAK,MAAM,KAAKsW,eAAA,CAAgBjJ,gBAAA,CAAiB,KAAK;MACtDnN,UAAA,EAAY;MACZC,OAAA,EAASkX,SAAA,CAAU5L,IAAA;MACnBrL,KAAA,EAAOiX,SAAA,CAAUjX;IACrB,CAAC;EACL;EAEA,MAAakX,wCAAwC;IACjDC,QAAA;IACAC,QAAA;IACAnF,YAAA,GAAe;IACf9B,gBAAA,GAAmB,CAAC;EACxB,GAAyE;IACrE,MAAMyC,aAAA,GAAyC,MAAM,KAAKlB,YAAA,CAAaT,mBAAA,CAAoB;MAAEkG,QAAA;MAAUC,QAAA;MAAU,GAAGjH;IAAiB,CAAC;IACtI,MAAMkH,cAAA,GAAiC,IAAI1C,cAAA,CAAe,IAAIjQ,eAAA,CAAgB,CAAC;IAC/E1J,MAAA,CAAO2R,MAAA,CAAO0K,cAAA,EAAgBzE,aAAa;IAC3C,MAAM,KAAKuD,UAAA,CAAWjE,2BAAA,CAA4BmF,cAAA,EAAgBpF,YAAY;IAC9E,OAAOoF,cAAA;EACX;EAEA,MAAaC,gBAAgB;IACzBrS,KAAA;IACA8J,YAAA;IACAQ,QAAA;IACAnG,gBAAA;IACA4B,YAAA;IACAmF;EACJ,GAAiD;IAjQrD,IAAAlO,EAAA;IAkQQ,MAAM6B,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,iBAAiB;IAKpD,IAAIiU,KAAA;IACJ,IAAI,KAAKmH,QAAA,CAAShG,wBAAA,KAA6B,QAAW;MACtDnB,KAAA,GAAQ7J,KAAA,CAAM6J,KAAA;IAClB,OAAO;MACH,MAAMyI,eAAA,GAAkB,KAAKtB,QAAA,CAAShG,wBAAA,CAAyB8E,KAAA,CAAM,GAAG;MACxE,MAAMyC,cAAA,KAAiBvV,EAAA,GAAAgD,KAAA,CAAM6J,KAAA,KAAN,gBAAA7M,EAAA,CAAa8S,KAAA,CAAM,SAAQ,EAAC;MAEnDjG,KAAA,GAAQ0I,cAAA,CAAevU,MAAA,CAAOwU,CAAA,IAAKF,eAAA,CAAgB7L,QAAA,CAAS+L,CAAC,CAAC,EAAEla,IAAA,CAAK,GAAG;IAC5E;IAEA,IAAI,KAAK0Y,QAAA,CAAS7F,IAAA,IAAQ,KAAK6F,QAAA,CAAS7F,IAAA,CAAKvC,KAAA,EAAO;MAChD,MAAMiJ,SAAA,GAAY,MAAM,KAAKC,YAAA,CAAa,KAAKd,QAAA,CAAS7F,IAAA,CAAKvC,KAAK;MAClE7C,YAAA,GAAe;QAAE,GAAGA,YAAA;QAAc,QAAQ8L;MAAU;IACxD;IAUA,IAAItB,MAAA;IACJ,IAAI;MACAA,MAAA,GAAS,MAAM,KAAK9D,YAAA,CAAaR,oBAAA,CAAqB;QAClDC,aAAA,EAAelM,KAAA,CAAMkM,aAAA;QAAA;QAErBrC,KAAA;QACAC,YAAA;QACAQ,QAAA;QACAnG,gBAAA;QACA4B,YAAA;QACA,GAAGmF;MACP,CAAC;IACL,SAASvV,GAAA,EAAK;MACV,IAAIA,GAAA,YAAeiO,cAAA,IAAkB,KAAKoN,QAAA,CAAS7F,IAAA,EAAM;QACrDpF,YAAA,CAAc,MAAM,IAAI,MAAM,KAAK+L,YAAA,CAAa,KAAKd,QAAA,CAAS7F,IAAA,CAAKvC,KAAA,EAAOjT,GAAA,CAAIoF,KAAK;QACnFwV,MAAA,GAAS,MAAM,KAAK9D,YAAA,CAAaR,oBAAA,CAAqB;UAClDC,aAAA,EAAelM,KAAA,CAAMkM,aAAA;UAAA;UAErBrC,KAAA;UACAC,YAAA;UACAQ,QAAA;UACAnG,gBAAA;UACA4B,YAAA;UACA,GAAGmF;QACP,CAAC;MACL,OAAO;QACH,MAAMvV,GAAA;MACV;IACJ;IAEA,MAAMgP,QAAA,GAAW,IAAI+K,cAAA,CAAe,IAAIjQ,eAAA,CAAgB,CAAC;IACzD1J,MAAA,CAAO2R,MAAA,CAAO/C,QAAA,EAAU4L,MAAM;IAC9B1R,OAAA,CAAOvK,KAAA,CAAM,uBAAuBqQ,QAAQ;IAC5C,MAAM,KAAKuM,UAAA,CAAW/D,uBAAA,CAAwBxI,QAAA,EAAU;MACpD,GAAG3E,KAAA;MAAA;MAAA;MAGH6J;IACJ,CAAC;IACD,OAAOlF,QAAA;EACX;EAEA,MAAa8N,qBAAqB;IAC9BzS,KAAA;IACAkQ,aAAA;IACAzW,SAAA;IACA2U,YAAA;IACAjO,SAAA;IACA4J,wBAAA,GAA2B,KAAKiH,QAAA,CAASjH,wBAAA;IACzCkB,gBAAA,GAAmB,KAAK+F,QAAA,CAAS/F;EACrC,IAA8B,CAAC,GAA4B;IACvD,MAAMpM,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,sBAAsB;IAEzD,MAAM+E,GAAA,GAAM,MAAM,KAAKsW,eAAA,CAAgB9I,qBAAA,CAAsB;IAC7D,IAAI,CAACxN,GAAA,EAAK;MACNkE,OAAA,CAAOnJ,KAAA,CAAM,IAAIT,KAAA,CAAM,yBAAyB,CAAC;MAEjD,MAAM;IACV;IAEA4J,OAAA,CAAOvK,KAAA,CAAM,iCAAiCqG,GAAG;IAGjD,IAAI,CAAClB,SAAA,IAAasQ,wBAAA,IAA4B,CAACmG,aAAA,EAAe;MAC1DzW,SAAA,GAAY,KAAKuX,QAAA,CAASvX,SAAA;IAC9B;IAEA,MAAM2X,OAAA,GAAU,IAAInB,cAAA,CAAe;MAC/BtV,GAAA;MACAuV,aAAA;MACAzW,SAAA;MACAsQ,wBAAA;MACAkF,UAAA,EAAYjP,KAAA;MACZiL,gBAAA;MACAmD,YAAA;MACAjO;IACJ,CAAC;IAGD,MAAM,KAAKqO,eAAA,CAAgB;IAE3B,MAAMkE,YAAA,GAAetB,OAAA,CAAQpR,KAAA;IAC7B,IAAI0S,YAAA,EAAc;MACd7T,OAAA,CAAOvK,KAAA,CAAM,sCAAsC;MACnD,MAAM,KAAK0c,QAAA,CAASlG,UAAA,CAAW9B,GAAA,CAAI0J,YAAA,CAAanF,EAAA,EAAImF,YAAA,CAAarE,eAAA,CAAgB,CAAC;IACtF;IAEA,OAAO+C,OAAA;EACX;EAEA,MAAauB,yBAAyBhY,GAAA,EAAa+W,WAAA,GAAc,OAAyE;IACtI,MAAM7S,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,0BAA0B;IAE7D,MAAM+O,QAAA,GAAW,IAAIwL,eAAA,CAAgBhR,QAAA,CAASC,UAAA,CAAWzE,GAAA,EAAK,KAAKqW,QAAA,CAASzG,aAAa,CAAC;IAC1F,IAAI,CAAC5F,QAAA,CAAS3E,KAAA,EAAO;MACjBnB,OAAA,CAAOvK,KAAA,CAAM,sBAAsB;MAEnC,IAAIqQ,QAAA,CAASlQ,KAAA,EAAO;QAChBoK,OAAA,CAAOrK,IAAA,CAAK,uBAAuBmQ,QAAA,CAASlQ,KAAK;QACjD,MAAM,IAAImL,aAAA,CAAc+E,QAAQ;MACpC;MAEA,OAAO;QAAE3E,KAAA,EAAO;QAAW2E;MAAS;IACxC;IAEA,MAAMgN,iBAAA,GAAoB,MAAM,KAAKX,QAAA,CAASlG,UAAA,CAAW4G,WAAA,GAAc,WAAW,KAAK,EAAE/M,QAAA,CAAS3E,KAAK;IACvG,IAAI,CAAC2R,iBAAA,EAAmB;MACpB9S,OAAA,CAAOnJ,KAAA,CAAM,IAAIT,KAAA,CAAM,oCAAoC,CAAC;MAE5D,MAAM;IACV;IAEA,MAAM+K,KAAA,GAAQ,MAAMiO,KAAA,CAAMK,iBAAA,CAAkBqD,iBAAiB;IAC7D,OAAO;MAAE3R,KAAA;MAAO2E;IAAS;EAC7B;EAEA,MAAaiO,uBAAuBjY,GAAA,EAAuC;IACvE,MAAMkE,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,wBAAwB;IAE3D,MAAM;MAAEoK,KAAA;MAAO2E;IAAS,IAAI,MAAM,KAAKgO,wBAAA,CAAyBhY,GAAA,EAAK,IAAI;IACzE,IAAIqF,KAAA,EAAO;MACPnB,OAAA,CAAOvK,KAAA,CAAM,kDAAkD;MAC/D,KAAK4c,UAAA,CAAW5D,uBAAA,CAAwB3I,QAAA,EAAU3E,KAAK;IAC3D,OAAO;MACHnB,OAAA,CAAOvK,KAAA,CAAM,qDAAqD;IACtE;IAEA,OAAOqQ,QAAA;EACX;EAEO6J,gBAAA,EAAiC;IACpC,KAAKrS,OAAA,CAAQvG,MAAA,CAAO,iBAAiB;IACrC,OAAOqY,KAAA,CAAMO,eAAA,CAAgB,KAAKwC,QAAA,CAASlG,UAAA,EAAY,KAAKkG,QAAA,CAAStG,sBAAsB;EAC/F;EAEA,MAAamI,YAAYrc,KAAA,EAAesc,IAAA,EAAwD;IAC5F,KAAK3W,OAAA,CAAQvG,MAAA,CAAO,aAAa;IACjC,OAAO,MAAM,KAAK6W,YAAA,CAAaL,MAAA,CAAO;MAClC5V,KAAA;MACA6V,eAAA,EAAiByG;IACrB,CAAC;EACL;AACJ;;;ACjaO,IAAMC,cAAA,GAAN,MAAqB;EAMjB1d,YAA6B2d,YAAA,EAA2B;IAA3B,KAAAA,YAAA,GAAAA,YAAA;IALpC,KAAiB7W,OAAA,GAAU,IAAIhH,MAAA,CAAO,gBAAgB;IAyCtD,KAAU8d,MAAA,GAAS,MACfC,IAAA,IAIgB;MAChB,MAAMhT,aAAA,GAAgBgT,IAAA,CAAKhT,aAAA;MAC3B,IAAI,CAACA,aAAA,EAAe;QAChB;MACJ;MACA,MAAMrB,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,QAAQ;MAE3C,IAAIsd,IAAA,CAAK9F,OAAA,EAAS;QACd,KAAK+F,IAAA,GAAOD,IAAA,CAAK9F,OAAA,CAAQK,GAAA;QACzB5O,OAAA,CAAOvK,KAAA,CAAM,iBAAiB4L,aAAA,EAAe,SAAS,KAAKiT,IAAI;MACnE,OACK;QACD,KAAKA,IAAA,GAAO;QACZtU,OAAA,CAAOvK,KAAA,CAAM,iBAAiB4L,aAAA,EAAe,kBAAkB;MACnE;MAEA,IAAI,KAAKkT,mBAAA,EAAqB;QAC1B,KAAKA,mBAAA,CAAoBpQ,KAAA,CAAM9C,aAAa;QAC5C;MACJ;MAEA,IAAI;QACA,MAAMvF,GAAA,GAAM,MAAM,KAAKqY,YAAA,CAAa/B,eAAA,CAAgB/I,qBAAA,CAAsB;QAC1E,IAAIvN,GAAA,EAAK;UACLkE,OAAA,CAAOvK,KAAA,CAAM,mCAAmC;UAEhD,MAAMmF,SAAA,GAAY,KAAKuZ,YAAA,CAAahC,QAAA,CAASvX,SAAA;UAC7C,MAAM4Z,iBAAA,GAAoB,KAAKL,YAAA,CAAahC,QAAA,CAASsC,6BAAA;UACrD,MAAMC,WAAA,GAAc,KAAKP,YAAA,CAAahC,QAAA,CAASwC,uBAAA;UAE/C,MAAMC,kBAAA,GAAqB,IAAInS,kBAAA,CAAmB,KAAK/C,SAAA,EAAW9E,SAAA,EAAWkB,GAAA,EAAK0Y,iBAAA,EAAmBE,WAAW;UAChH,MAAME,kBAAA,CAAmB/S,IAAA,CAAK;UAC9B,KAAK0S,mBAAA,GAAsBK,kBAAA;UAC3BA,kBAAA,CAAmBzQ,KAAA,CAAM9C,aAAa;QAC1C,OACK;UACDrB,OAAA,CAAOrK,IAAA,CAAK,+CAA+C;QAC/D;MACJ,SACOmB,GAAA,EAAK;QAERkJ,OAAA,CAAOpK,KAAA,CAAM,qCAAqCkB,GAAA,YAAeV,KAAA,GAAQU,GAAA,CAAIiE,OAAA,GAAUjE,GAAG;MAC9F;IACJ;IAEA,KAAU+d,KAAA,GAAQ,MAAY;MAC1B,MAAM7U,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,OAAO;MAC1C,KAAKud,IAAA,GAAO;MAEZ,IAAI,KAAKC,mBAAA,EAAqB;QAC1B,KAAKA,mBAAA,CAAoBlR,IAAA,CAAK;MAClC;MAEA,IAAI,KAAK8Q,YAAA,CAAahC,QAAA,CAAS2C,uBAAA,EAAyB;QAIpD,MAAMC,WAAA,GAAc3U,WAAA,CAAY,YAAY;UACxCC,aAAA,CAAc0U,WAAW;UAEzB,IAAI;YACA,MAAMC,OAAA,GAAU,MAAM,KAAKb,YAAA,CAAac,kBAAA,CAAmB;YAC3D,IAAID,OAAA,EAAS;cACT,MAAME,OAAA,GAAU;gBACZ7T,aAAA,EAAe2T,OAAA,CAAQ3T,aAAA;gBACvBkN,OAAA,EAASyG,OAAA,CAAQpG,GAAA,GAAM;kBACnBA,GAAA,EAAKoG,OAAA,CAAQpG;gBACjB,IAAI;cACR;cACA,KAAK,KAAKwF,MAAA,CAAOc,OAAO;YAC5B;UACJ,SACOpe,GAAA,EAAK;YAERkJ,OAAA,CAAOpK,KAAA,CAAM,iCAAiCkB,GAAA,YAAeV,KAAA,GAAQU,GAAA,CAAIiE,OAAA,GAAUjE,GAAG;UAC1F;QACJ,GAAG,GAAI;MACX;IACJ;IAEA,KAAU4I,SAAA,GAAY,YAA2B;MAC7C,MAAMM,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,WAAW;MAC9C,IAAI;QACA,MAAMie,OAAA,GAAU,MAAM,KAAKb,YAAA,CAAac,kBAAA,CAAmB;QAC3D,IAAIE,UAAA,GAAa;QAEjB,IAAIH,OAAA,IAAW,KAAKT,mBAAA,EAAqB;UACrC,IAAIS,OAAA,CAAQpG,GAAA,KAAQ,KAAK0F,IAAA,EAAM;YAC3Ba,UAAA,GAAa;YACb,KAAKZ,mBAAA,CAAoBpQ,KAAA,CAAM6Q,OAAA,CAAQ3T,aAAa;YAEpDrB,OAAA,CAAOvK,KAAA,CAAM,6GAA6Guf,OAAA,CAAQ3T,aAAa;YAC/I,MAAM,KAAK8S,YAAA,CAAaiB,MAAA,CAAOC,wBAAA,CAAyB;UAC5D,OACK;YACDrV,OAAA,CAAOvK,KAAA,CAAM,oCAAoCuf,OAAA,CAAQpG,GAAG;UAChE;QACJ,OACK;UACD5O,OAAA,CAAOvK,KAAA,CAAM,kCAAkC;QACnD;QAEA,IAAI0f,UAAA,EAAY;UACZ,IAAI,KAAKb,IAAA,EAAM;YACX,MAAM,KAAKH,YAAA,CAAaiB,MAAA,CAAOE,mBAAA,CAAoB;UACvD,OACK;YACD,MAAM,KAAKnB,YAAA,CAAaiB,MAAA,CAAOG,kBAAA,CAAmB;UACtD;QACJ,OAAO;UACHvV,OAAA,CAAOvK,KAAA,CAAM,kDAAkD;QACnE;MACJ,SACOqB,GAAA,EAAK;QACR,IAAI,KAAKwd,IAAA,EAAM;UACXtU,OAAA,CAAOvK,KAAA,CAAM,qEAAqEqB,GAAG;UACrF,MAAM,KAAKqd,YAAA,CAAaiB,MAAA,CAAOE,mBAAA,CAAoB;QACvD;MACJ;IACJ;IA/JI,IAAI,CAACnB,YAAA,EAAc;MACf,KAAK7W,OAAA,CAAQzG,KAAA,CAAM,IAAIT,KAAA,CAAM,wBAAwB,CAAC;IAC1D;IAEA,KAAK+d,YAAA,CAAaiB,MAAA,CAAOI,aAAA,CAAc,KAAKpB,MAAM;IAClD,KAAKD,YAAA,CAAaiB,MAAA,CAAOK,eAAA,CAAgB,KAAKZ,KAAK;IAEnD,KAAKa,KAAA,CAAM,EAAEC,KAAA,CAAO7e,GAAA,IAAiB;MAEjC,KAAKwG,OAAA,CAAQ1H,KAAA,CAAMkB,GAAG;IAC1B,CAAC;EACL;EAEA,MAAgB4e,MAAA,EAAuB;IACnC,KAAKpY,OAAA,CAAQvG,MAAA,CAAO,OAAO;IAC3B,MAAMsd,IAAA,GAAO,MAAM,KAAKF,YAAA,CAAayB,OAAA,CAAQ;IAG7C,IAAIvB,IAAA,EAAM;MACN,KAAK,KAAKD,MAAA,CAAOC,IAAI;IACzB,WACS,KAAKF,YAAA,CAAahC,QAAA,CAAS2C,uBAAA,EAAyB;MACzD,MAAME,OAAA,GAAU,MAAM,KAAKb,YAAA,CAAac,kBAAA,CAAmB;MAC3D,IAAID,OAAA,EAAS;QACT,MAAME,OAAA,GAAU;UACZ7T,aAAA,EAAe2T,OAAA,CAAQ3T,aAAA;UACvBkN,OAAA,EAASyG,OAAA,CAAQpG,GAAA,GAAM;YACnBA,GAAA,EAAKoG,OAAA,CAAQpG;UACjB,IAAI;QACR;QACA,KAAK,KAAKwF,MAAA,CAAOc,OAAO;MAC5B;IACJ;EACJ;AA+HJ;;;AClKO,IAAMW,IAAA,GAAN,MAAMC,KAAA,CAAK;EAuCPtf,YAAYE,IAAA,EAWhB;IAlEP,IAAAyH,EAAA;IAmEQ,KAAKkQ,QAAA,GAAW3X,IAAA,CAAK2X,QAAA;IACrB,KAAKhN,aAAA,IAAgBlD,EAAA,GAAAzH,IAAA,CAAK2K,aAAA,KAAL,OAAAlD,EAAA,GAAsB;IAC3C,KAAK4D,YAAA,GAAerL,IAAA,CAAKqL,YAAA;IACzB,KAAKsL,aAAA,GAAgB3W,IAAA,CAAK2W,aAAA;IAE1B,KAAKyD,UAAA,GAAapa,IAAA,CAAKoa,UAAA;IACvB,KAAK9F,KAAA,GAAQtU,IAAA,CAAKsU,KAAA;IAClB,KAAKuD,OAAA,GAAU7X,IAAA,CAAK6X,OAAA;IACpB,KAAK2C,UAAA,GAAaxa,IAAA,CAAKwa,UAAA;IACvB,KAAK/P,KAAA,GAAQzK,IAAA,CAAK0K,SAAA;IAClB,KAAKE,SAAA,GAAY5K,IAAA,CAAK4K,SAAA;EAC1B;EAAA;EAGA,IAAWU,WAAA,EAAiC;IACxC,IAAI,KAAKkP,UAAA,KAAe,QAAW;MAC/B,OAAO;IACX;IACA,OAAO,KAAKA,UAAA,GAAa7R,KAAA,CAAMO,YAAA,CAAa;EAChD;EAEA,IAAWoC,WAAW7L,KAAA,EAA2B;IAC7C,IAAIA,KAAA,KAAU,QAAW;MACrB,KAAK+a,UAAA,GAAa5U,IAAA,CAAKC,KAAA,CAAMpG,KAAK,IAAIkJ,KAAA,CAAMO,YAAA,CAAa;IAC7D;EACJ;EAAA;EAGA,IAAWuC,QAAA,EAA+B;IACtC,MAAMH,UAAA,GAAa,KAAKA,UAAA;IACxB,IAAIA,UAAA,KAAe,QAAW;MAC1B,OAAO;IACX;IACA,OAAOA,UAAA,IAAc;EACzB;EAAA;EAGA,IAAW+T,OAAA,EAAmB;IAxGlC,IAAA5X,EAAA,EAAAC,EAAA;IAyGQ,QAAOA,EAAA,IAAAD,EAAA,QAAK6M,KAAA,KAAL,gBAAA7M,EAAA,CAAY8S,KAAA,CAAM,SAAlB,OAAA7S,EAAA,GAA0B,EAAC;EACtC;EAEOoR,gBAAA,EAA0B;IAC7B,IAAIlZ,MAAA,CAAO,MAAM,EAAES,MAAA,CAAO,iBAAiB;IAC3C,OAAOsB,IAAA,CAAKC,SAAA,CAAU;MAClB+V,QAAA,EAAU,KAAKA,QAAA;MACfhN,aAAA,EAAe,KAAKA,aAAA;MACpBU,YAAA,EAAc,KAAKA,YAAA;MACnBsL,aAAA,EAAe,KAAKA,aAAA;MACpByD,UAAA,EAAY,KAAKA,UAAA;MACjB9F,KAAA,EAAO,KAAKA,KAAA;MACZuD,OAAA,EAAS,KAAKA,OAAA;MACd2C,UAAA,EAAY,KAAKA;IACrB,CAAC;EACL;EAEA,OAAczB,kBAAkBC,aAAA,EAA6B;IACzDpZ,MAAA,CAAOa,YAAA,CAAa,QAAQ,mBAAmB;IAC/C,OAAO,IAAI2e,KAAA,CAAKzd,IAAA,CAAK+O,KAAA,CAAMsI,aAAa,CAAC;EAC7C;AACJ;;;ACxHA,IAAMsG,aAAA,GAAgB;AAcf,IAAeC,mBAAA,GAAf,MAAsD;EAAtDzf,YAAA;IAEH,KAAmB0f,MAAA,GAAS,IAAI9Y,KAAA,CAAuB,2BAA2B;IAClF,KAAmB+Y,gBAAA,GAAmB,mBAAIC,GAAA,CAAgB;IAE1D,KAAUC,OAAA,GAA8B;EAAA;EAExC,MAAaC,SAAS3V,MAAA,EAAmD;IACrE,MAAMX,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,UAAU;IAC7C,IAAI,CAAC,KAAKsf,OAAA,EAAS;MACf,MAAM,IAAIjgB,KAAA,CAAM,4CAA4C;IAChE;IAEA4J,OAAA,CAAOvK,KAAA,CAAM,uBAAuB;IACpC,KAAK4gB,OAAA,CAAQE,QAAA,CAAStc,OAAA,CAAQ0G,MAAA,CAAO7E,GAAG;IAExC,MAAM;MAAEA,GAAA;MAAK0a;IAAS,IAAI,MAAM,IAAI3S,OAAA,CAAqB,CAACC,OAAA,EAAS2S,MAAA,KAAW;MAC1E,MAAMC,QAAA,GAAYpb,CAAA,IAAoB;QArClD,IAAA6C,EAAA;QAsCgB,MAAM3D,IAAA,GAAgCc,CAAA,CAAEd,IAAA;QACxC,MAAMwI,MAAA,IAAS7E,EAAA,GAAAwC,MAAA,CAAOgW,YAAA,KAAP,OAAAxY,EAAA,GAAuBzF,MAAA,CAAO6d,QAAA,CAASvT,MAAA;QACtD,IAAI1H,CAAA,CAAE0H,MAAA,KAAWA,MAAA,KAAUxI,IAAA,oBAAAA,IAAA,CAAM0I,MAAA,MAAW8S,aAAA,EAAe;UAEvD;QACJ;QACA,IAAI;UACA,MAAM7U,KAAA,GAAQb,QAAA,CAASC,UAAA,CAAW/F,IAAA,CAAKsB,GAAA,EAAK6E,MAAA,CAAO+K,aAAa,EAAElF,GAAA,CAAI,OAAO;UAC7E,IAAI,CAACrF,KAAA,EAAO;YACRnB,OAAA,CAAOrK,IAAA,CAAK,gCAAgC;UAChD;UACA,IAAI2F,CAAA,CAAE4H,MAAA,KAAW,KAAKmT,OAAA,IAAWlV,KAAA,KAAUR,MAAA,CAAOQ,KAAA,EAAO;YAGrD;UACJ;QACJ,QACM;UACF,KAAKyV,QAAA,CAAS;UACdH,MAAA,CAAO,IAAIrgB,KAAA,CAAM,8BAA8B,CAAC;QACpD;QACA0N,OAAA,CAAQtJ,IAAI;MAChB;MACA9B,MAAA,CAAOwL,gBAAA,CAAiB,WAAWwS,QAAA,EAAU,KAAK;MAClD,KAAKP,gBAAA,CAAiBU,GAAA,CAAI,MAAMne,MAAA,CAAOoe,mBAAA,CAAoB,WAAWJ,QAAA,EAAU,KAAK,CAAC;MACtF,KAAKP,gBAAA,CAAiBU,GAAA,CAAI,KAAKX,MAAA,CAAO3Y,UAAA,CAAYwZ,MAAA,IAAW;QACzD,KAAKH,QAAA,CAAS;QACdH,MAAA,CAAOM,MAAM;MACjB,CAAC,CAAC;IACN,CAAC;IACD/W,OAAA,CAAOvK,KAAA,CAAM,0BAA0B;IACvC,KAAKmhB,QAAA,CAAS;IAEd,IAAI,CAACJ,QAAA,EAAU;MACX,KAAKQ,KAAA,CAAM;IACf;IAEA,OAAO;MAAElb;IAAI;EACjB;EAIQ8a,SAAA,EAAiB;IACrB,KAAKtZ,OAAA,CAAQvG,MAAA,CAAO,UAAU;IAE9B,WAAWkgB,OAAA,IAAW,KAAKd,gBAAA,EAAkB;MACzCc,OAAA,CAAQ;IACZ;IACA,KAAKd,gBAAA,CAAiB3R,KAAA,CAAM;EAChC;EAEA,OAAiB0S,cAAcC,MAAA,EAAgBrb,GAAA,EAAa0a,QAAA,GAAW,OAAOY,YAAA,GAAe1e,MAAA,CAAO6d,QAAA,CAASvT,MAAA,EAAc;IACvHmU,MAAA,CAAO9S,WAAA,CAAY;MACfnB,MAAA,EAAQ8S,aAAA;MACRla,GAAA;MACA0a;IACJ,GAAkBY,YAAY;EAClC;AACJ;;;ACxFO,IAAMC,0BAAA,GAAkD;EAC3Dd,QAAA,EAAU;EACVe,OAAA,EAAS;EACTzY,MAAA,EAAQ;EACR0Y,8BAAA,EAAgC;AACpC;AACO,IAAMC,kBAAA,GAAqB;AAClC,IAAMC,mDAAA,GAAsD;AAC5D,IAAMC,oCAAA,GAAuC;AACtC,IAAMC,oCAAA,GAAuC;AA4E7C,IAAMC,wBAAA,GAAN,cAAuC/M,uBAAA,CAAwB;EA+B3DrU,YAAYE,IAAA,EAA2B;IAC1C,MAAM;MACFmhB,kBAAA,GAAqBnhB,IAAA,CAAKuU,YAAA;MAC1B6M,8BAAA,GAAiCphB,IAAA,CAAKwU,wBAAA;MACtC6M,mBAAA,GAAsBV,0BAAA;MACtBW,iBAAA,GAAoBR,kBAAA;MACpBS,cAAA,GAAiB;MACjBC,cAAA,GAAiB;MAEjBC,wBAAA,GAA2BzhB,IAAA,CAAKyhB,wBAAA;MAChCC,kBAAA,GAAqB1hB,IAAA,CAAK0hB,kBAAA;MAE1BxP,uBAAA;MACAyP,mBAAA,GAAsB3hB,IAAA,CAAKuU,YAAA;MAC3BqN,6BAAA;MACAC,oBAAA,GAAuB;MACvBC,wBAAA,GAA2B;MAC3BC,2BAAA,GAA8B;MAE9BC,cAAA,GAAiB;MACjB5D,uBAAA,GAA0B;MAC1BL,6BAAA,GAAgCiD,oCAAA;MAChCiB,0BAAA,GAA6B;MAC7BhE,uBAAA,GAA0B;MAE1BiE,gBAAA,GAAmB,CAAC,gBAAgB,eAAe;MACnDC,qBAAA,GAAwB;MACxBC,6BAAA,GAAgC;MAEhCC,4CAAA,GAA+CtB,mDAAA;MAE/CuB;IACJ,IAAItiB,IAAA;IAEJ,MAAMA,IAAI;IAEV,KAAKmhB,kBAAA,GAAqBA,kBAAA;IAC1B,KAAKC,8BAAA,GAAiCA,8BAAA;IACtC,KAAKC,mBAAA,GAAsBA,mBAAA;IAC3B,KAAKC,iBAAA,GAAoBA,iBAAA;IACzB,KAAKC,cAAA,GAAiBA,cAAA;IACtB,KAAKC,cAAA,GAAiBA,cAAA;IAEtB,KAAKC,wBAAA,GAA2BA,wBAAA;IAChC,KAAKC,kBAAA,GAAqBA,kBAAA;IAE1B,KAAKC,mBAAA,GAAsBA,mBAAA;IAC3B,KAAKC,6BAAA,GAAgCA,6BAAA,IAAiC1P,uBAAA,IAA2B+O,oCAAA;IACjG,KAAKY,oBAAA,GAAuBA,oBAAA;IAC5B,KAAKC,wBAAA,GAA2BA,wBAAA;IAChC,KAAKC,2BAAA,GAA8BA,2BAAA;IAEnC,KAAKC,cAAA,GAAiBA,cAAA;IACtB,KAAK5D,uBAAA,GAA0BA,uBAAA;IAC/B,KAAKL,6BAAA,GAAgCA,6BAAA;IACrC,KAAKE,uBAAA,GAA0BA,uBAAA;IAC/B,KAAKgE,0BAAA,GAA6BA,0BAAA;IAElC,KAAKC,gBAAA,GAAmBA,gBAAA;IACxB,KAAKC,qBAAA,GAAwBA,qBAAA;IAC7B,KAAKC,6BAAA,GAAgCA,6BAAA;IAErC,KAAKC,4CAAA,GAA+CA,4CAAA;IAEpD,IAAIC,SAAA,EAAW;MACX,KAAKA,SAAA,GAAYA,SAAA;IACrB,OACK;MACD,MAAMjP,KAAA,GAAQ,OAAOrR,MAAA,KAAW,cAAcA,MAAA,CAAOugB,cAAA,GAAiB,IAAI3U,kBAAA,CAAmB;MAC7F,KAAK0U,SAAA,GAAY,IAAIlP,oBAAA,CAAqB;QAAEC;MAAM,CAAC;IACvD;EACJ;AACJ;;;ACjLO,IAAMmP,YAAA,GAAN,MAAMC,aAAA,SAAqBlD,mBAAA,CAAoB;EAK3Czf,YAAY;IACf8hB,6BAAA,GAAgCX;EACpC,GAAuB;IACnB,MAAM;IAPV,KAAmBra,OAAA,GAAU,IAAIhH,MAAA,CAAO,cAAc;IAQlD,KAAK8iB,iBAAA,GAAoBd,6BAAA;IAEzB,KAAKnV,MAAA,GAASgW,aAAA,CAAaE,kBAAA,CAAmB;IAC9C,KAAKhD,OAAA,GAAU,KAAKlT,MAAA,CAAOC,aAAA;EAC/B;EAEA,OAAeiW,mBAAA,EAAwC;IACnD,MAAMC,MAAA,GAAS5gB,MAAA,CAAO4K,QAAA,CAASC,aAAA,CAAc,QAAQ;IAGrD+V,MAAA,CAAO9V,KAAA,CAAMC,UAAA,GAAa;IAC1B6V,MAAA,CAAO9V,KAAA,CAAME,QAAA,GAAW;IACxB4V,MAAA,CAAO9V,KAAA,CAAM/E,IAAA,GAAO;IACpB6a,MAAA,CAAO9V,KAAA,CAAM1E,GAAA,GAAM;IACnBwa,MAAA,CAAOhb,KAAA,GAAQ;IACfgb,MAAA,CAAOza,MAAA,GAAS;IAEhBnG,MAAA,CAAO4K,QAAA,CAASU,IAAA,CAAKC,WAAA,CAAYqV,MAAM;IACvC,OAAOA,MAAA;EACX;EAEA,MAAahD,SAAS3V,MAAA,EAAmD;IACrE,KAAKrD,OAAA,CAAQ7H,KAAA,CAAM,+BAA+B,KAAK2jB,iBAAiB;IACxE,MAAMG,KAAA,GAAQ3T,UAAA,CAAW,MAAM,KAAK,KAAKsQ,MAAA,CAAOpY,KAAA,CAAM,IAAIyD,YAAA,CAAa,qCAAqC,CAAC,GAAG,KAAK6X,iBAAA,GAAoB,GAAI;IAC7I,KAAKjD,gBAAA,CAAiBU,GAAA,CAAI,MAAM5Q,YAAA,CAAasT,KAAK,CAAC;IAEnD,OAAO,MAAM,MAAMjD,QAAA,CAAS3V,MAAM;EACtC;EAEOqW,MAAA,EAAc;IAzDzB,IAAA7Y,EAAA;IA0DQ,IAAI,KAAKgF,MAAA,EAAQ;MACb,IAAI,KAAKA,MAAA,CAAOqW,UAAA,EAAY;QACxB,KAAKrW,MAAA,CAAOe,gBAAA,CAAiB,QAASnG,EAAA,IAAO;UA5D7D,IAAA0b,GAAA;UA6DoB,MAAMC,KAAA,GAAQ3b,EAAA,CAAG4b,MAAA;UACjB,CAAAF,GAAA,GAAAC,KAAA,CAAMF,UAAA,KAAN,gBAAAC,GAAA,CAAkBG,WAAA,CAAYF,KAAA;UAC9B,KAAK,KAAKxD,MAAA,CAAOpY,KAAA,CAAM,IAAI1H,KAAA,CAAM,yBAAyB,CAAC;QAC/D,GAAG,IAAI;QACP,CAAA+H,EAAA,QAAKgF,MAAA,CAAOC,aAAA,KAAZ,gBAAAjF,EAAA,CAA2BoY,QAAA,CAAStc,OAAA,CAAQ;MAChD;MACA,KAAKkJ,MAAA,GAAS;IAClB;IACA,KAAKkT,OAAA,GAAU;EACnB;EAEA,OAAcwD,aAAa/d,GAAA,EAAasb,YAAA,EAA6B;IACjE,OAAO,MAAMF,aAAA,CAAcxe,MAAA,CAAOye,MAAA,EAAQrb,GAAA,EAAK,OAAOsb,YAAY;EACtE;AACJ;;;AChEO,IAAM0C,eAAA,GAAN,MAA4C;EAG/CtjB,YAAoBwR,SAAA,EAAqC;IAArC,KAAAA,SAAA,GAAAA,SAAA;IAFpB,KAAiB1K,OAAA,GAAU,IAAIhH,MAAA,CAAO,iBAAiB;EAEG;EAE1D,MAAayjB,QAAQ;IACjBzB,6BAAA,GAAgC,KAAKtQ,SAAA,CAAUsQ;EACnD,GAA8C;IAC1C,OAAO,IAAIY,YAAA,CAAa;MAAEZ;IAA8B,CAAC;EAC7D;EAEA,MAAa0B,SAASle,GAAA,EAA4B;IAC9C,KAAKwB,OAAA,CAAQvG,MAAA,CAAO,UAAU;IAC9BmiB,YAAA,CAAaW,YAAA,CAAa/d,GAAA,EAAK,KAAKkM,SAAA,CAAUmQ,wBAAwB;EAC1E;AACJ;;;AClBA,IAAM8B,2BAAA,GAA8B;AACpC,IAAMC,MAAA,GAAS;AAeR,IAAMC,WAAA,GAAN,cAA0BlE,mBAAA,CAAoB;EAK1Czf,YAAY;IACfwhB,iBAAA,GAAoBR,kBAAA;IACpBO,mBAAA,GAAsB,CAAC;IACvBqC;EACJ,GAAsB;IAClB,MAAM;IATV,KAAmB9c,OAAA,GAAU,IAAIhH,MAAA,CAAO,aAAa;IAUjD,MAAM+jB,aAAA,GAAgBrc,UAAA,CAAWC,MAAA,CAAO;MAAE,GAAGoZ,0BAAA;MAA4B,GAAGU;IAAoB,CAAC;IACjG,KAAK1B,OAAA,GAAU3d,MAAA,CAAO4hB,IAAA,CAAK,QAAWtC,iBAAA,EAAmBha,UAAA,CAAWiB,SAAA,CAAUob,aAAa,CAAC;IAE5F,IAAID,WAAA,EAAa;MACbA,WAAA,CAAYlW,gBAAA,CAAiB,SAAS,MAAM;QAvCxD,IAAA/F,EAAA;QAwCgB,KAAK,KAAK+X,MAAA,CAAOpY,KAAA,CAAM,IAAI1H,KAAA,EAAM+H,EAAA,GAAAic,WAAA,CAAYrD,MAAA,KAAZ,OAAA5Y,EAAA,GAAsB,eAAe,CAAC;MAC3E,CAAC;IACL;IAEA,IAAI4Z,mBAAA,CAAoBR,8BAAA,IAAkCQ,mBAAA,CAAoBR,8BAAA,GAAiC,GAAG;MAC9G3R,UAAA,CAAW,MAAM;QACb,IAAI,CAAC,KAAKyQ,OAAA,IAAW,OAAO,KAAKA,OAAA,CAAQkE,MAAA,KAAW,aAAa,KAAKlE,OAAA,CAAQkE,MAAA,EAAQ;UAClF,KAAK,KAAKrE,MAAA,CAAOpY,KAAA,CAAM,IAAI1H,KAAA,CAAM,uBAAuB,CAAC;UACzD;QACJ;QAEA,KAAK4gB,KAAA,CAAM;MACf,GAAGe,mBAAA,CAAoBR,8BAAA,GAAiC2C,MAAM;IAClE;EACJ;EAEA,MAAa5D,SAAS3V,MAAA,EAAmD;IAxD7E,IAAAxC,EAAA;IAyDQ,CAAAA,EAAA,QAAKkY,OAAA,KAAL,gBAAAlY,EAAA,CAAcqc,KAAA;IAEd,MAAMC,mBAAA,GAAsBra,WAAA,CAAY,MAAM;MAC1C,IAAI,CAAC,KAAKiW,OAAA,IAAW,KAAKA,OAAA,CAAQkE,MAAA,EAAQ;QACtC,KAAK,KAAKrE,MAAA,CAAOpY,KAAA,CAAM,IAAI1H,KAAA,CAAM,sBAAsB,CAAC;MAC5D;IACJ,GAAG6jB,2BAA2B;IAC9B,KAAK9D,gBAAA,CAAiBU,GAAA,CAAI,MAAMxW,aAAA,CAAcoa,mBAAmB,CAAC;IAElE,OAAO,MAAM,MAAMnE,QAAA,CAAS3V,MAAM;EACtC;EAEOqW,MAAA,EAAc;IACjB,IAAI,KAAKX,OAAA,EAAS;MACd,IAAI,CAAC,KAAKA,OAAA,CAAQkE,MAAA,EAAQ;QACtB,KAAKlE,OAAA,CAAQW,KAAA,CAAM;QACnB,KAAK,KAAKd,MAAA,CAAOpY,KAAA,CAAM,IAAI1H,KAAA,CAAM,cAAc,CAAC;MACpD;IACJ;IACA,KAAKigB,OAAA,GAAU;EACnB;EAEA,OAAcqE,aAAa5e,GAAA,EAAa0a,QAAA,EAAyB;IAC7D,IAAI,CAAC9d,MAAA,CAAOiiB,MAAA,EAAQ;MAChB,MAAM,IAAIvkB,KAAA,CAAM,gDAAgD;IACpE;IACA,OAAO,MAAM8gB,aAAA,CAAcxe,MAAA,CAAOiiB,MAAA,EAAQ7e,GAAA,EAAK0a,QAAQ;EAC3D;AACJ;;;AC1EO,IAAMoE,cAAA,GAAN,MAA2C;EAG9CpkB,YAAoBwR,SAAA,EAAqC;IAArC,KAAAA,SAAA,GAAAA,SAAA;IAFpB,KAAiB1K,OAAA,GAAU,IAAIhH,MAAA,CAAO,gBAAgB;EAEK;EAE3D,MAAayjB,QAAQ;IACjBhC,mBAAA,GAAsB,KAAK/P,SAAA,CAAU+P,mBAAA;IACrCC,iBAAA,GAAoB,KAAKhQ,SAAA,CAAUgQ,iBAAA;IACnCoC;EACJ,GAA4C;IACxC,OAAO,IAAID,WAAA,CAAY;MAAEpC,mBAAA;MAAqBC,iBAAA;MAAmBoC;IAAY,CAAC;EAClF;EAEA,MAAaJ,SAASle,GAAA,EAAa;IAAE0a,QAAA,GAAW;EAAM,GAAkB;IACpE,KAAKlZ,OAAA,CAAQvG,MAAA,CAAO,UAAU;IAE9BojB,WAAA,CAAYO,YAAA,CAAa5e,GAAA,EAAK0a,QAAQ;EAC1C;AACJ;;;ACVO,IAAMqE,iBAAA,GAAN,MAA8C;EAGjDrkB,YAAoBwR,SAAA,EAAqC;IAArC,KAAAA,SAAA,GAAAA,SAAA;IAFpB,KAAiB1K,OAAA,GAAU,IAAIhH,MAAA,CAAO,mBAAmB;EAEC;EAE1D,MAAayjB,QAAQ;IACjB9B,cAAA,GAAiB,KAAKjQ,SAAA,CAAUiQ,cAAA;IAChCC,cAAA,GAAiB,KAAKlQ,SAAA,CAAUkQ;EACpC,GAAqC;IA3BzC,IAAA/Z,EAAA;IA4BQ,KAAKb,OAAA,CAAQvG,MAAA,CAAO,SAAS;IAC7B,IAAI+jB,YAAA,GAAepiB,MAAA,CAAOqiB,IAAA;IAE1B,IAAI7C,cAAA,KAAmB,OAAO;MAC1B4C,YAAA,IAAe3c,EAAA,GAAAzF,MAAA,CAAOoG,GAAA,KAAP,OAAAX,EAAA,GAAczF,MAAA,CAAOqiB,IAAA;IACxC;IAEA,MAAMC,QAAA,GAAWF,YAAA,CAAavE,QAAA,CAAS0B,cAAc,EAAEgD,IAAA,CAAKH,YAAA,CAAavE,QAAQ;IACjF,IAAI1Q,KAAA;IACJ,OAAO;MACHyQ,QAAA,EAAU,MAAO3V,MAAA,IAA2B;QACxC,KAAKrD,OAAA,CAAQvG,MAAA,CAAO,UAAU;QAE9B,MAAMmkB,OAAA,GAAU,IAAIrX,OAAA,CAAQ,CAACC,OAAA,EAAS2S,MAAA,KAAW;UAC7C5Q,KAAA,GAAQ4Q,MAAA;QACZ,CAAC;QACDuE,QAAA,CAASra,MAAA,CAAO7E,GAAG;QACnB,OAAO,MAAOof,OAAA;MAClB;MACAlE,KAAA,EAAOA,CAAA,KAAM;QACT,KAAK1Z,OAAA,CAAQvG,MAAA,CAAO,OAAO;QAC3B8O,KAAA,oBAAAA,KAAA,CAAQ,IAAIzP,KAAA,CAAM,kBAAkB;QACpC0kB,YAAA,CAAazX,IAAA,CAAK;MACtB;IACJ;EACJ;EAEA,MAAa2W,SAAA,EAA0B;IACnC;EACJ;AACJ;;;ACtBO,IAAMmB,iBAAA,GAAN,cAAgC3Z,iBAAA,CAAkB;EAU9ChL,YAAY2b,QAAA,EAAoC;IACnD,MAAM;MAAEvQ,iCAAA,EAAmCuQ,QAAA,CAAS4G;IAA6C,CAAC;IAVtG,KAAmBzb,OAAA,GAAU,IAAIhH,MAAA,CAAO,mBAAmB;IAE3D,KAAiB8kB,WAAA,GAAc,IAAIhe,KAAA,CAAc,aAAa;IAC9D,KAAiBie,aAAA,GAAgB,IAAIje,KAAA,CAAU,eAAe;IAC9D,KAAiBke,iBAAA,GAAoB,IAAIle,KAAA,CAAe,oBAAoB;IAC5E,KAAiBme,aAAA,GAAgB,IAAIne,KAAA,CAAU,gBAAgB;IAC/D,KAAiBoe,cAAA,GAAiB,IAAIpe,KAAA,CAAU,iBAAiB;IACjE,KAAiBqe,mBAAA,GAAsB,IAAIre,KAAA,CAAU,sBAAsB;EAI3E;EAEA,MAAayE,KAAKwS,IAAA,EAAYc,UAAA,GAAW,MAAqB;IAC1D,MAAM,MAAMtT,IAAA,CAAKwS,IAAI;IACrB,IAAIc,UAAA,EAAY;MACZ,MAAM,KAAKiG,WAAA,CAAYtd,KAAA,CAAMuW,IAAI;IACrC;EACJ;EAEA,MAAajS,OAAA,EAAwB;IACjC,MAAM,MAAMA,MAAA,CAAO;IACnB,MAAM,KAAKiZ,aAAA,CAAcvd,KAAA,CAAM;EACnC;EAAA;AAAA;AAAA;EAKO0X,cAAchY,EAAA,EAAoC;IACrD,OAAO,KAAK4d,WAAA,CAAY7d,UAAA,CAAWC,EAAE;EACzC;EAAA;AAAA;AAAA;EAIOke,iBAAiBle,EAAA,EAA8B;IAClD,OAAO,KAAK4d,WAAA,CAAY1d,aAAA,CAAcF,EAAE;EAC5C;EAAA;AAAA;AAAA;EAKOiY,gBAAgBjY,EAAA,EAAsC;IACzD,OAAO,KAAK6d,aAAA,CAAc9d,UAAA,CAAWC,EAAE;EAC3C;EAAA;AAAA;AAAA;EAIOme,mBAAmBne,EAAA,EAAgC;IACtD,OAAO,KAAK6d,aAAA,CAAc3d,aAAA,CAAcF,EAAE;EAC9C;EAAA;AAAA;AAAA;EAKOoe,oBAAoBpe,EAAA,EAA0C;IACjE,OAAO,KAAK8d,iBAAA,CAAkB/d,UAAA,CAAWC,EAAE;EAC/C;EAAA;AAAA;AAAA;EAIOqe,uBAAuBre,EAAA,EAAoC;IAC9D,OAAO,KAAK8d,iBAAA,CAAkB5d,aAAA,CAAcF,EAAE;EAClD;EAAA;AAAA;AAAA;EAIA,MAAase,uBAAuBxgB,CAAA,EAAyB;IACzD,MAAM,KAAKggB,iBAAA,CAAkBxd,KAAA,CAAMxC,CAAC;EACxC;EAAA;AAAA;AAAA;AAAA;EAMOygB,gBAAgBve,EAAA,EAAsC;IACzD,OAAO,KAAK+d,aAAA,CAAche,UAAA,CAAWC,EAAE;EAC3C;EAAA;AAAA;AAAA;EAIOwe,mBAAmBxe,EAAA,EAAgC;IACtD,KAAK+d,aAAA,CAAc7d,aAAA,CAAcF,EAAE;EACvC;EAAA;AAAA;AAAA;EAIA,MAAa+X,mBAAA,EAAoC;IAC7C,MAAM,KAAKgG,aAAA,CAAczd,KAAA,CAAM;EACnC;EAAA;AAAA;AAAA;AAAA;EAMOme,iBAAiBze,EAAA,EAAuC;IAC3D,OAAO,KAAKge,cAAA,CAAeje,UAAA,CAAWC,EAAE;EAC5C;EAAA;AAAA;AAAA;EAIO0e,oBAAoB1e,EAAA,EAAiC;IACxD,KAAKge,cAAA,CAAe9d,aAAA,CAAcF,EAAE;EACxC;EAAA;AAAA;AAAA;EAIA,MAAa8X,oBAAA,EAAqC;IAC9C,MAAM,KAAKkG,cAAA,CAAe1d,KAAA,CAAM;EACpC;EAAA;AAAA;AAAA;AAAA;EAMOqe,sBAAsB3e,EAAA,EAA4C;IACrE,OAAO,KAAKie,mBAAA,CAAoBle,UAAA,CAAWC,EAAE;EACjD;EAAA;AAAA;AAAA;EAIO4e,yBAAyB5e,EAAA,EAAsC;IAClE,KAAKie,mBAAA,CAAoB/d,aAAA,CAAcF,EAAE;EAC7C;EAAA;AAAA;AAAA;EAIA,MAAa6X,yBAAA,EAA0C;IACnD,MAAM,KAAKoG,mBAAA,CAAoB3d,KAAA,CAAM;EACzC;AACJ;;;AC3JO,IAAMue,kBAAA,GAAN,MAAyB;EAKrB7lB,YAAoB2d,YAAA,EAA2B;IAA3B,KAAAA,YAAA,GAAAA,YAAA;IAJ3B,KAAU7W,OAAA,GAAU,IAAIhH,MAAA,CAAO,oBAAoB;IACnD,KAAQgmB,UAAA,GAAa;IACrB,KAAiBC,WAAA,GAAc,IAAIld,KAAA,CAAM,oBAAoB;IAgC7D,KAAUmd,cAAA,GAAsC,YAAY;MACxD,MAAMxc,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,gBAAgB;MACnD,IAAI;QACA,MAAM,KAAKod,YAAA,CAAasI,YAAA,CAAa;QACrCzc,OAAA,CAAOvK,KAAA,CAAM,iCAAiC;MAClD,SACOqB,GAAA,EAAK;QACR,IAAIA,GAAA,YAAeyK,YAAA,EAAc;UAE7BvB,OAAA,CAAOrK,IAAA,CAAK,mCAAmCmB,GAAA,EAAK,aAAa;UACjE,KAAKylB,WAAA,CAAYzc,IAAA,CAAK,CAAC;UACvB;QACJ;QAEAE,OAAA,CAAOpK,KAAA,CAAM,4BAA4BkB,GAAG;QAC5C,MAAM,KAAKqd,YAAA,CAAaiB,MAAA,CAAO0G,sBAAA,CAAuBhlB,GAAY;MACtE;IACJ;EA/CuD;EAEvD,MAAaqN,MAAA,EAAuB;IAChC,MAAMnE,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,OAAO;IAC1C,IAAI,CAAC,KAAKulB,UAAA,EAAY;MAClB,KAAKA,UAAA,GAAa;MAClB,KAAKnI,YAAA,CAAaiB,MAAA,CAAO/S,sBAAA,CAAuB,KAAKma,cAAc;MACnE,KAAKD,WAAA,CAAYhf,UAAA,CAAW,KAAKif,cAAc;MAG/C,IAAI;QACA,MAAM,KAAKrI,YAAA,CAAayB,OAAA,CAAQ;MAEpC,SACO9e,GAAA,EAAK;QAERkJ,OAAA,CAAOpK,KAAA,CAAM,iBAAiBkB,GAAG;MACrC;IACJ;EACJ;EAEOuM,KAAA,EAAa;IAChB,IAAI,KAAKiZ,UAAA,EAAY;MACjB,KAAKC,WAAA,CAAY1c,MAAA,CAAO;MACxB,KAAK0c,WAAA,CAAY7e,aAAA,CAAc,KAAK8e,cAAc;MAClD,KAAKrI,YAAA,CAAaiB,MAAA,CAAO9S,yBAAA,CAA0B,KAAKka,cAAc;MACtE,KAAKF,UAAA,GAAa;IACtB;EACJ;AAoBJ;;;ACtDO,IAAMI,YAAA,GAAN,MAAmB;EAUtBlmB,YAAYE,IAAA,EAQT;IACC,KAAK2W,aAAA,GAAgB3W,IAAA,CAAK2W,aAAA;IAC1B,KAAKgB,QAAA,GAAW3X,IAAA,CAAK2X,QAAA;IACrB,KAAKhN,aAAA,GAAgB3K,IAAA,CAAK2K,aAAA;IAC1B,KAAK2J,KAAA,GAAQtU,IAAA,CAAKsU,KAAA;IAClB,KAAKuD,OAAA,GAAU7X,IAAA,CAAK6X,OAAA;IAEpB,KAAK/T,IAAA,GAAO9D,IAAA,CAAKyK,KAAA;EAErB;AACJ;;;AC0CO,IAAMwb,WAAA,GAAN,MAAkB;EAadnmB,YAAY2b,QAAA,EAA+ByK,iBAAA,EAAgCC,cAAA,EAA6BC,eAAA,EAA8B;IAV7I,KAAmBxf,OAAA,GAAU,IAAIhH,MAAA,CAAO,aAAa;IAWjD,KAAK6b,QAAA,GAAW,IAAIyF,wBAAA,CAAyBzF,QAAQ;IAErD,KAAK4K,OAAA,GAAU,IAAI7K,UAAA,CAAWC,QAAQ;IAEtC,KAAK6K,kBAAA,GAAqBJ,iBAAA,WAAAA,iBAAA,GAAqB,IAAI/B,iBAAA,CAAkB,KAAK1I,QAAQ;IAClF,KAAK8K,eAAA,GAAkBJ,cAAA,WAAAA,cAAA,GAAkB,IAAIjC,cAAA,CAAe,KAAKzI,QAAQ;IACzE,KAAK+K,gBAAA,GAAmBJ,eAAA,WAAAA,eAAA,GAAmB,IAAIhD,eAAA,CAAgB,KAAK3H,QAAQ;IAE5E,KAAKgL,OAAA,GAAU,IAAIhC,iBAAA,CAAkB,KAAKhJ,QAAQ;IAClD,KAAKiL,mBAAA,GAAsB,IAAIf,kBAAA,CAAmB,IAAI;IAGtD,IAAI,KAAKlK,QAAA,CAASoG,oBAAA,EAAsB;MACpC,KAAK8E,gBAAA,CAAiB;IAC1B;IAEA,KAAKC,eAAA,GAAkB;IACvB,IAAI,KAAKnL,QAAA,CAASuG,cAAA,EAAgB;MAC9B,KAAK4E,eAAA,GAAkB,IAAIpJ,cAAA,CAAe,IAAI;IAClD;EACJ;EAAA;AAAA;AAAA;EAKA,IAAWkB,OAAA,EAA4B;IACnC,OAAO,KAAK+H,OAAA;EAChB;EAAA;AAAA;AAAA;EAKA,IAAW/K,gBAAA,EAAmC;IAC1C,OAAO,KAAK2K,OAAA,CAAQ3K,eAAA;EACxB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQA,MAAawD,QAAQT,UAAA,GAAa,OAA6B;IAC3D,MAAMnV,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,SAAS;IAC5C,MAAMsd,IAAA,GAAO,MAAM,KAAKkJ,SAAA,CAAU;IAClC,IAAIlJ,IAAA,EAAM;MACNrU,OAAA,CAAOtK,IAAA,CAAK,aAAa;MACzB,MAAM,KAAKynB,OAAA,CAAQtb,IAAA,CAAKwS,IAAA,EAAMc,UAAU;MACxC,OAAOd,IAAA;IACX;IAEArU,OAAA,CAAOtK,IAAA,CAAK,2BAA2B;IACvC,OAAO;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA,MAAa8nB,WAAA,EAA4B;IACrC,MAAMxd,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,YAAY;IAC/C,MAAM,KAAK0mB,SAAA,CAAU,IAAI;IACzBzd,OAAA,CAAOtK,IAAA,CAAK,2BAA2B;IACvC,MAAM,KAAKynB,OAAA,CAAQ/a,MAAA,CAAO;EAC9B;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASA,MAAasb,eAAehnB,IAAA,GAA2B,CAAC,GAAkB;IAxK9E,IAAAyH,EAAA;IAyKQ,KAAKb,OAAA,CAAQvG,MAAA,CAAO,gBAAgB;IACpC,MAAM;MACFkhB,cAAA;MACA,GAAG0F;IACP,IAAIjnB,IAAA;IAEJ,IAAI2Z,OAAA;IACJ,KAAIlS,EAAA,QAAKgU,QAAA,CAAS7F,IAAA,KAAd,gBAAAnO,EAAA,CAAoByf,uBAAA,EAAyB;MAC7CvN,OAAA,GAAU,MAAM,KAAKtT,eAAA,CAAgB,KAAKoV,QAAA,CAAS7F,IAAI;IAC3D;IAEA,MAAMuR,MAAA,GAAS,MAAM,KAAKb,kBAAA,CAAmBjD,OAAA,CAAQ;MAAE9B;IAAe,CAAC;IACvE,MAAM,KAAK6F,YAAA,CAAa;MACpBvO,YAAA,EAAc;MACdc,OAAA;MACA,GAAGsN;IACP,GAAGE,MAAM;EACb;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUA,MAAaE,uBAAuBjiB,GAAA,GAAMpD,MAAA,CAAO6d,QAAA,CAAS3S,IAAA,EAAqB;IAC3E,MAAM5D,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,wBAAwB;IAC3D,MAAMsd,IAAA,GAAO,MAAM,KAAK2J,UAAA,CAAWliB,GAAG;IACtC,IAAIuY,IAAA,CAAK9F,OAAA,IAAW8F,IAAA,CAAK9F,OAAA,CAAQK,GAAA,EAAK;MAClC5O,OAAA,CAAOtK,IAAA,CAAK,8BAA8B2e,IAAA,CAAK9F,OAAA,CAAQK,GAAG;IAC9D,OACK;MACD5O,OAAA,CAAOtK,IAAA,CAAK,YAAY;IAC5B;IAEA,OAAO2e,IAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQA,MAAa4J,+BAA+B;IACxC5K,QAAA;IACAC,QAAA;IACAnF,YAAA,GAAe;EACnB,GAAsD;IAClD,MAAMnO,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,+BAA+B;IAElE,MAAMwc,cAAA,GAAiB,MAAM,KAAKwJ,OAAA,CAAQ3J,uCAAA,CAAwC;MAC9EC,QAAA;MACAC,QAAA;MACAnF,YAAA;MACA9B,gBAAA,EAAkB,KAAK8F,QAAA,CAAS9F;IACpC,CAAC;IACDrM,OAAA,CAAOvK,KAAA,CAAM,qBAAqB;IAElC,MAAM4e,IAAA,GAAO,MAAM,KAAK6J,UAAA,CAAW3K,cAAc;IACjD,IAAIc,IAAA,CAAK9F,OAAA,IAAW8F,IAAA,CAAK9F,OAAA,CAAQK,GAAA,EAAK;MAClC5O,OAAA,CAAOtK,IAAA,CAAK,8BAA8B2e,IAAA,CAAK9F,OAAA,CAAQK,GAAG;IAC9D,OAAO;MACH5O,OAAA,CAAOtK,IAAA,CAAK,YAAY;IAC5B;IACA,OAAO2e,IAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQA,MAAa8J,YAAYznB,IAAA,GAAwB,CAAC,GAAkB;IArPxE,IAAAyH,EAAA;IAsPQ,MAAM6B,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,aAAa;IAEhD,IAAIsZ,OAAA;IACJ,KAAIlS,EAAA,QAAKgU,QAAA,CAAS7F,IAAA,KAAd,gBAAAnO,EAAA,CAAoByf,uBAAA,EAAyB;MAC7CvN,OAAA,GAAU,MAAM,KAAKtT,eAAA,CAAgB,KAAKoV,QAAA,CAAS7F,IAAI;IAC3D;IAEA,MAAM;MACFyL,mBAAA;MACAC,iBAAA;MACAoC,WAAA;MACA,GAAGuD;IACP,IAAIjnB,IAAA;IACJ,MAAMoF,GAAA,GAAM,KAAKqW,QAAA,CAAS0F,kBAAA;IAC1B,IAAI,CAAC/b,GAAA,EAAK;MACNkE,OAAA,CAAOnJ,KAAA,CAAM,IAAIT,KAAA,CAAM,kCAAkC,CAAC;IAC9D;IAEA,MAAMynB,MAAA,GAAS,MAAM,KAAKZ,eAAA,CAAgBlD,OAAA,CAAQ;MAAEhC,mBAAA;MAAqBC,iBAAA;MAAmBoC;IAAY,CAAC;IACzG,MAAM/F,IAAA,GAAO,MAAM,KAAK+J,OAAA,CAAQ;MAC5B7O,YAAA,EAAc;MACdtE,YAAA,EAAcnP,GAAA;MACduP,OAAA,EAAS;MACTgF,OAAA;MACA,GAAGsN;IACP,GAAGE,MAAM;IACT,IAAIxJ,IAAA,EAAM;MACN,IAAIA,IAAA,CAAK9F,OAAA,IAAW8F,IAAA,CAAK9F,OAAA,CAAQK,GAAA,EAAK;QAClC5O,OAAA,CAAOtK,IAAA,CAAK,8BAA8B2e,IAAA,CAAK9F,OAAA,CAAQK,GAAG;MAC9D,OAAO;QACH5O,OAAA,CAAOtK,IAAA,CAAK,YAAY;MAC5B;IACJ;IAEA,OAAO2e,IAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUA,MAAagK,oBAAoBviB,GAAA,GAAMpD,MAAA,CAAO6d,QAAA,CAAS3S,IAAA,EAAM4S,QAAA,GAAW,OAAsB;IAC1F,MAAMxW,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,qBAAqB;IACxD,MAAM,KAAKkmB,eAAA,CAAgBjD,QAAA,CAASle,GAAA,EAAK;MAAE0a;IAAS,CAAC;IACrDxW,OAAA,CAAOtK,IAAA,CAAK,SAAS;EACzB;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA,MAAa+mB,aAAa/lB,IAAA,GAAyB,CAAC,GAAyB;IA9SjF,IAAAyH,EAAA,EAAAC,EAAA;IA+SQ,MAAM4B,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,cAAc;IACjD,MAAM;MACFuhB,6BAAA;MACA,GAAGqF;IACP,IAAIjnB,IAAA;IAEJ,IAAI2d,IAAA,GAAO,MAAM,KAAKkJ,SAAA,CAAU;IAChC,IAAIlJ,IAAA,oBAAAA,IAAA,CAAMhH,aAAA,EAAe;MACrBrN,OAAA,CAAOvK,KAAA,CAAM,qBAAqB;MAClC,MAAM0L,KAAA,GAAQ,IAAIub,YAAA,CAAarI,IAAsB;MACrD,OAAO,MAAM,KAAKiK,gBAAA,CAAiB;QAC/Bnd,KAAA;QACA8J,YAAA,EAAc0S,WAAA,CAAY1S,YAAA;QAC1BQ,QAAA,EAAUkS,WAAA,CAAYlS,QAAA;QACtBY,gBAAA,EAAkBsR,WAAA,CAAYtR,gBAAA;QAC9B/G,gBAAA,EAAkBgT;MACtB,CAAC;IACL;IAEA,IAAIjI,OAAA;IACJ,KAAIlS,EAAA,QAAKgU,QAAA,CAAS7F,IAAA,KAAd,gBAAAnO,EAAA,CAAoByf,uBAAA,EAAyB;MAC7CvN,OAAA,GAAU,MAAM,KAAKtT,eAAA,CAAgB,KAAKoV,QAAA,CAAS7F,IAAI;IAC3D;IAEA,MAAMxQ,GAAA,GAAM,KAAKqW,QAAA,CAASkG,mBAAA;IAC1B,IAAI,CAACvc,GAAA,EAAK;MACNkE,OAAA,CAAOnJ,KAAA,CAAM,IAAIT,KAAA,CAAM,mCAAmC,CAAC;IAC/D;IAEA,IAAImoB,SAAA;IACJ,IAAIlK,IAAA,IAAQ,KAAKlC,QAAA,CAASqG,wBAAA,EAA0B;MAChDxY,OAAA,CAAOvK,KAAA,CAAM,kCAAkC4e,IAAA,CAAK9F,OAAA,CAAQK,GAAG;MAC/D2P,SAAA,GAAYlK,IAAA,CAAK9F,OAAA,CAAQK,GAAA;IAC7B;IAEA,MAAMiP,MAAA,GAAS,MAAM,KAAKX,gBAAA,CAAiBnD,OAAA,CAAQ;MAAEzB;IAA8B,CAAC;IACpFjE,IAAA,GAAO,MAAM,KAAK+J,OAAA,CAAQ;MACtB7O,YAAA,EAAc;MACdtE,YAAA,EAAcnP,GAAA;MACdsP,MAAA,EAAQ;MACRiG,aAAA,EAAe,KAAKc,QAAA,CAASsG,2BAAA,GAA8BpE,IAAA,oBAAAA,IAAA,CAAMhG,QAAA,GAAW;MAC5EgC,OAAA;MACA,GAAGsN;IACP,GAAGE,MAAA,EAAQU,SAAS;IACpB,IAAIlK,IAAA,EAAM;MACN,KAAIjW,EAAA,GAAAiW,IAAA,CAAK9F,OAAA,KAAL,gBAAAnQ,EAAA,CAAcwQ,GAAA,EAAK;QACnB5O,OAAA,CAAOtK,IAAA,CAAK,8BAA8B2e,IAAA,CAAK9F,OAAA,CAAQK,GAAG;MAC9D,OACK;QACD5O,OAAA,CAAOtK,IAAA,CAAK,YAAY;MAC5B;IACJ;IAEA,OAAO2e,IAAA;EACX;EAEA,MAAgBiK,iBAAiB5nB,IAAA,EAA0C;IACvE,MAAMoP,QAAA,GAAW,MAAM,KAAKiX,OAAA,CAAQvJ,eAAA,CAAgB;MAChDlO,gBAAA,EAAkB,KAAK6M,QAAA,CAASmG,6BAAA;MAChC,GAAG5hB;IACP,CAAC;IACD,MAAM2d,IAAA,GAAO,IAAIwB,IAAA,CAAK;MAAE,GAAGnf,IAAA,CAAKyK,KAAA;MAAO,GAAG2E;IAAS,CAAC;IAEpD,MAAM,KAAK2X,SAAA,CAAUpJ,IAAI;IACzB,MAAM,KAAK8I,OAAA,CAAQtb,IAAA,CAAKwS,IAAI;IAC5B,OAAOA,IAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWA,MAAamK,qBAAqB1iB,GAAA,GAAMpD,MAAA,CAAO6d,QAAA,CAAS3S,IAAA,EAAqB;IACzE,MAAM5D,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,sBAAsB;IACzD,MAAM,KAAKmmB,gBAAA,CAAiBlD,QAAA,CAASle,GAAG;IACxCkE,OAAA,CAAOtK,IAAA,CAAK,SAAS;EACzB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWA,MAAa+oB,eAAe3iB,GAAA,GAAMpD,MAAA,CAAO6d,QAAA,CAAS3S,IAAA,EAAiC;IAC/E,MAAM;MAAEzC;IAAM,IAAI,MAAM,KAAK4b,OAAA,CAAQnK,uBAAA,CAAwB9W,GAAG;IAChE,QAAQqF,KAAA,CAAMoO,YAAA;MACV,KAAK;QACD,OAAO,MAAM,KAAKwO,sBAAA,CAAuBjiB,GAAG;MAChD,KAAK;QACD,MAAM,KAAKuiB,mBAAA,CAAoBviB,GAAG;QAClC;MACJ,KAAK;QACD,MAAM,KAAK0iB,oBAAA,CAAqB1iB,GAAG;QACnC;MACJ;QACI,MAAM,IAAI1F,KAAA,CAAM,gCAAgC;IACxD;IACA,OAAO;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWA,MAAasoB,gBAAgB5iB,GAAA,GAAMpD,MAAA,CAAO6d,QAAA,CAAS3S,IAAA,EAAM4S,QAAA,GAAW,OAA6C;IAC7G,MAAM;MAAErV;IAAM,IAAI,MAAM,KAAK4b,OAAA,CAAQjJ,wBAAA,CAAyBhY,GAAG;IACjE,IAAI,CAACqF,KAAA,EAAO;MACR,OAAO;IACX;IAEA,QAAQA,KAAA,CAAMoO,YAAA;MACV,KAAK;QACD,OAAO,MAAM,KAAKoP,uBAAA,CAAwB7iB,GAAG;MACjD,KAAK;QACD,MAAM,KAAK8iB,oBAAA,CAAqB9iB,GAAA,EAAK0a,QAAQ;QAC7C;MACJ,KAAK;QACD,MAAM,KAAKqI,qBAAA,CAAsB/iB,GAAG;QACpC;MACJ;QACI,MAAM,IAAI1F,KAAA,CAAM,gCAAgC;IACxD;IACA,OAAO;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA,MAAa6e,mBAAmBve,IAAA,GAA+B,CAAC,GAAkC;IAC9F,MAAMsJ,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,oBAAoB;IACvD,MAAM;MACFuhB,6BAAA;MACA,GAAGqF;IACP,IAAIjnB,IAAA;IACJ,MAAMoF,GAAA,GAAM,KAAKqW,QAAA,CAASkG,mBAAA;IAC1B,IAAI,CAACvc,GAAA,EAAK;MACNkE,OAAA,CAAOnJ,KAAA,CAAM,IAAIT,KAAA,CAAM,mCAAmC,CAAC;IAC/D;IAEA,MAAMie,IAAA,GAAO,MAAM,KAAKkJ,SAAA,CAAU;IAClC,MAAMM,MAAA,GAAS,MAAM,KAAKX,gBAAA,CAAiBnD,OAAA,CAAQ;MAAEzB;IAA8B,CAAC;IACpF,MAAMwG,WAAA,GAAc,MAAM,KAAKhB,YAAA,CAAa;MACxCvO,YAAA,EAAc;MAAA;MACdtE,YAAA,EAAcnP,GAAA;MACdsP,MAAA,EAAQ;MACRiG,aAAA,EAAe,KAAKc,QAAA,CAASsG,2BAAA,GAA8BpE,IAAA,oBAAAA,IAAA,CAAMhG,QAAA,GAAW;MAC5EtD,aAAA,EAAe,KAAKoH,QAAA,CAASwG,0BAAA;MAC7B3N,KAAA,EAAO;MACPmD,YAAA,EAAc;MACd,GAAGwP;IACP,GAAGE,MAAM;IACT,IAAI;MACA,MAAM3W,YAAA,GAA4C,CAAC;MACnD,MAAMqM,cAAA,GAAiB,MAAM,KAAKwJ,OAAA,CAAQhK,qBAAA,CAAsB+L,WAAA,CAAYhjB,GAAA,EAAKoL,YAAY;MAC7FlH,OAAA,CAAOvK,KAAA,CAAM,qBAAqB;MAElC,IAAI8d,cAAA,CAAelS,aAAA,IAAiBkS,cAAA,CAAehF,OAAA,CAAQK,GAAA,EAAK;QAC5D5O,OAAA,CAAOtK,IAAA,CAAK,uBAAuB6d,cAAA,CAAehF,OAAA,CAAQK,GAAG;QAC7D,OAAO;UACHvN,aAAA,EAAekS,cAAA,CAAelS,aAAA;UAC9BuN,GAAA,EAAK2E,cAAA,CAAehF,OAAA,CAAQK;QAChC;MACJ;MAEA5O,OAAA,CAAOtK,IAAA,CAAK,iCAAiC;MAC7C,OAAO;IACX,SAASoB,GAAA,EAAK;MACV,IAAI,KAAKqb,QAAA,CAAS2C,uBAAA,IAA2Bhe,GAAA,YAAeiK,aAAA,EAAe;QACvE,QAAQjK,GAAA,CAAIlB,KAAA;UACR,KAAK;UACL,KAAK;UACL,KAAK;UACL,KAAK;YACDoK,OAAA,CAAOtK,IAAA,CAAK,4BAA4B;YACxC,OAAO;cACH2L,aAAA,EAAevK,GAAA,CAAIuK;YACvB;QACR;MACJ;MACA,MAAMvK,GAAA;IACV;EACJ;EAEA,MAAgBsnB,QAAQ1nB,IAAA,EAA+BmnB,MAAA,EAAiBU,SAAA,EAAmC;IACvG,MAAMO,WAAA,GAAc,MAAM,KAAKhB,YAAA,CAAapnB,IAAA,EAAMmnB,MAAM;IACxD,OAAO,MAAM,KAAKG,UAAA,CAAWc,WAAA,CAAYhjB,GAAA,EAAKyiB,SAAS;EAC3D;EAEA,MAAgBT,aAAapnB,IAAA,EAA+BmnB,MAAA,EAA4C;IACpG,MAAM7d,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,cAAc;IAEjD,IAAI;MACA,MAAM2b,aAAA,GAAgB,MAAM,KAAKqK,OAAA,CAAQzK,mBAAA,CAAoB5b,IAAI;MACjEsJ,OAAA,CAAOvK,KAAA,CAAM,oBAAoB;MAEjC,OAAO,MAAMooB,MAAA,CAAOvH,QAAA,CAAS;QACzBxa,GAAA,EAAK4W,aAAA,CAAc5W,GAAA;QACnBqF,KAAA,EAAOuR,aAAA,CAAcvR,KAAA,CAAMuN,EAAA;QAC3BhD,aAAA,EAAegH,aAAA,CAAcvR,KAAA,CAAMuK,aAAA;QACnCiL,YAAA,EAAc,KAAKxE,QAAA,CAASiG;MAChC,CAAC;IACL,SAASthB,GAAA,EAAK;MACVkJ,OAAA,CAAOvK,KAAA,CAAM,2DAA2D;MACxEooB,MAAA,CAAO7G,KAAA,CAAM;MACb,MAAMlgB,GAAA;IACV;EACJ;EAEA,MAAgBknB,WAAWliB,GAAA,EAAayiB,SAAA,EAAmC;IACvE,MAAMve,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,YAAY;IAC/C,MAAMmQ,YAAA,GAA4C,CAAC;IACnD,MAAMqM,cAAA,GAAiB,MAAM,KAAKwJ,OAAA,CAAQhK,qBAAA,CAAsBjX,GAAA,EAAKoL,YAAY;IACjFlH,OAAA,CAAOvK,KAAA,CAAM,qBAAqB;IAElC,MAAM4e,IAAA,GAAO,MAAM,KAAK6J,UAAA,CAAW3K,cAAA,EAAgBgL,SAAS;IAC5D,OAAOlK,IAAA;EACX;EAEA,MAAgB6J,WAAW3K,cAAA,EAAgCgL,SAAA,EAAoB;IAC3E,MAAMve,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,YAAY;IAC/C,MAAMsd,IAAA,GAAO,IAAIwB,IAAA,CAAKtC,cAAc;IACpC,IAAIgL,SAAA,EAAW;MACX,IAAIA,SAAA,KAAclK,IAAA,CAAK9F,OAAA,CAAQK,GAAA,EAAK;QAChC5O,OAAA,CAAOvK,KAAA,CAAM,2EAA2E4e,IAAA,CAAK9F,OAAA,CAAQK,GAAG;QACxG,MAAM,IAAI7N,aAAA,CAAc;UAAE,GAAGwS,cAAA;UAAgB3d,KAAA,EAAO;QAAiB,CAAC;MAC1E;MACAoK,OAAA,CAAOvK,KAAA,CAAM,gDAAgD;IACjE;IAEA,MAAM,KAAKgoB,SAAA,CAAUpJ,IAAI;IACzBrU,OAAA,CAAOvK,KAAA,CAAM,aAAa;IAC1B,MAAM,KAAK0nB,OAAA,CAAQtb,IAAA,CAAKwS,IAAI;IAE5B,OAAOA,IAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA,MAAa0K,gBAAgBroB,IAAA,GAA4B,CAAC,GAAkB;IACxE,MAAMsJ,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,iBAAiB;IACpD,MAAM;MACFkhB,cAAA;MACA,GAAG0F;IACP,IAAIjnB,IAAA;IACJ,MAAMmnB,MAAA,GAAS,MAAM,KAAKb,kBAAA,CAAmBjD,OAAA,CAAQ;MAAE9B;IAAe,CAAC;IACvE,MAAM,KAAK+G,aAAA,CAAc;MACrBzP,YAAA,EAAc;MACdrE,wBAAA,EAA0B,KAAKiH,QAAA,CAASjH,wBAAA;MACxC,GAAGyS;IACP,GAAGE,MAAM;IACT7d,OAAA,CAAOtK,IAAA,CAAK,SAAS;EACzB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUA,MAAaipB,wBAAwB7iB,GAAA,GAAMpD,MAAA,CAAO6d,QAAA,CAAS3S,IAAA,EAAgC;IACvF,MAAM5D,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,yBAAyB;IAC5D,MAAM+O,QAAA,GAAW,MAAM,KAAKmZ,WAAA,CAAYnjB,GAAG;IAC3CkE,OAAA,CAAOtK,IAAA,CAAK,SAAS;IACrB,OAAOoQ,QAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA,MAAaoZ,aAAaxoB,IAAA,GAAyB,CAAC,GAAkB;IAClE,MAAMsJ,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,cAAc;IACjD,MAAM;MACFghB,mBAAA;MACAC,iBAAA;MACAoC,WAAA;MACA,GAAGuD;IACP,IAAIjnB,IAAA;IACJ,MAAMoF,GAAA,GAAM,KAAKqW,QAAA,CAAS2F,8BAAA;IAE1B,MAAM+F,MAAA,GAAS,MAAM,KAAKZ,eAAA,CAAgBlD,OAAA,CAAQ;MAAEhC,mBAAA;MAAqBC,iBAAA;MAAmBoC;IAAY,CAAC;IACzG,MAAM,KAAK+E,QAAA,CAAS;MAChB5P,YAAA,EAAc;MACdrE,wBAAA,EAA0BpP,GAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAM1BqF,KAAA,EAAOrF,GAAA,IAAO,OAAO,SAAY,CAAC;MAClC,GAAG6hB;IACP,GAAGE,MAAM;IACT7d,OAAA,CAAOtK,IAAA,CAAK,SAAS;EACzB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUA,MAAakpB,qBAAqB9iB,GAAA,GAAMpD,MAAA,CAAO6d,QAAA,CAAS3S,IAAA,EAAM4S,QAAA,GAAW,OAAsB;IAC3F,MAAMxW,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,sBAAsB;IACzD,MAAM,KAAKkmB,eAAA,CAAgBjD,QAAA,CAASle,GAAA,EAAK;MAAE0a;IAAS,CAAC;IACrDxW,OAAA,CAAOtK,IAAA,CAAK,SAAS;EACzB;EAEA,MAAgBypB,SAASzoB,IAAA,EAAgCmnB,MAAA,EAA2C;IAChG,MAAMiB,WAAA,GAAc,MAAM,KAAKE,aAAA,CAActoB,IAAA,EAAMmnB,MAAM;IACzD,OAAO,MAAM,KAAKoB,WAAA,CAAYH,WAAA,CAAYhjB,GAAG;EACjD;EAEA,MAAgBkjB,cAActoB,IAAA,GAAiC,CAAC,GAAGmnB,MAAA,EAA4C;IA/nBnH,IAAA1f,EAAA;IAgoBQ,MAAM6B,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,eAAe;IAElD,IAAI;MACA,MAAMsd,IAAA,GAAO,MAAM,KAAKkJ,SAAA,CAAU;MAClCvd,OAAA,CAAOvK,KAAA,CAAM,kCAAkC;MAE/C,IAAI,KAAK0c,QAAA,CAAS0G,qBAAA,EAAuB;QACrC,MAAM,KAAKuG,eAAA,CAAgB/K,IAAI;MACnC;MAEA,MAAMhG,QAAA,GAAW3X,IAAA,CAAK2a,aAAA,IAAiBgD,IAAA,IAAQA,IAAA,CAAKhG,QAAA;MACpD,IAAIA,QAAA,EAAU;QACVrO,OAAA,CAAOvK,KAAA,CAAM,0CAA0C;QACvDiB,IAAA,CAAK2a,aAAA,GAAgBhD,QAAA;MACzB;MAEA,MAAM,KAAKmP,UAAA,CAAW;MACtBxd,OAAA,CAAOvK,KAAA,CAAM,wCAAwC;MAErD,MAAM4pB,cAAA,GAAiB,MAAM,KAAKtC,OAAA,CAAQnJ,oBAAA,CAAqBld,IAAI;MACnEsJ,OAAA,CAAOvK,KAAA,CAAM,qBAAqB;MAElC,OAAO,MAAMooB,MAAA,CAAOvH,QAAA,CAAS;QACzBxa,GAAA,EAAKujB,cAAA,CAAevjB,GAAA;QACpBqF,KAAA,GAAOhD,EAAA,GAAAkhB,cAAA,CAAele,KAAA,KAAf,gBAAAhD,EAAA,CAAsBuQ,EAAA;QAC7BiI,YAAA,EAAc,KAAKxE,QAAA,CAASiG;MAChC,CAAC;IACL,SAASthB,GAAA,EAAK;MACVkJ,OAAA,CAAOvK,KAAA,CAAM,2DAA2D;MACxEooB,MAAA,CAAO7G,KAAA,CAAM;MACb,MAAMlgB,GAAA;IACV;EACJ;EAEA,MAAgBmoB,YAAYnjB,GAAA,EAAuC;IAC/D,MAAMkE,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,aAAa;IAChD,MAAMuoB,eAAA,GAAkB,MAAM,KAAKvC,OAAA,CAAQhJ,sBAAA,CAAuBjY,GAAG;IACrEkE,OAAA,CAAOvK,KAAA,CAAM,sBAAsB;IAEnC,OAAO6pB,eAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA,MAAaC,cAAc7oB,IAAA,GAA0B,CAAC,GAAkB;IA/qB5E,IAAAyH,EAAA;IAgrBQ,MAAM6B,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,eAAe;IAClD,MAAM;MACFuhB,6BAAA;MACA,GAAGqF;IACP,IAAIjnB,IAAA;IAEJ,MAAM2a,aAAA,GAAgB,KAAKc,QAAA,CAAS2G,6BAAA,IAC7B3a,EAAA,SAAM,KAAKof,SAAA,CAAU,MAArB,gBAAApf,EAAA,CAAyBkQ,QAAA,GAC1B;IAEN,MAAMvS,GAAA,GAAM,KAAKqW,QAAA,CAAS2F,8BAAA;IAC1B,MAAM+F,MAAA,GAAS,MAAM,KAAKX,gBAAA,CAAiBnD,OAAA,CAAQ;MAAEzB;IAA8B,CAAC;IACpF,MAAM,KAAK6G,QAAA,CAAS;MAChB5P,YAAA,EAAc;MACdrE,wBAAA,EAA0BpP,GAAA;MAC1BuV,aAAA;MACA,GAAGsM;IACP,GAAGE,MAAM;IAET7d,OAAA,CAAOtK,IAAA,CAAK,SAAS;EACzB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUA,MAAampB,sBAAsB/iB,GAAA,GAAMpD,MAAA,CAAO6d,QAAA,CAAS3S,IAAA,EAAqB;IAC1E,MAAM5D,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,uBAAuB;IAC1D,MAAM,KAAKmmB,gBAAA,CAAiBlD,QAAA,CAASle,GAAG;IACxCkE,OAAA,CAAOtK,IAAA,CAAK,SAAS;EACzB;EAEA,MAAa8pB,aAAaC,KAAA,EAA0C;IAChE,MAAMpL,IAAA,GAAO,MAAM,KAAKkJ,SAAA,CAAU;IAClC,MAAM,KAAK6B,eAAA,CAAgB/K,IAAA,EAAMoL,KAAK;EAC1C;EAEA,MAAgBL,gBAAgB/K,IAAA,EAAmBoL,KAAA,GAAQ,KAAKtN,QAAA,CAASyG,gBAAA,EAAiC;IACtG,MAAM5Y,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,iBAAiB;IACpD,IAAI,CAACsd,IAAA,EAAM;IAEX,MAAMqL,YAAA,GAAeD,KAAA,CAAMtgB,MAAA,CAAO8U,IAAA,IAAQ,OAAOI,IAAA,CAAKJ,IAAI,MAAM,QAAQ;IAExE,IAAI,CAACyL,YAAA,CAAa9a,MAAA,EAAQ;MACtB5E,OAAA,CAAOvK,KAAA,CAAM,sCAAsC;MACnD;IACJ;IAGA,WAAWwe,IAAA,IAAQyL,YAAA,EAAc;MAC7B,MAAM,KAAK3C,OAAA,CAAQ/I,WAAA,CACfK,IAAA,CAAKJ,IAAI,GACTA,IACJ;MACAjU,OAAA,CAAOtK,IAAA,CAAK,GAAGue,IAAI,uBAAuB;MAC1C,IAAIA,IAAA,KAAS,gBAAgB;QACzBI,IAAA,CAAKJ,IAAI,IAAI;MACjB;IACJ;IAEA,MAAM,KAAKwJ,SAAA,CAAUpJ,IAAI;IACzBrU,OAAA,CAAOvK,KAAA,CAAM,aAAa;IAC1B,MAAM,KAAK0nB,OAAA,CAAQtb,IAAA,CAAKwS,IAAI;EAChC;EAAA;AAAA;AAAA;EAKOgJ,iBAAA,EAAyB;IAC5B,KAAK/f,OAAA,CAAQvG,MAAA,CAAO,kBAAkB;IACtC,KAAK,KAAKqmB,mBAAA,CAAoBjZ,KAAA,CAAM;EACxC;EAAA;AAAA;AAAA;EAKOwb,gBAAA,EAAwB;IAC3B,KAAKvC,mBAAA,CAAoB/Z,IAAA,CAAK;EAClC;EAEA,IAAcuc,cAAA,EAAwB;IAClC,OAAO,QAAQ,KAAKzN,QAAA,CAASrH,SAAS,IAAI,KAAKqH,QAAA,CAASvX,SAAS;EACrE;EAEA,MAAgB2iB,UAAA,EAAkC;IAC9C,MAAMvd,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,WAAW;IAC9C,MAAM2Y,aAAA,GAAgB,MAAM,KAAKyC,QAAA,CAAS6G,SAAA,CAAUxS,GAAA,CAAI,KAAKoZ,aAAa;IAC1E,IAAIlQ,aAAA,EAAe;MACf1P,OAAA,CAAOvK,KAAA,CAAM,2BAA2B;MACxC,OAAOogB,IAAA,CAAKpG,iBAAA,CAAkBC,aAAa;IAC/C;IAEA1P,OAAA,CAAOvK,KAAA,CAAM,uBAAuB;IACpC,OAAO;EACX;EAEA,MAAagoB,UAAUpJ,IAAA,EAAkC;IACrD,MAAMrU,OAAA,GAAS,KAAK1C,OAAA,CAAQvG,MAAA,CAAO,WAAW;IAC9C,IAAIsd,IAAA,EAAM;MACNrU,OAAA,CAAOvK,KAAA,CAAM,cAAc;MAC3B,MAAMia,aAAA,GAAgB2E,IAAA,CAAK7E,eAAA,CAAgB;MAC3C,MAAM,KAAK2C,QAAA,CAAS6G,SAAA,CAAU7O,GAAA,CAAI,KAAKyV,aAAA,EAAelQ,aAAa;IACvE,OAAO;MACH,KAAKpS,OAAA,CAAQ7H,KAAA,CAAM,eAAe;MAClC,MAAM,KAAK0c,QAAA,CAAS6G,SAAA,CAAU5O,MAAA,CAAO,KAAKwV,aAAa;MACvD,IAAI,KAAKzN,QAAA,CAAS7F,IAAA,EAAM;QACpB,MAAM,KAAK6F,QAAA,CAAS7F,IAAA,CAAKvC,KAAA,CAAMK,MAAA,CAAO,KAAK+H,QAAA,CAASvX,SAAS;MACjE;IACJ;EACJ;EAAA;AAAA;AAAA;EAKA,MAAa+U,gBAAA,EAAiC;IAC1C,MAAM,KAAKoN,OAAA,CAAQpN,eAAA,CAAgB;EACvC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAaA,MAAaqD,UAAUlX,GAAA,EAAauY,IAAA,EAAYrY,UAAA,EAAqBE,KAAA,EAA6C;IArzBtH,IAAAiC,EAAA,EAAAC,EAAA;IAszBQ,MAAM+U,SAAA,GAAY,QAAM/U,EAAA,IAAAD,EAAA,QAAKgU,QAAA,CAAS7F,IAAA,KAAd,gBAAAnO,EAAA,CAAoB4L,KAAA,KAApB,gBAAA3L,EAAA,CAA2BoI,GAAA,CAAI,KAAK2L,QAAA,CAASvX,SAAA;IACrE,IAAIuY,SAAA,EAAW;MACX,OAAO,MAAMlb,WAAA,CAAY4D,iBAAA,CAAkB;QACvCC,GAAA;QACAC,WAAA,EAAasY,IAAA,oBAAAA,IAAA,CAAMtS,YAAA;QACnB/F,UAAA;QACAC,OAAA,EAASkX,SAAA,CAAU5L,IAAA;QACnBrL;MACJ,CAAC;IACL;IACA,OAAO;EACX;EAEA,MAAMa,gBAAgB8iB,YAAA,EAAyD;IAC3E,IAAI1M,SAAA,GAAY,MAAM0M,YAAA,CAAa9V,KAAA,CAAMvD,GAAA,CAAI,KAAK2L,QAAA,CAASvX,SAAS;IACpE,IAAI,CAACuY,SAAA,EAAW;MACZ,MAAM2M,QAAA,GAAW,MAAM7nB,WAAA,CAAY+E,gBAAA,CAAiB;MACpDmW,SAAA,GAAY,IAAIlB,SAAA,CAAU6N,QAAQ;MAClC,MAAMD,YAAA,CAAa9V,KAAA,CAAMI,GAAA,CAAI,KAAKgI,QAAA,CAASvX,SAAA,EAAWuY,SAAS;IACnE;IACA,OAAO,MAAMlb,WAAA,CAAY8E,eAAA,CAAgBoW,SAAA,CAAU5L,IAAI;EAC3D;AACJ;;;AC10BE,IAAAwY,OAAA,GAAW;;;ACIN,IAAMC,OAAA,GAAkBD,OAAA;;;ACCxB,IAAME,kBAAA,GAAN,MAA8C;EAA9CzpB,YAAA;IACH,KAAS0pB,OAAA,GAAkB;IAC3B,KAASC,UAAA,GAAqB;EAAA;EAE9B,MAAahW,IAAI/K,GAAA,EAAajJ,KAAA,EAAiC;IAC3D,MAAM4T,KAAA,GAAQ,MAAM,KAAKqW,WAAA,CAAY,KAAKF,OAAA,EAAS,KAAKC,UAAU;IAClE,MAAMpW,KAAA,CAAM,aAAcsW,GAAA,IAAwB;MAC9CA,GAAA,CAAIC,GAAA,CAAInqB,KAAA,EAAOiJ,GAAG;MAClB,OAAO,KAAKmhB,gBAAA,CAAiBF,GAAA,CAAIG,WAAW;IAChD,CAAC;EACL;EAEA,MAAaha,IAAIpH,GAAA,EAAiC;IAC9C,MAAM2K,KAAA,GAAQ,MAAM,KAAKqW,WAAA,CAAY,KAAKF,OAAA,EAAS,KAAKC,UAAU;IAClE,OAAO,MAAMpW,KAAA,CAAM,YAAasW,GAAA,IAAQ;MACpC,OAAO,KAAKE,gBAAA,CAAiBF,GAAA,CAAI7Z,GAAA,CAAIpH,GAAG,CAAC;IAC7C,CAAC;EACL;EAEA,MAAagL,OAAOhL,GAAA,EAAiC;IACjD,MAAMqH,IAAA,GAAO,MAAM,KAAKD,GAAA,CAAIpH,GAAG;IAC/B,MAAM2K,KAAA,GAAQ,MAAM,KAAKqW,WAAA,CAAY,KAAKF,OAAA,EAAS,KAAKC,UAAU;IAClE,MAAMpW,KAAA,CAAM,aAAcsW,GAAA,IAAQ;MAC9B,OAAO,KAAKE,gBAAA,CAAiBF,GAAA,CAAII,MAAA,CAAOrhB,GAAG,CAAC;IAChD,CAAC;IACD,OAAOqH,IAAA;EACX;EAEA,MAAa4D,WAAA,EAAgC;IACzC,MAAMN,KAAA,GAAQ,MAAM,KAAKqW,WAAA,CAAY,KAAKF,OAAA,EAAS,KAAKC,UAAU;IAClE,OAAO,MAAMpW,KAAA,CAAM,YAAasW,GAAA,IAAQ;MACpC,OAAO,KAAKE,gBAAA,CAAiBF,GAAA,CAAIhW,UAAA,CAAW,CAAC;IACjD,CAAC;EACL;EAEAkW,iBACIhO,OAAA,EAAqD;IACrD,OAAO,IAAI1O,OAAA,CAAW,CAACC,OAAA,EAAS2S,MAAA,KAAW;MACtClE,OAAA,CAA2BmO,UAAA,GAAcnO,OAAA,CAA0BoO,SAAA,GAAY,MAAM7c,OAAA,CAASyO,OAAA,CAA0Bb,MAAM;MAC9Ha,OAAA,CAA2BqO,OAAA,GAAWrO,OAAA,CAA0BsO,OAAA,GAAU,MAAMpK,MAAA,CAAQlE,OAAA,CAA0B3c,KAAc;IACrI,CAAC;EACL;EAEA,MAAMwqB,YACFU,MAAA,EACAC,SAAA,EAC4G;IAC5G,MAAMxO,OAAA,GAAUyO,SAAA,CAAU1G,IAAA,CAAKwG,MAAM;IACrCvO,OAAA,CAAQ0O,eAAA,GAAkB,MAAM1O,OAAA,CAAQb,MAAA,CAAOwP,iBAAA,CAAkBH,SAAS;IAC1E,MAAMI,EAAA,GAAK,MAAM,KAAKZ,gBAAA,CAA8BhO,OAAO;IAE3D,OAAO,OACH6O,MAAA,EACApH,QAAA,KACC;MACD,MAAMqH,EAAA,GAAKF,EAAA,CAAGX,WAAA,CAAYO,SAAA,EAAWK,MAAM;MAC3C,MAAMrX,KAAA,GAAQsX,EAAA,CAAGC,WAAA,CAAYP,SAAS;MACtC,OAAO,MAAM/G,QAAA,CAASjQ,KAAK;IAC/B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}