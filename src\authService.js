import { UserManager } from 'oidc-client-ts';

const oidcConfig = {
  authority: 'http://localhost:8000',
  client_id: 'client1',
  redirect_uri: 'http://127.0.0.1:3001/callback/authorized',
  response_type: 'code',
  scope: 'openid read mywrite',
  post_logout_redirect_uri: 'http://127.0.0.1:3001/',
  automaticSilentRenew: true,
  silent_redirect_uri: 'http://127.0.0.1:3001/silent-renew'
};

const userManager = new UserManager(oidcConfig);
export default userManager;