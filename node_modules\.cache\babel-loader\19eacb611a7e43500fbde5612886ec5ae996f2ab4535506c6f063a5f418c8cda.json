{"ast": null, "code": "/**\n * react-router v7.9.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { ENABLE_DEV_WARNINGS, ErrorResponseImpl, FrameworkContext, NO_BODY_STATUS_CODES, Outlet, RSCRouterContext, RemixErrorBoundary, RouterContextProvider, RouterProvider, SINGLE_FETCH_REDIRECT_STATUS, SingleFetchRedirectSymbol, StaticRouterProvider, StreamTransfer, UNSTABLE_TransitionEnabledRouterProvider, convertRoutesToDataRoutes, createBrowserHistory, createContext, createMemoryRouter, createRequestInit, createRouter, createServerRoutes, createStaticHandler, createStaticRouter, decodeViaTurboStream, encode, escapeHtml, getManifestPath, getSingleFetchDataStrategyImpl, getStaticContextFromError, invariant, isDataWithResponseInit, isMutationMethod, isRedirectResponse, isRedirectStatusCode, isResponse, isRouteErrorResponse, matchRoutes, noActionDefinedError, redirect, redirectDocument, replace, setIsHydrated, shouldHydrateRouteLoader, singleFetchUrl, stripBasename, stripIndexParam, useRouteError, warnOnce, withComponentProps, withErrorBoundaryProps, withHydrateFallbackProps } from \"./chunk-NISHYRIK.mjs\";\n\n// lib/dom/ssr/server.tsx\nimport * as React from \"react\";\nfunction ServerRouter({\n  context,\n  url,\n  nonce\n}) {\n  if (typeof url === \"string\") {\n    url = new URL(url);\n  }\n  let {\n    manifest,\n    routeModules,\n    criticalCss,\n    serverHandoffString\n  } = context;\n  let routes = createServerRoutes(manifest.routes, routeModules, context.future, context.isSpaMode);\n  context.staticHandlerContext.loaderData = {\n    ...context.staticHandlerContext.loaderData\n  };\n  for (let match of context.staticHandlerContext.matches) {\n    let routeId = match.route.id;\n    let route = routeModules[routeId];\n    let manifestRoute = context.manifest.routes[routeId];\n    if (route && manifestRoute && shouldHydrateRouteLoader(routeId, route.clientLoader, manifestRoute.hasLoader, context.isSpaMode) && (route.HydrateFallback || !manifestRoute.hasLoader)) {\n      delete context.staticHandlerContext.loaderData[routeId];\n    }\n  }\n  let router = createStaticRouter(routes, context.staticHandlerContext);\n  return /* @__PURE__ */React.createElement(React.Fragment, null, /* @__PURE__ */React.createElement(FrameworkContext.Provider, {\n    value: {\n      manifest,\n      routeModules,\n      criticalCss,\n      serverHandoffString,\n      future: context.future,\n      ssr: context.ssr,\n      isSpaMode: context.isSpaMode,\n      routeDiscovery: context.routeDiscovery,\n      serializeError: context.serializeError,\n      renderMeta: context.renderMeta\n    }\n  }, /* @__PURE__ */React.createElement(RemixErrorBoundary, {\n    location: router.state.location\n  }, /* @__PURE__ */React.createElement(StaticRouterProvider, {\n    router,\n    context: context.staticHandlerContext,\n    hydrate: false\n  }))), context.serverHandoffStream ? /* @__PURE__ */React.createElement(React.Suspense, null, /* @__PURE__ */React.createElement(StreamTransfer, {\n    context,\n    identifier: 0,\n    reader: context.serverHandoffStream.getReader(),\n    textDecoder: new TextDecoder(),\n    nonce\n  })) : null);\n}\n\n// lib/dom/ssr/routes-test-stub.tsx\nimport * as React2 from \"react\";\nfunction createRoutesStub(routes, _context) {\n  return function RoutesTestStub({\n    initialEntries,\n    initialIndex,\n    hydrationData,\n    future\n  }) {\n    let routerRef = React2.useRef();\n    let frameworkContextRef = React2.useRef();\n    if (routerRef.current == null) {\n      frameworkContextRef.current = {\n        future: {\n          unstable_subResourceIntegrity: future?.unstable_subResourceIntegrity === true,\n          v8_middleware: future?.v8_middleware === true\n        },\n        manifest: {\n          routes: {},\n          entry: {\n            imports: [],\n            module: \"\"\n          },\n          url: \"\",\n          version: \"\"\n        },\n        routeModules: {},\n        ssr: false,\n        isSpaMode: false,\n        routeDiscovery: {\n          mode: \"lazy\",\n          manifestPath: \"/__manifest\"\n        }\n      };\n      let patched = processRoutes(\n      // @ts-expect-error `StubRouteObject` is stricter about `loader`/`action`\n      // types compared to `AgnosticRouteObject`\n      convertRoutesToDataRoutes(routes, r => r), _context !== void 0 ? _context : future?.v8_middleware ? new RouterContextProvider() : {}, frameworkContextRef.current.manifest, frameworkContextRef.current.routeModules);\n      routerRef.current = createMemoryRouter(patched, {\n        initialEntries,\n        initialIndex,\n        hydrationData\n      });\n    }\n    return /* @__PURE__ */React2.createElement(FrameworkContext.Provider, {\n      value: frameworkContextRef.current\n    }, /* @__PURE__ */React2.createElement(RouterProvider, {\n      router: routerRef.current\n    }));\n  };\n}\nfunction processRoutes(routes, context, manifest, routeModules, parentId) {\n  return routes.map(route => {\n    if (!route.id) {\n      throw new Error(\"Expected a route.id in react-router processRoutes() function\");\n    }\n    let newRoute = {\n      id: route.id,\n      path: route.path,\n      index: route.index,\n      Component: route.Component ? withComponentProps(route.Component) : void 0,\n      HydrateFallback: route.HydrateFallback ? withHydrateFallbackProps(route.HydrateFallback) : void 0,\n      ErrorBoundary: route.ErrorBoundary ? withErrorBoundaryProps(route.ErrorBoundary) : void 0,\n      action: route.action ? args => route.action({\n        ...args,\n        context\n      }) : void 0,\n      loader: route.loader ? args => route.loader({\n        ...args,\n        context\n      }) : void 0,\n      middleware: route.middleware ? route.middleware.map(mw => (...args) => mw({\n        ...args[0],\n        context\n      }, args[1])) : void 0,\n      handle: route.handle,\n      shouldRevalidate: route.shouldRevalidate\n    };\n    let entryRoute = {\n      id: route.id,\n      path: route.path,\n      index: route.index,\n      parentId,\n      hasAction: route.action != null,\n      hasLoader: route.loader != null,\n      // When testing routes, you should be stubbing loader/action/middleware,\n      // not trying to re-implement the full loader/clientLoader/SSR/hydration\n      // flow. That is better tested via E2E tests.\n      hasClientAction: false,\n      hasClientLoader: false,\n      hasClientMiddleware: false,\n      hasErrorBoundary: route.ErrorBoundary != null,\n      // any need for these?\n      module: \"build/stub-path-to-module.js\",\n      clientActionModule: void 0,\n      clientLoaderModule: void 0,\n      clientMiddlewareModule: void 0,\n      hydrateFallbackModule: void 0\n    };\n    manifest.routes[newRoute.id] = entryRoute;\n    routeModules[route.id] = {\n      default: newRoute.Component || Outlet,\n      ErrorBoundary: newRoute.ErrorBoundary || void 0,\n      handle: route.handle,\n      links: route.links,\n      meta: route.meta,\n      shouldRevalidate: route.shouldRevalidate\n    };\n    if (route.children) {\n      newRoute.children = processRoutes(route.children, context, manifest, routeModules, newRoute.id);\n    }\n    return newRoute;\n  });\n}\n\n// lib/server-runtime/cookies.ts\nimport { parse, serialize } from \"cookie\";\n\n// lib/server-runtime/crypto.ts\nvar encoder = /* @__PURE__ */new TextEncoder();\nvar sign = async (value, secret) => {\n  let data2 = encoder.encode(value);\n  let key = await createKey(secret, [\"sign\"]);\n  let signature = await crypto.subtle.sign(\"HMAC\", key, data2);\n  let hash = btoa(String.fromCharCode(...new Uint8Array(signature))).replace(/=+$/, \"\");\n  return value + \".\" + hash;\n};\nvar unsign = async (cookie, secret) => {\n  let index = cookie.lastIndexOf(\".\");\n  let value = cookie.slice(0, index);\n  let hash = cookie.slice(index + 1);\n  let data2 = encoder.encode(value);\n  let key = await createKey(secret, [\"verify\"]);\n  try {\n    let signature = byteStringToUint8Array(atob(hash));\n    let valid = await crypto.subtle.verify(\"HMAC\", key, signature, data2);\n    return valid ? value : false;\n  } catch (error) {\n    return false;\n  }\n};\nvar createKey = async (secret, usages) => crypto.subtle.importKey(\"raw\", encoder.encode(secret), {\n  name: \"HMAC\",\n  hash: \"SHA-256\"\n}, false, usages);\nfunction byteStringToUint8Array(byteString) {\n  let array = new Uint8Array(byteString.length);\n  for (let i = 0; i < byteString.length; i++) {\n    array[i] = byteString.charCodeAt(i);\n  }\n  return array;\n}\n\n// lib/server-runtime/cookies.ts\nvar createCookie = (name, cookieOptions = {}) => {\n  let {\n    secrets = [],\n    ...options\n  } = {\n    path: \"/\",\n    sameSite: \"lax\",\n    ...cookieOptions\n  };\n  warnOnceAboutExpiresCookie(name, options.expires);\n  return {\n    get name() {\n      return name;\n    },\n    get isSigned() {\n      return secrets.length > 0;\n    },\n    get expires() {\n      return typeof options.maxAge !== \"undefined\" ? new Date(Date.now() + options.maxAge * 1e3) : options.expires;\n    },\n    async parse(cookieHeader, parseOptions) {\n      if (!cookieHeader) return null;\n      let cookies = parse(cookieHeader, {\n        ...options,\n        ...parseOptions\n      });\n      if (name in cookies) {\n        let value = cookies[name];\n        if (typeof value === \"string\" && value !== \"\") {\n          let decoded = await decodeCookieValue(value, secrets);\n          return decoded;\n        } else {\n          return \"\";\n        }\n      } else {\n        return null;\n      }\n    },\n    async serialize(value, serializeOptions) {\n      return serialize(name, value === \"\" ? \"\" : await encodeCookieValue(value, secrets), {\n        ...options,\n        ...serializeOptions\n      });\n    }\n  };\n};\nvar isCookie = object => {\n  return object != null && typeof object.name === \"string\" && typeof object.isSigned === \"boolean\" && typeof object.parse === \"function\" && typeof object.serialize === \"function\";\n};\nasync function encodeCookieValue(value, secrets) {\n  let encoded = encodeData(value);\n  if (secrets.length > 0) {\n    encoded = await sign(encoded, secrets[0]);\n  }\n  return encoded;\n}\nasync function decodeCookieValue(value, secrets) {\n  if (secrets.length > 0) {\n    for (let secret of secrets) {\n      let unsignedValue = await unsign(value, secret);\n      if (unsignedValue !== false) {\n        return decodeData(unsignedValue);\n      }\n    }\n    return null;\n  }\n  return decodeData(value);\n}\nfunction encodeData(value) {\n  return btoa(myUnescape(encodeURIComponent(JSON.stringify(value))));\n}\nfunction decodeData(value) {\n  try {\n    return JSON.parse(decodeURIComponent(myEscape(atob(value))));\n  } catch (error) {\n    return {};\n  }\n}\nfunction myEscape(value) {\n  let str = value.toString();\n  let result = \"\";\n  let index = 0;\n  let chr, code;\n  while (index < str.length) {\n    chr = str.charAt(index++);\n    if (/[\\w*+\\-./@]/.exec(chr)) {\n      result += chr;\n    } else {\n      code = chr.charCodeAt(0);\n      if (code < 256) {\n        result += \"%\" + hex(code, 2);\n      } else {\n        result += \"%u\" + hex(code, 4).toUpperCase();\n      }\n    }\n  }\n  return result;\n}\nfunction hex(code, length) {\n  let result = code.toString(16);\n  while (result.length < length) result = \"0\" + result;\n  return result;\n}\nfunction myUnescape(value) {\n  let str = value.toString();\n  let result = \"\";\n  let index = 0;\n  let chr, part;\n  while (index < str.length) {\n    chr = str.charAt(index++);\n    if (chr === \"%\") {\n      if (str.charAt(index) === \"u\") {\n        part = str.slice(index + 1, index + 5);\n        if (/^[\\da-f]{4}$/i.exec(part)) {\n          result += String.fromCharCode(parseInt(part, 16));\n          index += 5;\n          continue;\n        }\n      } else {\n        part = str.slice(index, index + 2);\n        if (/^[\\da-f]{2}$/i.exec(part)) {\n          result += String.fromCharCode(parseInt(part, 16));\n          index += 2;\n          continue;\n        }\n      }\n    }\n    result += chr;\n  }\n  return result;\n}\nfunction warnOnceAboutExpiresCookie(name, expires) {\n  warnOnce(!expires, `The \"${name}\" cookie has an \"expires\" property set. This will cause the expires value to not be updated when the session is committed. Instead, you should set the expires value when serializing the cookie. You can use \\`commitSession(session, { expires })\\` if using a session storage object, or \\`cookie.serialize(\"value\", { expires })\\` if you're using the cookie directly.`);\n}\n\n// lib/server-runtime/entry.ts\nfunction createEntryRouteModules(manifest) {\n  return Object.keys(manifest).reduce((memo, routeId) => {\n    let route = manifest[routeId];\n    if (route) {\n      memo[routeId] = route.module;\n    }\n    return memo;\n  }, {});\n}\n\n// lib/server-runtime/mode.ts\nvar ServerMode = /* @__PURE__ */(ServerMode2 => {\n  ServerMode2[\"Development\"] = \"development\";\n  ServerMode2[\"Production\"] = \"production\";\n  ServerMode2[\"Test\"] = \"test\";\n  return ServerMode2;\n})(ServerMode || {});\nfunction isServerMode(value) {\n  return value === \"development\" /* Development */ || value === \"production\" /* Production */ || value === \"test\" /* Test */;\n}\n\n// lib/server-runtime/errors.ts\nfunction sanitizeError(error, serverMode) {\n  if (error instanceof Error && serverMode !== \"development\" /* Development */) {\n    let sanitized = new Error(\"Unexpected Server Error\");\n    sanitized.stack = void 0;\n    return sanitized;\n  }\n  return error;\n}\nfunction sanitizeErrors(errors, serverMode) {\n  return Object.entries(errors).reduce((acc, [routeId, error]) => {\n    return Object.assign(acc, {\n      [routeId]: sanitizeError(error, serverMode)\n    });\n  }, {});\n}\nfunction serializeError(error, serverMode) {\n  let sanitized = sanitizeError(error, serverMode);\n  return {\n    message: sanitized.message,\n    stack: sanitized.stack\n  };\n}\nfunction serializeErrors(errors, serverMode) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    if (isRouteErrorResponse(val)) {\n      serialized[key] = {\n        ...val,\n        __type: \"RouteErrorResponse\"\n      };\n    } else if (val instanceof Error) {\n      let sanitized = sanitizeError(val, serverMode);\n      serialized[key] = {\n        message: sanitized.message,\n        stack: sanitized.stack,\n        __type: \"Error\",\n        // If this is a subclass (i.e., ReferenceError), send up the type so we\n        // can re-create the same type during hydration.  This will only apply\n        // in dev mode since all production errors are sanitized to normal\n        // Error instances\n        ...(sanitized.name !== \"Error\" ? {\n          __subType: sanitized.name\n        } : {})\n      };\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n\n// lib/server-runtime/routeMatching.ts\nfunction matchServerRoutes(routes, pathname, basename) {\n  let matches = matchRoutes(routes, pathname, basename);\n  if (!matches) return null;\n  return matches.map(match => ({\n    params: match.params,\n    pathname: match.pathname,\n    route: match.route\n  }));\n}\n\n// lib/server-runtime/data.ts\nasync function callRouteHandler(handler, args) {\n  let result = await handler({\n    request: stripRoutesParam(stripIndexParam2(args.request)),\n    params: args.params,\n    context: args.context\n  });\n  if (isDataWithResponseInit(result) && result.init && result.init.status && isRedirectStatusCode(result.init.status)) {\n    throw new Response(null, result.init);\n  }\n  return result;\n}\nfunction stripIndexParam2(request) {\n  let url = new URL(request.url);\n  let indexValues = url.searchParams.getAll(\"index\");\n  url.searchParams.delete(\"index\");\n  let indexValuesToKeep = [];\n  for (let indexValue of indexValues) {\n    if (indexValue) {\n      indexValuesToKeep.push(indexValue);\n    }\n  }\n  for (let toKeep of indexValuesToKeep) {\n    url.searchParams.append(\"index\", toKeep);\n  }\n  let init = {\n    method: request.method,\n    body: request.body,\n    headers: request.headers,\n    signal: request.signal\n  };\n  if (init.body) {\n    init.duplex = \"half\";\n  }\n  return new Request(url.href, init);\n}\nfunction stripRoutesParam(request) {\n  let url = new URL(request.url);\n  url.searchParams.delete(\"_routes\");\n  let init = {\n    method: request.method,\n    body: request.body,\n    headers: request.headers,\n    signal: request.signal\n  };\n  if (init.body) {\n    init.duplex = \"half\";\n  }\n  return new Request(url.href, init);\n}\n\n// lib/server-runtime/invariant.ts\nfunction invariant2(value, message) {\n  if (value === false || value === null || typeof value === \"undefined\") {\n    console.error(\"The following error is a bug in React Router; please open an issue! https://github.com/remix-run/react-router/issues/new/choose\");\n    throw new Error(message);\n  }\n}\n\n// lib/server-runtime/dev.ts\nvar globalDevServerHooksKey = \"__reactRouterDevServerHooks\";\nfunction setDevServerHooks(devServerHooks) {\n  globalThis[globalDevServerHooksKey] = devServerHooks;\n}\nfunction getDevServerHooks() {\n  return globalThis[globalDevServerHooksKey];\n}\nfunction getBuildTimeHeader(request, headerName) {\n  if (typeof process !== \"undefined\") {\n    try {\n      if (process.env?.IS_RR_BUILD_REQUEST === \"yes\") {\n        return request.headers.get(headerName);\n      }\n    } catch (e) {}\n  }\n  return null;\n}\n\n// lib/server-runtime/routes.ts\nfunction groupRoutesByParentId(manifest) {\n  let routes = {};\n  Object.values(manifest).forEach(route => {\n    if (route) {\n      let parentId = route.parentId || \"\";\n      if (!routes[parentId]) {\n        routes[parentId] = [];\n      }\n      routes[parentId].push(route);\n    }\n  });\n  return routes;\n}\nfunction createRoutes(manifest, parentId = \"\", routesByParentId = groupRoutesByParentId(manifest)) {\n  return (routesByParentId[parentId] || []).map(route => ({\n    ...route,\n    children: createRoutes(manifest, route.id, routesByParentId)\n  }));\n}\nfunction createStaticHandlerDataRoutes(manifest, future, parentId = \"\", routesByParentId = groupRoutesByParentId(manifest)) {\n  return (routesByParentId[parentId] || []).map(route => {\n    let commonRoute = {\n      // Always include root due to default boundaries\n      hasErrorBoundary: route.id === \"root\" || route.module.ErrorBoundary != null,\n      id: route.id,\n      path: route.path,\n      middleware: route.module.middleware,\n      // Need to use RR's version in the param typed here to permit the optional\n      // context even though we know it'll always be provided in remix\n      loader: route.module.loader ? async args => {\n        let preRenderedData = getBuildTimeHeader(args.request, \"X-React-Router-Prerender-Data\");\n        if (preRenderedData != null) {\n          let encoded = preRenderedData ? decodeURI(preRenderedData) : preRenderedData;\n          invariant2(encoded, \"Missing prerendered data for route\");\n          let uint8array = new TextEncoder().encode(encoded);\n          let stream = new ReadableStream({\n            start(controller) {\n              controller.enqueue(uint8array);\n              controller.close();\n            }\n          });\n          let decoded = await decodeViaTurboStream(stream, global);\n          let data2 = decoded.value;\n          if (data2 && SingleFetchRedirectSymbol in data2) {\n            let result = data2[SingleFetchRedirectSymbol];\n            let init = {\n              status: result.status\n            };\n            if (result.reload) {\n              throw redirectDocument(result.redirect, init);\n            } else if (result.replace) {\n              throw replace(result.redirect, init);\n            } else {\n              throw redirect(result.redirect, init);\n            }\n          } else {\n            invariant2(data2 && route.id in data2, \"Unable to decode prerendered data\");\n            let result = data2[route.id];\n            invariant2(\"data\" in result, \"Unable to process prerendered data\");\n            return result.data;\n          }\n        }\n        let val = await callRouteHandler(route.module.loader, args);\n        return val;\n      } : void 0,\n      action: route.module.action ? args => callRouteHandler(route.module.action, args) : void 0,\n      handle: route.module.handle\n    };\n    return route.index ? {\n      index: true,\n      ...commonRoute\n    } : {\n      caseSensitive: route.caseSensitive,\n      children: createStaticHandlerDataRoutes(manifest, future, route.id, routesByParentId),\n      ...commonRoute\n    };\n  });\n}\n\n// lib/server-runtime/serverHandoff.ts\nfunction createServerHandoffString(serverHandoff) {\n  return escapeHtml(JSON.stringify(serverHandoff));\n}\n\n// lib/server-runtime/headers.ts\nimport { splitCookiesString } from \"set-cookie-parser\";\nfunction getDocumentHeaders(context, build) {\n  return getDocumentHeadersImpl(context, m => {\n    let route = build.routes[m.route.id];\n    invariant2(route, `Route with id \"${m.route.id}\" not found in build`);\n    return route.module.headers;\n  });\n}\nfunction getDocumentHeadersImpl(context, getRouteHeadersFn, _defaultHeaders) {\n  let boundaryIdx = context.errors ? context.matches.findIndex(m => context.errors[m.route.id]) : -1;\n  let matches = boundaryIdx >= 0 ? context.matches.slice(0, boundaryIdx + 1) : context.matches;\n  let errorHeaders;\n  if (boundaryIdx >= 0) {\n    let {\n      actionHeaders,\n      actionData,\n      loaderHeaders,\n      loaderData\n    } = context;\n    context.matches.slice(boundaryIdx).some(match => {\n      let id = match.route.id;\n      if (actionHeaders[id] && (!actionData || !actionData.hasOwnProperty(id))) {\n        errorHeaders = actionHeaders[id];\n      } else if (loaderHeaders[id] && !loaderData.hasOwnProperty(id)) {\n        errorHeaders = loaderHeaders[id];\n      }\n      return errorHeaders != null;\n    });\n  }\n  const defaultHeaders = new Headers(_defaultHeaders);\n  return matches.reduce((parentHeaders, match, idx) => {\n    let {\n      id\n    } = match.route;\n    let loaderHeaders = context.loaderHeaders[id] || new Headers();\n    let actionHeaders = context.actionHeaders[id] || new Headers();\n    let includeErrorHeaders = errorHeaders != null && idx === matches.length - 1;\n    let includeErrorCookies = includeErrorHeaders && errorHeaders !== loaderHeaders && errorHeaders !== actionHeaders;\n    let headersFn = getRouteHeadersFn(match);\n    if (headersFn == null) {\n      let headers2 = new Headers(parentHeaders);\n      if (includeErrorCookies) {\n        prependCookies(errorHeaders, headers2);\n      }\n      prependCookies(actionHeaders, headers2);\n      prependCookies(loaderHeaders, headers2);\n      return headers2;\n    }\n    let headers = new Headers(typeof headersFn === \"function\" ? headersFn({\n      loaderHeaders,\n      parentHeaders,\n      actionHeaders,\n      errorHeaders: includeErrorHeaders ? errorHeaders : void 0\n    }) : headersFn);\n    if (includeErrorCookies) {\n      prependCookies(errorHeaders, headers);\n    }\n    prependCookies(actionHeaders, headers);\n    prependCookies(loaderHeaders, headers);\n    prependCookies(parentHeaders, headers);\n    return headers;\n  }, new Headers(defaultHeaders));\n}\nfunction prependCookies(parentHeaders, childHeaders) {\n  let parentSetCookieString = parentHeaders.get(\"Set-Cookie\");\n  if (parentSetCookieString) {\n    let cookies = splitCookiesString(parentSetCookieString);\n    let childCookies = new Set(childHeaders.getSetCookie());\n    cookies.forEach(cookie => {\n      if (!childCookies.has(cookie)) {\n        childHeaders.append(\"Set-Cookie\", cookie);\n      }\n    });\n  }\n}\n\n// lib/server-runtime/single-fetch.ts\nvar SERVER_NO_BODY_STATUS_CODES = /* @__PURE__ */new Set([...NO_BODY_STATUS_CODES, 304]);\nasync function singleFetchAction(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) {\n  try {\n    let handlerRequest = new Request(handlerUrl, {\n      method: request.method,\n      body: request.body,\n      headers: request.headers,\n      signal: request.signal,\n      ...(request.body ? {\n        duplex: \"half\"\n      } : void 0)\n    });\n    let result = await staticHandler.query(handlerRequest, {\n      requestContext: loadContext,\n      skipLoaderErrorBubbling: true,\n      skipRevalidation: true,\n      generateMiddlewareResponse: build.future.v8_middleware ? async query => {\n        try {\n          let innerResult = await query(handlerRequest);\n          return handleQueryResult(innerResult);\n        } catch (error) {\n          return handleQueryError(error);\n        }\n      } : void 0\n    });\n    return handleQueryResult(result);\n  } catch (error) {\n    return handleQueryError(error);\n  }\n  function handleQueryResult(result) {\n    return isResponse(result) ? result : staticContextToResponse(result);\n  }\n  function handleQueryError(error) {\n    handleError(error);\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: {\n        error\n      },\n      headers: new Headers(),\n      status: 500\n    });\n  }\n  function staticContextToResponse(context) {\n    let headers = getDocumentHeaders(context, build);\n    if (isRedirectStatusCode(context.statusCode) && headers.has(\"Location\")) {\n      return new Response(null, {\n        status: context.statusCode,\n        headers\n      });\n    }\n    if (context.errors) {\n      Object.values(context.errors).forEach(err => {\n        if (!isRouteErrorResponse(err) || err.error) {\n          handleError(err);\n        }\n      });\n      context.errors = sanitizeErrors(context.errors, serverMode);\n    }\n    let singleFetchResult;\n    if (context.errors) {\n      singleFetchResult = {\n        error: Object.values(context.errors)[0]\n      };\n    } else {\n      singleFetchResult = {\n        data: Object.values(context.actionData || {})[0]\n      };\n    }\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: singleFetchResult,\n      headers,\n      status: context.statusCode\n    });\n  }\n}\nasync function singleFetchLoaders(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) {\n  let routesParam = new URL(request.url).searchParams.get(\"_routes\");\n  let loadRouteIds = routesParam ? new Set(routesParam.split(\",\")) : null;\n  try {\n    let handlerRequest = new Request(handlerUrl, {\n      headers: request.headers,\n      signal: request.signal\n    });\n    let result = await staticHandler.query(handlerRequest, {\n      requestContext: loadContext,\n      filterMatchesToLoad: m => !loadRouteIds || loadRouteIds.has(m.route.id),\n      skipLoaderErrorBubbling: true,\n      generateMiddlewareResponse: build.future.v8_middleware ? async query => {\n        try {\n          let innerResult = await query(handlerRequest);\n          return handleQueryResult(innerResult);\n        } catch (error) {\n          return handleQueryError(error);\n        }\n      } : void 0\n    });\n    return handleQueryResult(result);\n  } catch (error) {\n    return handleQueryError(error);\n  }\n  function handleQueryResult(result) {\n    return isResponse(result) ? result : staticContextToResponse(result);\n  }\n  function handleQueryError(error) {\n    handleError(error);\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: {\n        error\n      },\n      headers: new Headers(),\n      status: 500\n    });\n  }\n  function staticContextToResponse(context) {\n    let headers = getDocumentHeaders(context, build);\n    if (isRedirectStatusCode(context.statusCode) && headers.has(\"Location\")) {\n      return new Response(null, {\n        status: context.statusCode,\n        headers\n      });\n    }\n    if (context.errors) {\n      Object.values(context.errors).forEach(err => {\n        if (!isRouteErrorResponse(err) || err.error) {\n          handleError(err);\n        }\n      });\n      context.errors = sanitizeErrors(context.errors, serverMode);\n    }\n    let results = {};\n    let loadedMatches = new Set(context.matches.filter(m => loadRouteIds ? loadRouteIds.has(m.route.id) : m.route.loader != null).map(m => m.route.id));\n    if (context.errors) {\n      for (let [id, error] of Object.entries(context.errors)) {\n        results[id] = {\n          error\n        };\n      }\n    }\n    for (let [id, data2] of Object.entries(context.loaderData)) {\n      if (!(id in results) && loadedMatches.has(id)) {\n        results[id] = {\n          data: data2\n        };\n      }\n    }\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: results,\n      headers,\n      status: context.statusCode\n    });\n  }\n}\nfunction generateSingleFetchResponse(request, build, serverMode, {\n  result,\n  headers,\n  status\n}) {\n  let resultHeaders = new Headers(headers);\n  resultHeaders.set(\"X-Remix-Response\", \"yes\");\n  if (SERVER_NO_BODY_STATUS_CODES.has(status)) {\n    return new Response(null, {\n      status,\n      headers: resultHeaders\n    });\n  }\n  resultHeaders.set(\"Content-Type\", \"text/x-script\");\n  resultHeaders.delete(\"Content-Length\");\n  return new Response(encodeViaTurboStream(result, request.signal, build.entry.module.streamTimeout, serverMode), {\n    status: status || 200,\n    headers: resultHeaders\n  });\n}\nfunction generateSingleFetchRedirectResponse(redirectResponse, request, build, serverMode) {\n  let redirect2 = getSingleFetchRedirect(redirectResponse.status, redirectResponse.headers, build.basename);\n  let headers = new Headers(redirectResponse.headers);\n  headers.delete(\"Location\");\n  headers.set(\"Content-Type\", \"text/x-script\");\n  return generateSingleFetchResponse(request, build, serverMode, {\n    result: request.method === \"GET\" ? {\n      [SingleFetchRedirectSymbol]: redirect2\n    } : redirect2,\n    headers,\n    status: SINGLE_FETCH_REDIRECT_STATUS\n  });\n}\nfunction getSingleFetchRedirect(status, headers, basename) {\n  let redirect2 = headers.get(\"Location\");\n  if (basename) {\n    redirect2 = stripBasename(redirect2, basename) || redirect2;\n  }\n  return {\n    redirect: redirect2,\n    status,\n    revalidate:\n    // Technically X-Remix-Revalidate isn't needed here - that was an implementation\n    // detail of ?_data requests as our way to tell the front end to revalidate when\n    // we didn't have a response body to include that information in.\n    // With single fetch, we tell the front end via this revalidate boolean field.\n    // However, we're respecting it for now because it may be something folks have\n    // used in their own responses\n    // TODO(v3): Consider removing or making this official public API\n    headers.has(\"X-Remix-Revalidate\") || headers.has(\"Set-Cookie\"),\n    reload: headers.has(\"X-Remix-Reload-Document\"),\n    replace: headers.has(\"X-Remix-Replace\")\n  };\n}\nfunction encodeViaTurboStream(data2, requestSignal, streamTimeout, serverMode) {\n  let controller = new AbortController();\n  let timeoutId = setTimeout(() => controller.abort(new Error(\"Server Timeout\")), typeof streamTimeout === \"number\" ? streamTimeout : 4950);\n  requestSignal.addEventListener(\"abort\", () => clearTimeout(timeoutId));\n  return encode(data2, {\n    signal: controller.signal,\n    plugins: [value => {\n      if (value instanceof Error) {\n        let {\n          name,\n          message,\n          stack\n        } = serverMode === \"production\" /* Production */ ? sanitizeError(value, serverMode) : value;\n        return [\"SanitizedError\", name, message, stack];\n      }\n      if (value instanceof ErrorResponseImpl) {\n        let {\n          data: data3,\n          status,\n          statusText\n        } = value;\n        return [\"ErrorResponse\", data3, status, statusText];\n      }\n      if (value && typeof value === \"object\" && SingleFetchRedirectSymbol in value) {\n        return [\"SingleFetchRedirect\", value[SingleFetchRedirectSymbol]];\n      }\n    }],\n    postPlugins: [value => {\n      if (!value) return;\n      if (typeof value !== \"object\") return;\n      return [\"SingleFetchClassInstance\", Object.fromEntries(Object.entries(value))];\n    }, () => [\"SingleFetchFallback\"]]\n  });\n}\n\n// lib/server-runtime/server.ts\nfunction derive(build, mode) {\n  let routes = createRoutes(build.routes);\n  let dataRoutes = createStaticHandlerDataRoutes(build.routes, build.future);\n  let serverMode = isServerMode(mode) ? mode : \"production\" /* Production */;\n  let staticHandler = createStaticHandler(dataRoutes, {\n    basename: build.basename\n  });\n  let errorHandler = build.entry.module.handleError || ((error, {\n    request\n  }) => {\n    if (serverMode !== \"test\" /* Test */ && !request.signal.aborted) {\n      console.error(\n      // @ts-expect-error This is \"private\" from users but intended for internal use\n      isRouteErrorResponse(error) && error.error ? error.error : error);\n    }\n  });\n  return {\n    routes,\n    dataRoutes,\n    serverMode,\n    staticHandler,\n    errorHandler\n  };\n}\nvar createRequestHandler = (build, mode) => {\n  let _build;\n  let routes;\n  let serverMode;\n  let staticHandler;\n  let errorHandler;\n  return async function requestHandler(request, initialContext) {\n    _build = typeof build === \"function\" ? await build() : build;\n    if (typeof build === \"function\") {\n      let derived = derive(_build, mode);\n      routes = derived.routes;\n      serverMode = derived.serverMode;\n      staticHandler = derived.staticHandler;\n      errorHandler = derived.errorHandler;\n    } else if (!routes || !serverMode || !staticHandler || !errorHandler) {\n      let derived = derive(_build, mode);\n      routes = derived.routes;\n      serverMode = derived.serverMode;\n      staticHandler = derived.staticHandler;\n      errorHandler = derived.errorHandler;\n    }\n    let params = {};\n    let loadContext;\n    let handleError = error => {\n      if (mode === \"development\" /* Development */) {\n        getDevServerHooks()?.processRequestError?.(error);\n      }\n      errorHandler(error, {\n        context: loadContext,\n        params,\n        request\n      });\n    };\n    if (_build.future.v8_middleware) {\n      if (initialContext && !(initialContext instanceof RouterContextProvider)) {\n        let error = new Error(\"Invalid `context` value provided to `handleRequest`. When middleware is enabled you must return an instance of `RouterContextProvider` from your `getLoadContext` function.\");\n        handleError(error);\n        return returnLastResortErrorResponse(error, serverMode);\n      }\n      loadContext = initialContext || new RouterContextProvider();\n    } else {\n      loadContext = initialContext || {};\n    }\n    let url = new URL(request.url);\n    let normalizedBasename = _build.basename || \"/\";\n    let normalizedPath = url.pathname;\n    if (stripBasename(normalizedPath, normalizedBasename) === \"/_root.data\") {\n      normalizedPath = normalizedBasename;\n    } else if (normalizedPath.endsWith(\".data\")) {\n      normalizedPath = normalizedPath.replace(/\\.data$/, \"\");\n    }\n    if (stripBasename(normalizedPath, normalizedBasename) !== \"/\" && normalizedPath.endsWith(\"/\")) {\n      normalizedPath = normalizedPath.slice(0, -1);\n    }\n    let isSpaMode = getBuildTimeHeader(request, \"X-React-Router-SPA-Mode\") === \"yes\";\n    if (!_build.ssr) {\n      let decodedPath = decodeURI(normalizedPath);\n      if (normalizedBasename !== \"/\") {\n        let strippedPath = stripBasename(decodedPath, normalizedBasename);\n        if (strippedPath == null) {\n          errorHandler(new ErrorResponseImpl(404, \"Not Found\", `Refusing to prerender the \\`${decodedPath}\\` path because it does not start with the basename \\`${normalizedBasename}\\``), {\n            context: loadContext,\n            params,\n            request\n          });\n          return new Response(\"Not Found\", {\n            status: 404,\n            statusText: \"Not Found\"\n          });\n        }\n        decodedPath = strippedPath;\n      }\n      if (_build.prerender.length === 0) {\n        isSpaMode = true;\n      } else if (!_build.prerender.includes(decodedPath) && !_build.prerender.includes(decodedPath + \"/\")) {\n        if (url.pathname.endsWith(\".data\")) {\n          errorHandler(new ErrorResponseImpl(404, \"Not Found\", `Refusing to SSR the path \\`${decodedPath}\\` because \\`ssr:false\\` is set and the path is not included in the \\`prerender\\` config, so in production the path will be a 404.`), {\n            context: loadContext,\n            params,\n            request\n          });\n          return new Response(\"Not Found\", {\n            status: 404,\n            statusText: \"Not Found\"\n          });\n        } else {\n          isSpaMode = true;\n        }\n      }\n    }\n    let manifestUrl = getManifestPath(_build.routeDiscovery.manifestPath, normalizedBasename);\n    if (url.pathname === manifestUrl) {\n      try {\n        let res = await handleManifestRequest(_build, routes, url);\n        return res;\n      } catch (e) {\n        handleError(e);\n        return new Response(\"Unknown Server Error\", {\n          status: 500\n        });\n      }\n    }\n    let matches = matchServerRoutes(routes, normalizedPath, _build.basename);\n    if (matches && matches.length > 0) {\n      Object.assign(params, matches[0].params);\n    }\n    let response;\n    if (url.pathname.endsWith(\".data\")) {\n      let handlerUrl = new URL(request.url);\n      handlerUrl.pathname = normalizedPath;\n      let singleFetchMatches = matchServerRoutes(routes, handlerUrl.pathname, _build.basename);\n      response = await handleSingleFetchRequest(serverMode, _build, staticHandler, request, handlerUrl, loadContext, handleError);\n      if (isRedirectResponse(response)) {\n        response = generateSingleFetchRedirectResponse(response, request, _build, serverMode);\n      }\n      if (_build.entry.module.handleDataRequest) {\n        response = await _build.entry.module.handleDataRequest(response, {\n          context: loadContext,\n          params: singleFetchMatches ? singleFetchMatches[0].params : {},\n          request\n        });\n        if (isRedirectResponse(response)) {\n          response = generateSingleFetchRedirectResponse(response, request, _build, serverMode);\n        }\n      }\n    } else if (!isSpaMode && matches && matches[matches.length - 1].route.module.default == null && matches[matches.length - 1].route.module.ErrorBoundary == null) {\n      response = await handleResourceRequest(serverMode, _build, staticHandler, matches.slice(-1)[0].route.id, request, loadContext, handleError);\n    } else {\n      let {\n        pathname\n      } = url;\n      let criticalCss = void 0;\n      if (_build.unstable_getCriticalCss) {\n        criticalCss = await _build.unstable_getCriticalCss({\n          pathname\n        });\n      } else if (mode === \"development\" /* Development */ && getDevServerHooks()?.getCriticalCss) {\n        criticalCss = await getDevServerHooks()?.getCriticalCss?.(pathname);\n      }\n      response = await handleDocumentRequest(serverMode, _build, staticHandler, request, loadContext, handleError, isSpaMode, criticalCss);\n    }\n    if (request.method === \"HEAD\") {\n      return new Response(null, {\n        headers: response.headers,\n        status: response.status,\n        statusText: response.statusText\n      });\n    }\n    return response;\n  };\n};\nasync function handleManifestRequest(build, routes, url) {\n  if (build.assets.version !== url.searchParams.get(\"version\")) {\n    return new Response(null, {\n      status: 204,\n      headers: {\n        \"X-Remix-Reload-Document\": \"true\"\n      }\n    });\n  }\n  let patches = {};\n  if (url.searchParams.has(\"paths\")) {\n    let paths = /* @__PURE__ */new Set();\n    let pathParam = url.searchParams.get(\"paths\") || \"\";\n    let requestedPaths = pathParam.split(\",\").filter(Boolean);\n    requestedPaths.forEach(path => {\n      if (!path.startsWith(\"/\")) {\n        path = `/${path}`;\n      }\n      let segments = path.split(\"/\").slice(1);\n      segments.forEach((_, i) => {\n        let partialPath = segments.slice(0, i + 1).join(\"/\");\n        paths.add(`/${partialPath}`);\n      });\n    });\n    for (let path of paths) {\n      let matches = matchServerRoutes(routes, path, build.basename);\n      if (matches) {\n        for (let match of matches) {\n          let routeId = match.route.id;\n          let route = build.assets.routes[routeId];\n          if (route) {\n            patches[routeId] = route;\n          }\n        }\n      }\n    }\n    return Response.json(patches, {\n      headers: {\n        \"Cache-Control\": \"public, max-age=31536000, immutable\"\n      }\n    });\n  }\n  return new Response(\"Invalid Request\", {\n    status: 400\n  });\n}\nasync function handleSingleFetchRequest(serverMode, build, staticHandler, request, handlerUrl, loadContext, handleError) {\n  let response = request.method !== \"GET\" ? await singleFetchAction(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) : await singleFetchLoaders(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError);\n  return response;\n}\nasync function handleDocumentRequest(serverMode, build, staticHandler, request, loadContext, handleError, isSpaMode, criticalCss) {\n  try {\n    let result = await staticHandler.query(request, {\n      requestContext: loadContext,\n      generateMiddlewareResponse: build.future.v8_middleware ? async query => {\n        try {\n          let innerResult = await query(request);\n          if (!isResponse(innerResult)) {\n            innerResult = await renderHtml(innerResult, isSpaMode);\n          }\n          return innerResult;\n        } catch (error) {\n          handleError(error);\n          return new Response(null, {\n            status: 500\n          });\n        }\n      } : void 0\n    });\n    if (!isResponse(result)) {\n      result = await renderHtml(result, isSpaMode);\n    }\n    return result;\n  } catch (error) {\n    handleError(error);\n    return new Response(null, {\n      status: 500\n    });\n  }\n  async function renderHtml(context, isSpaMode2) {\n    let headers = getDocumentHeaders(context, build);\n    if (SERVER_NO_BODY_STATUS_CODES.has(context.statusCode)) {\n      return new Response(null, {\n        status: context.statusCode,\n        headers\n      });\n    }\n    if (context.errors) {\n      Object.values(context.errors).forEach(err => {\n        if (!isRouteErrorResponse(err) || err.error) {\n          handleError(err);\n        }\n      });\n      context.errors = sanitizeErrors(context.errors, serverMode);\n    }\n    let state = {\n      loaderData: context.loaderData,\n      actionData: context.actionData,\n      errors: serializeErrors(context.errors, serverMode)\n    };\n    let baseServerHandoff = {\n      basename: build.basename,\n      future: build.future,\n      routeDiscovery: build.routeDiscovery,\n      ssr: build.ssr,\n      isSpaMode: isSpaMode2\n    };\n    let entryContext = {\n      manifest: build.assets,\n      routeModules: createEntryRouteModules(build.routes),\n      staticHandlerContext: context,\n      criticalCss,\n      serverHandoffString: createServerHandoffString({\n        ...baseServerHandoff,\n        criticalCss\n      }),\n      serverHandoffStream: encodeViaTurboStream(state, request.signal, build.entry.module.streamTimeout, serverMode),\n      renderMeta: {},\n      future: build.future,\n      ssr: build.ssr,\n      routeDiscovery: build.routeDiscovery,\n      isSpaMode: isSpaMode2,\n      serializeError: err => serializeError(err, serverMode)\n    };\n    let handleDocumentRequestFunction = build.entry.module.default;\n    try {\n      return await handleDocumentRequestFunction(request, context.statusCode, headers, entryContext, loadContext);\n    } catch (error) {\n      handleError(error);\n      let errorForSecondRender = error;\n      if (isResponse(error)) {\n        try {\n          let data2 = await unwrapResponse(error);\n          errorForSecondRender = new ErrorResponseImpl(error.status, error.statusText, data2);\n        } catch (e) {}\n      }\n      context = getStaticContextFromError(staticHandler.dataRoutes, context, errorForSecondRender);\n      if (context.errors) {\n        context.errors = sanitizeErrors(context.errors, serverMode);\n      }\n      let state2 = {\n        loaderData: context.loaderData,\n        actionData: context.actionData,\n        errors: serializeErrors(context.errors, serverMode)\n      };\n      entryContext = {\n        ...entryContext,\n        staticHandlerContext: context,\n        serverHandoffString: createServerHandoffString(baseServerHandoff),\n        serverHandoffStream: encodeViaTurboStream(state2, request.signal, build.entry.module.streamTimeout, serverMode),\n        renderMeta: {}\n      };\n      try {\n        return await handleDocumentRequestFunction(request, context.statusCode, headers, entryContext, loadContext);\n      } catch (error2) {\n        handleError(error2);\n        return returnLastResortErrorResponse(error2, serverMode);\n      }\n    }\n  }\n}\nasync function handleResourceRequest(serverMode, build, staticHandler, routeId, request, loadContext, handleError) {\n  try {\n    let result = await staticHandler.queryRoute(request, {\n      routeId,\n      requestContext: loadContext,\n      generateMiddlewareResponse: build.future.v8_middleware ? async queryRoute => {\n        try {\n          let innerResult = await queryRoute(request);\n          return handleQueryRouteResult(innerResult);\n        } catch (error) {\n          return handleQueryRouteError(error);\n        }\n      } : void 0\n    });\n    return handleQueryRouteResult(result);\n  } catch (error) {\n    return handleQueryRouteError(error);\n  }\n  function handleQueryRouteResult(result) {\n    if (isResponse(result)) {\n      return result;\n    }\n    if (typeof result === \"string\") {\n      return new Response(result);\n    }\n    return Response.json(result);\n  }\n  function handleQueryRouteError(error) {\n    if (isResponse(error)) {\n      return error;\n    }\n    if (isRouteErrorResponse(error)) {\n      handleError(error);\n      return errorResponseToJson(error, serverMode);\n    }\n    if (error instanceof Error && error.message === \"Expected a response from queryRoute\") {\n      let newError = new Error(\"Expected a Response to be returned from resource route handler\");\n      handleError(newError);\n      return returnLastResortErrorResponse(newError, serverMode);\n    }\n    handleError(error);\n    return returnLastResortErrorResponse(error, serverMode);\n  }\n}\nfunction errorResponseToJson(errorResponse, serverMode) {\n  return Response.json(serializeError(\n  // @ts-expect-error This is \"private\" from users but intended for internal use\n  errorResponse.error || new Error(\"Unexpected Server Error\"), serverMode), {\n    status: errorResponse.status,\n    statusText: errorResponse.statusText\n  });\n}\nfunction returnLastResortErrorResponse(error, serverMode) {\n  let message = \"Unexpected Server Error\";\n  if (serverMode !== \"production\" /* Production */) {\n    message += `\n\n${String(error)}`;\n  }\n  return new Response(message, {\n    status: 500,\n    headers: {\n      \"Content-Type\": \"text/plain\"\n    }\n  });\n}\nfunction unwrapResponse(response) {\n  let contentType = response.headers.get(\"Content-Type\");\n  return contentType && /\\bapplication\\/json\\b/.test(contentType) ? response.body == null ? null : response.json() : response.text();\n}\n\n// lib/server-runtime/sessions.ts\nfunction flash(name) {\n  return `__flash_${name}__`;\n}\nvar createSession = (initialData = {}, id = \"\") => {\n  let map = new Map(Object.entries(initialData));\n  return {\n    get id() {\n      return id;\n    },\n    get data() {\n      return Object.fromEntries(map);\n    },\n    has(name) {\n      return map.has(name) || map.has(flash(name));\n    },\n    get(name) {\n      if (map.has(name)) return map.get(name);\n      let flashName = flash(name);\n      if (map.has(flashName)) {\n        let value = map.get(flashName);\n        map.delete(flashName);\n        return value;\n      }\n      return void 0;\n    },\n    set(name, value) {\n      map.set(name, value);\n    },\n    flash(name, value) {\n      map.set(flash(name), value);\n    },\n    unset(name) {\n      map.delete(name);\n    }\n  };\n};\nvar isSession = object => {\n  return object != null && typeof object.id === \"string\" && typeof object.data !== \"undefined\" && typeof object.has === \"function\" && typeof object.get === \"function\" && typeof object.set === \"function\" && typeof object.flash === \"function\" && typeof object.unset === \"function\";\n};\nfunction createSessionStorage({\n  cookie: cookieArg,\n  createData,\n  readData,\n  updateData,\n  deleteData\n}) {\n  let cookie = isCookie(cookieArg) ? cookieArg : createCookie(cookieArg?.name || \"__session\", cookieArg);\n  warnOnceAboutSigningSessionCookie(cookie);\n  return {\n    async getSession(cookieHeader, options) {\n      let id = cookieHeader && (await cookie.parse(cookieHeader, options));\n      let data2 = id && (await readData(id));\n      return createSession(data2 || {}, id || \"\");\n    },\n    async commitSession(session, options) {\n      let {\n        id,\n        data: data2\n      } = session;\n      let expires = options?.maxAge != null ? new Date(Date.now() + options.maxAge * 1e3) : options?.expires != null ? options.expires : cookie.expires;\n      if (id) {\n        await updateData(id, data2, expires);\n      } else {\n        id = await createData(data2, expires);\n      }\n      return cookie.serialize(id, options);\n    },\n    async destroySession(session, options) {\n      await deleteData(session.id);\n      return cookie.serialize(\"\", {\n        ...options,\n        maxAge: void 0,\n        expires: /* @__PURE__ */new Date(0)\n      });\n    }\n  };\n}\nfunction warnOnceAboutSigningSessionCookie(cookie) {\n  warnOnce(cookie.isSigned, `The \"${cookie.name}\" cookie is not signed, but session cookies should be signed to prevent tampering on the client before they are sent back to the server. See https://reactrouter.com/explanation/sessions-and-cookies#signing-cookies for more information.`);\n}\n\n// lib/server-runtime/sessions/cookieStorage.ts\nfunction createCookieSessionStorage({\n  cookie: cookieArg\n} = {}) {\n  let cookie = isCookie(cookieArg) ? cookieArg : createCookie(cookieArg?.name || \"__session\", cookieArg);\n  warnOnceAboutSigningSessionCookie(cookie);\n  return {\n    async getSession(cookieHeader, options) {\n      return createSession(cookieHeader && (await cookie.parse(cookieHeader, options)) || {});\n    },\n    async commitSession(session, options) {\n      let serializedCookie = await cookie.serialize(session.data, options);\n      if (serializedCookie.length > 4096) {\n        throw new Error(\"Cookie length will exceed browser maximum. Length: \" + serializedCookie.length);\n      }\n      return serializedCookie;\n    },\n    async destroySession(_session, options) {\n      return cookie.serialize(\"\", {\n        ...options,\n        maxAge: void 0,\n        expires: /* @__PURE__ */new Date(0)\n      });\n    }\n  };\n}\n\n// lib/server-runtime/sessions/memoryStorage.ts\nfunction createMemorySessionStorage({\n  cookie\n} = {}) {\n  let map = /* @__PURE__ */new Map();\n  return createSessionStorage({\n    cookie,\n    async createData(data2, expires) {\n      let id = Math.random().toString(36).substring(2, 10);\n      map.set(id, {\n        data: data2,\n        expires\n      });\n      return id;\n    },\n    async readData(id) {\n      if (map.has(id)) {\n        let {\n          data: data2,\n          expires\n        } = map.get(id);\n        if (!expires || expires > /* @__PURE__ */new Date()) {\n          return data2;\n        }\n        if (expires) map.delete(id);\n      }\n      return null;\n    },\n    async updateData(id, data2, expires) {\n      map.set(id, {\n        data: data2,\n        expires\n      });\n    },\n    async deleteData(id) {\n      map.delete(id);\n    }\n  });\n}\n\n// lib/href.ts\nfunction href(path, ...args) {\n  let params = args[0];\n  let result = path.replace(/\\/*\\*?$/, \"\").replace(/\\/:([\\w-]+)(\\?)?/g,\n  // same regex as in .\\router\\utils.ts: compilePath().\n  (_, param, questionMark) => {\n    const isRequired = questionMark === void 0;\n    const value = params ? params[param] : void 0;\n    if (isRequired && value === void 0) {\n      throw new Error(`Path '${path}' requires param '${param}' but it was not provided`);\n    }\n    return value === void 0 ? \"\" : \"/\" + value;\n  });\n  if (path.endsWith(\"*\")) {\n    const value = params ? params[\"*\"] : void 0;\n    if (value !== void 0) {\n      result += \"/\" + value;\n    }\n  }\n  return result || \"/\";\n}\n\n// lib/rsc/browser.tsx\nimport * as React4 from \"react\";\nimport * as ReactDOM from \"react-dom\";\n\n// lib/dom/ssr/hydration.tsx\nfunction getHydrationData({\n  state,\n  routes,\n  getRouteInfo,\n  location: location2,\n  basename,\n  isSpaMode\n}) {\n  let hydrationData = {\n    ...state,\n    loaderData: {\n      ...state.loaderData\n    }\n  };\n  let initialMatches = matchRoutes(routes, location2, basename);\n  if (initialMatches) {\n    for (let match of initialMatches) {\n      let routeId = match.route.id;\n      let routeInfo = getRouteInfo(routeId);\n      if (shouldHydrateRouteLoader(routeId, routeInfo.clientLoader, routeInfo.hasLoader, isSpaMode) && (routeInfo.hasHydrateFallback || !routeInfo.hasLoader)) {\n        delete hydrationData.loaderData[routeId];\n      } else if (!routeInfo.hasLoader) {\n        hydrationData.loaderData[routeId] = null;\n      }\n    }\n  }\n  return hydrationData;\n}\n\n// lib/rsc/errorBoundaries.tsx\nimport React3 from \"react\";\nvar RSCRouterGlobalErrorBoundary = class extends React3.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      error: null,\n      location: props.location\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      error\n    };\n  }\n  static getDerivedStateFromProps(props, state) {\n    if (state.location !== props.location) {\n      return {\n        error: null,\n        location: props.location\n      };\n    }\n    return {\n      error: state.error,\n      location: state.location\n    };\n  }\n  render() {\n    if (this.state.error) {\n      return /* @__PURE__ */React3.createElement(RSCDefaultRootErrorBoundaryImpl, {\n        error: this.state.error,\n        renderAppShell: true\n      });\n    } else {\n      return this.props.children;\n    }\n  }\n};\nfunction ErrorWrapper({\n  renderAppShell,\n  title,\n  children\n}) {\n  if (!renderAppShell) {\n    return children;\n  }\n  return /* @__PURE__ */React3.createElement(\"html\", {\n    lang: \"en\"\n  }, /* @__PURE__ */React3.createElement(\"head\", null, /* @__PURE__ */React3.createElement(\"meta\", {\n    charSet: \"utf-8\"\n  }), /* @__PURE__ */React3.createElement(\"meta\", {\n    name: \"viewport\",\n    content: \"width=device-width,initial-scale=1,viewport-fit=cover\"\n  }), /* @__PURE__ */React3.createElement(\"title\", null, title)), /* @__PURE__ */React3.createElement(\"body\", null, /* @__PURE__ */React3.createElement(\"main\", {\n    style: {\n      fontFamily: \"system-ui, sans-serif\",\n      padding: \"2rem\"\n    }\n  }, children)));\n}\nfunction RSCDefaultRootErrorBoundaryImpl({\n  error,\n  renderAppShell\n}) {\n  console.error(error);\n  let heyDeveloper = /* @__PURE__ */React3.createElement(\"script\", {\n    dangerouslySetInnerHTML: {\n      __html: `\n        console.log(\n          \"\\u{1F4BF} Hey developer \\u{1F44B}. You can provide a way better UX than this when your app throws errors. Check out https://reactrouter.com/how-to/error-boundary for more information.\"\n        );\n      `\n    }\n  });\n  if (isRouteErrorResponse(error)) {\n    return /* @__PURE__ */React3.createElement(ErrorWrapper, {\n      renderAppShell,\n      title: \"Unhandled Thrown Response!\"\n    }, /* @__PURE__ */React3.createElement(\"h1\", {\n      style: {\n        fontSize: \"24px\"\n      }\n    }, error.status, \" \", error.statusText), ENABLE_DEV_WARNINGS ? heyDeveloper : null);\n  }\n  let errorInstance;\n  if (error instanceof Error) {\n    errorInstance = error;\n  } else {\n    let errorString = error == null ? \"Unknown Error\" : typeof error === \"object\" && \"toString\" in error ? error.toString() : JSON.stringify(error);\n    errorInstance = new Error(errorString);\n  }\n  return /* @__PURE__ */React3.createElement(ErrorWrapper, {\n    renderAppShell,\n    title: \"Application Error!\"\n  }, /* @__PURE__ */React3.createElement(\"h1\", {\n    style: {\n      fontSize: \"24px\"\n    }\n  }, \"Application Error\"), /* @__PURE__ */React3.createElement(\"pre\", {\n    style: {\n      padding: \"2rem\",\n      background: \"hsla(10, 50%, 50%, 0.1)\",\n      color: \"red\",\n      overflow: \"auto\"\n    }\n  }, errorInstance.stack), heyDeveloper);\n}\nfunction RSCDefaultRootErrorBoundary({\n  hasRootLayout\n}) {\n  let error = useRouteError();\n  if (hasRootLayout === void 0) {\n    throw new Error(\"Missing 'hasRootLayout' prop\");\n  }\n  return /* @__PURE__ */React3.createElement(RSCDefaultRootErrorBoundaryImpl, {\n    renderAppShell: !hasRootLayout,\n    error\n  });\n}\n\n// lib/rsc/route-modules.ts\nfunction createRSCRouteModules(payload) {\n  const routeModules = {};\n  for (const match of payload.matches) {\n    populateRSCRouteModules(routeModules, match);\n  }\n  return routeModules;\n}\nfunction populateRSCRouteModules(routeModules, matches) {\n  matches = Array.isArray(matches) ? matches : [matches];\n  for (const match of matches) {\n    routeModules[match.id] = {\n      links: match.links,\n      meta: match.meta,\n      default: noopComponent\n    };\n  }\n}\nvar noopComponent = () => null;\n\n// lib/rsc/browser.tsx\nfunction createCallServer({\n  createFromReadableStream,\n  createTemporaryReferenceSet,\n  encodeReply,\n  fetch: fetchImplementation = fetch\n}) {\n  const globalVar = window;\n  let landedActionId = 0;\n  return async (id, args) => {\n    let actionId = globalVar.__routerActionID = (globalVar.__routerActionID ?? (globalVar.__routerActionID = 0)) + 1;\n    const temporaryReferences = createTemporaryReferenceSet();\n    const payloadPromise = fetchImplementation(new Request(location.href, {\n      body: await encodeReply(args, {\n        temporaryReferences\n      }),\n      method: \"POST\",\n      headers: {\n        Accept: \"text/x-component\",\n        \"rsc-action-id\": id\n      }\n    })).then(response => {\n      if (!response.body) {\n        throw new Error(\"No response body\");\n      }\n      return createFromReadableStream(response.body, {\n        temporaryReferences\n      });\n    });\n    globalVar.__reactRouterDataRouter.__setPendingRerender(Promise.resolve(payloadPromise).then(async payload => {\n      if (payload.type === \"redirect\") {\n        if (payload.reload) {\n          window.location.href = payload.location;\n          return () => {};\n        }\n        return () => {\n          globalVar.__reactRouterDataRouter.navigate(payload.location, {\n            replace: payload.replace\n          });\n        };\n      }\n      if (payload.type !== \"action\") {\n        throw new Error(\"Unexpected payload type\");\n      }\n      const rerender = await payload.rerender;\n      if (rerender && landedActionId < actionId && globalVar.__routerActionID <= actionId) {\n        if (rerender.type === \"redirect\") {\n          if (rerender.reload) {\n            window.location.href = rerender.location;\n            return;\n          }\n          return () => {\n            globalVar.__reactRouterDataRouter.navigate(rerender.location, {\n              replace: rerender.replace\n            });\n          };\n        }\n        return () => {\n          let lastMatch;\n          for (const match of rerender.matches) {\n            globalVar.__reactRouterDataRouter.patchRoutes(lastMatch?.id ?? null, [createRouteFromServerManifest(match)], true);\n            lastMatch = match;\n          }\n          window.__reactRouterDataRouter._internalSetStateDoNotUseOrYouWillBreakYourApp({\n            loaderData: Object.assign({}, globalVar.__reactRouterDataRouter.state.loaderData, rerender.loaderData),\n            errors: rerender.errors ? Object.assign({}, globalVar.__reactRouterDataRouter.state.errors, rerender.errors) : null\n          });\n        };\n      }\n      return () => {};\n    }).catch(() => {}));\n    return payloadPromise.then(payload => {\n      if (payload.type !== \"action\" && payload.type !== \"redirect\") {\n        throw new Error(\"Unexpected payload type\");\n      }\n      return payload.actionResult;\n    });\n  };\n}\nfunction createRouterFromPayload({\n  fetchImplementation,\n  createFromReadableStream,\n  getContext,\n  payload\n}) {\n  const globalVar = window;\n  if (globalVar.__reactRouterDataRouter && globalVar.__reactRouterRouteModules) return {\n    router: globalVar.__reactRouterDataRouter,\n    routeModules: globalVar.__reactRouterRouteModules\n  };\n  if (payload.type !== \"render\") throw new Error(\"Invalid payload type\");\n  globalVar.__reactRouterRouteModules = globalVar.__reactRouterRouteModules ?? {};\n  populateRSCRouteModules(globalVar.__reactRouterRouteModules, payload.matches);\n  let patches = /* @__PURE__ */new Map();\n  payload.patches?.forEach(patch => {\n    invariant(patch.parentId, \"Invalid patch parentId\");\n    if (!patches.has(patch.parentId)) {\n      patches.set(patch.parentId, []);\n    }\n    patches.get(patch.parentId)?.push(patch);\n  });\n  let routes = payload.matches.reduceRight((previous, match) => {\n    const route = createRouteFromServerManifest(match, payload);\n    if (previous.length > 0) {\n      route.children = previous;\n      let childrenToPatch = patches.get(match.id);\n      if (childrenToPatch) {\n        route.children.push(...childrenToPatch.map(r => createRouteFromServerManifest(r)));\n      }\n    }\n    return [route];\n  }, []);\n  globalVar.__reactRouterDataRouter = createRouter({\n    routes,\n    getContext,\n    basename: payload.basename,\n    history: createBrowserHistory(),\n    hydrationData: getHydrationData({\n      state: {\n        loaderData: payload.loaderData,\n        actionData: payload.actionData,\n        errors: payload.errors\n      },\n      routes,\n      getRouteInfo: routeId => {\n        let match = payload.matches.find(m => m.id === routeId);\n        invariant(match, \"Route not found in payload\");\n        return {\n          clientLoader: match.clientLoader,\n          hasLoader: match.hasLoader,\n          hasHydrateFallback: match.hydrateFallbackElement != null\n        };\n      },\n      location: payload.location,\n      basename: payload.basename,\n      isSpaMode: false\n    }),\n    async patchRoutesOnNavigation({\n      path,\n      signal\n    }) {\n      if (discoveredPaths.has(path)) {\n        return;\n      }\n      await fetchAndApplyManifestPatches([path], createFromReadableStream, fetchImplementation, signal);\n    },\n    // FIXME: Pass `build.ssr` into this function\n    dataStrategy: getRSCSingleFetchDataStrategy(() => globalVar.__reactRouterDataRouter, true, payload.basename, createFromReadableStream, fetchImplementation)\n  });\n  if (globalVar.__reactRouterDataRouter.state.initialized) {\n    globalVar.__routerInitialized = true;\n    globalVar.__reactRouterDataRouter.initialize();\n  } else {\n    globalVar.__routerInitialized = false;\n  }\n  let lastLoaderData = void 0;\n  globalVar.__reactRouterDataRouter.subscribe(({\n    loaderData,\n    actionData\n  }) => {\n    if (lastLoaderData !== loaderData) {\n      globalVar.__routerActionID = (globalVar.__routerActionID ?? (globalVar.__routerActionID = 0)) + 1;\n    }\n  });\n  globalVar.__reactRouterDataRouter._updateRoutesForHMR = routeUpdateByRouteId => {\n    const oldRoutes = window.__reactRouterDataRouter.routes;\n    const newRoutes = [];\n    function walkRoutes(routes2, parentId) {\n      return routes2.map(route => {\n        const routeUpdate = routeUpdateByRouteId.get(route.id);\n        if (routeUpdate) {\n          const {\n            routeModule,\n            hasAction,\n            hasComponent,\n            hasErrorBoundary,\n            hasLoader\n          } = routeUpdate;\n          const newRoute = createRouteFromServerManifest({\n            clientAction: routeModule.clientAction,\n            clientLoader: routeModule.clientLoader,\n            element: route.element,\n            errorElement: route.errorElement,\n            handle: route.handle,\n            hasAction,\n            hasComponent,\n            hasErrorBoundary,\n            hasLoader,\n            hydrateFallbackElement: route.hydrateFallbackElement,\n            id: route.id,\n            index: route.index,\n            links: routeModule.links,\n            meta: routeModule.meta,\n            parentId,\n            path: route.path,\n            shouldRevalidate: routeModule.shouldRevalidate\n          });\n          if (route.children) {\n            newRoute.children = walkRoutes(route.children, route.id);\n          }\n          return newRoute;\n        }\n        const updatedRoute = {\n          ...route\n        };\n        if (route.children) {\n          updatedRoute.children = walkRoutes(route.children, route.id);\n        }\n        return updatedRoute;\n      });\n    }\n    newRoutes.push(...walkRoutes(oldRoutes, void 0));\n    window.__reactRouterDataRouter._internalSetRoutes(newRoutes);\n  };\n  return {\n    router: globalVar.__reactRouterDataRouter,\n    routeModules: globalVar.__reactRouterRouteModules\n  };\n}\nvar renderedRoutesContext = createContext();\nfunction getRSCSingleFetchDataStrategy(getRouter, ssr, basename, createFromReadableStream, fetchImplementation) {\n  let dataStrategy = getSingleFetchDataStrategyImpl(getRouter, match => {\n    let M = match;\n    return {\n      hasLoader: M.route.hasLoader,\n      hasClientLoader: M.route.hasClientLoader,\n      hasComponent: M.route.hasComponent,\n      hasAction: M.route.hasAction,\n      hasClientAction: M.route.hasClientAction,\n      hasShouldRevalidate: M.route.hasShouldRevalidate\n    };\n  },\n  // pass map into fetchAndDecode so it can add payloads\n  getFetchAndDecodeViaRSC(createFromReadableStream, fetchImplementation), ssr, basename,\n  // If the route has a component but we don't have an element, we need to hit\n  // the server loader flow regardless of whether the client loader calls\n  // `serverLoader` or not, otherwise we'll have nothing to render.\n  match => {\n    let M = match;\n    return M.route.hasComponent && !M.route.element;\n  });\n  return async args => args.runClientMiddleware(async () => {\n    let context = args.context;\n    context.set(renderedRoutesContext, []);\n    let results = await dataStrategy(args);\n    const renderedRoutesById = /* @__PURE__ */new Map();\n    for (const route of context.get(renderedRoutesContext)) {\n      if (!renderedRoutesById.has(route.id)) {\n        renderedRoutesById.set(route.id, []);\n      }\n      renderedRoutesById.get(route.id).push(route);\n    }\n    for (const match of args.matches) {\n      const renderedRoutes = renderedRoutesById.get(match.route.id);\n      if (renderedRoutes) {\n        for (const rendered of renderedRoutes) {\n          window.__reactRouterDataRouter.patchRoutes(rendered.parentId ?? null, [createRouteFromServerManifest(rendered)], true);\n        }\n      }\n    }\n    return results;\n  });\n}\nfunction getFetchAndDecodeViaRSC(createFromReadableStream, fetchImplementation) {\n  return async (args, basename, targetRoutes) => {\n    let {\n      request,\n      context\n    } = args;\n    let url = singleFetchUrl(request.url, basename, \"rsc\");\n    if (request.method === \"GET\") {\n      url = stripIndexParam(url);\n      if (targetRoutes) {\n        url.searchParams.set(\"_routes\", targetRoutes.join(\",\"));\n      }\n    }\n    let res = await fetchImplementation(new Request(url, await createRequestInit(request)));\n    if (res.status >= 400 && !res.headers.has(\"X-Remix-Response\")) {\n      throw new ErrorResponseImpl(res.status, res.statusText, await res.text());\n    }\n    invariant(res.body, \"No response body to decode\");\n    try {\n      const payload = await createFromReadableStream(res.body, {\n        temporaryReferences: void 0\n      });\n      if (payload.type === \"redirect\") {\n        return {\n          status: res.status,\n          data: {\n            redirect: {\n              redirect: payload.location,\n              reload: payload.reload,\n              replace: payload.replace,\n              revalidate: false,\n              status: payload.status\n            }\n          }\n        };\n      }\n      if (payload.type !== \"render\") {\n        throw new Error(\"Unexpected payload type\");\n      }\n      context.get(renderedRoutesContext).push(...payload.matches);\n      let results = {\n        routes: {}\n      };\n      const dataKey = isMutationMethod(request.method) ? \"actionData\" : \"loaderData\";\n      for (let [routeId, data2] of Object.entries(payload[dataKey] || {})) {\n        results.routes[routeId] = {\n          data: data2\n        };\n      }\n      if (payload.errors) {\n        for (let [routeId, error] of Object.entries(payload.errors)) {\n          results.routes[routeId] = {\n            error\n          };\n        }\n      }\n      return {\n        status: res.status,\n        data: results\n      };\n    } catch (e) {\n      throw new Error(\"Unable to decode RSC response\");\n    }\n  };\n}\nfunction RSCHydratedRouter({\n  createFromReadableStream,\n  fetch: fetchImplementation = fetch,\n  payload,\n  routeDiscovery = \"eager\",\n  getContext\n}) {\n  if (payload.type !== \"render\") throw new Error(\"Invalid payload type\");\n  let {\n    router,\n    routeModules\n  } = React4.useMemo(() => createRouterFromPayload({\n    payload,\n    fetchImplementation,\n    getContext,\n    createFromReadableStream\n  }), [createFromReadableStream, payload, fetchImplementation, getContext]);\n  React4.useEffect(() => {\n    setIsHydrated();\n  }, []);\n  React4.useLayoutEffect(() => {\n    const globalVar = window;\n    if (!globalVar.__routerInitialized) {\n      globalVar.__routerInitialized = true;\n      globalVar.__reactRouterDataRouter.initialize();\n    }\n  }, []);\n  let [location2, setLocation] = React4.useState(router.state.location);\n  React4.useLayoutEffect(() => router.subscribe(newState => {\n    if (newState.location !== location2) {\n      setLocation(newState.location);\n    }\n  }), [router, location2]);\n  React4.useEffect(() => {\n    if (routeDiscovery === \"lazy\" ||\n    // @ts-expect-error - TS doesn't know about this yet\n    window.navigator?.connection?.saveData === true) {\n      return;\n    }\n    function registerElement(el) {\n      let path = el.tagName === \"FORM\" ? el.getAttribute(\"action\") : el.getAttribute(\"href\");\n      if (!path) {\n        return;\n      }\n      let pathname = el.tagName === \"A\" ? el.pathname : new URL(path, window.location.origin).pathname;\n      if (!discoveredPaths.has(pathname)) {\n        nextPaths.add(pathname);\n      }\n    }\n    async function fetchPatches() {\n      document.querySelectorAll(\"a[data-discover], form[data-discover]\").forEach(registerElement);\n      let paths = Array.from(nextPaths.keys()).filter(path => {\n        if (discoveredPaths.has(path)) {\n          nextPaths.delete(path);\n          return false;\n        }\n        return true;\n      });\n      if (paths.length === 0) {\n        return;\n      }\n      try {\n        await fetchAndApplyManifestPatches(paths, createFromReadableStream, fetchImplementation);\n      } catch (e) {\n        console.error(\"Failed to fetch manifest patches\", e);\n      }\n    }\n    let debouncedFetchPatches = debounce(fetchPatches, 100);\n    fetchPatches();\n    let observer = new MutationObserver(() => debouncedFetchPatches());\n    observer.observe(document.documentElement, {\n      subtree: true,\n      childList: true,\n      attributes: true,\n      attributeFilter: [\"data-discover\", \"href\", \"action\"]\n    });\n  }, [routeDiscovery, createFromReadableStream, fetchImplementation]);\n  const frameworkContext = {\n    future: {\n      // These flags have no runtime impact so can always be false.  If we add\n      // flags that drive runtime behavior they'll need to be proxied through.\n      v8_middleware: false,\n      unstable_subResourceIntegrity: false\n    },\n    isSpaMode: false,\n    ssr: true,\n    criticalCss: \"\",\n    manifest: {\n      routes: {},\n      version: \"1\",\n      url: \"\",\n      entry: {\n        module: \"\",\n        imports: []\n      }\n    },\n    routeDiscovery: {\n      mode: \"lazy\",\n      manifestPath: \"/__manifest\"\n    },\n    routeModules\n  };\n  return /* @__PURE__ */React4.createElement(RSCRouterContext.Provider, {\n    value: true\n  }, /* @__PURE__ */React4.createElement(RSCRouterGlobalErrorBoundary, {\n    location: location2\n  }, /* @__PURE__ */React4.createElement(FrameworkContext.Provider, {\n    value: frameworkContext\n  }, /* @__PURE__ */React4.createElement(UNSTABLE_TransitionEnabledRouterProvider, {\n    router,\n    flushSync: ReactDOM.flushSync\n  }))));\n}\nfunction createRouteFromServerManifest(match, payload) {\n  let hasInitialData = payload && match.id in payload.loaderData;\n  let initialData = payload?.loaderData[match.id];\n  let hasInitialError = payload?.errors && match.id in payload.errors;\n  let initialError = payload?.errors?.[match.id];\n  let isHydrationRequest = match.clientLoader?.hydrate === true || !match.hasLoader ||\n  // If the route has a component but we don't have an element, we need to hit\n  // the server loader flow regardless of whether the client loader calls\n  // `serverLoader` or not, otherwise we'll have nothing to render.\n  match.hasComponent && !match.element;\n  invariant(window.__reactRouterRouteModules);\n  populateRSCRouteModules(window.__reactRouterRouteModules, match);\n  let dataRoute = {\n    id: match.id,\n    element: match.element,\n    errorElement: match.errorElement,\n    handle: match.handle,\n    hasErrorBoundary: match.hasErrorBoundary,\n    hydrateFallbackElement: match.hydrateFallbackElement,\n    index: match.index,\n    loader: match.clientLoader ? async (args, singleFetch) => {\n      try {\n        let result = await match.clientLoader({\n          ...args,\n          serverLoader: () => {\n            preventInvalidServerHandlerCall(\"loader\", match.id, match.hasLoader);\n            if (isHydrationRequest) {\n              if (hasInitialData) {\n                return initialData;\n              }\n              if (hasInitialError) {\n                throw initialError;\n              }\n            }\n            return callSingleFetch(singleFetch);\n          }\n        });\n        return result;\n      } finally {\n        isHydrationRequest = false;\n      }\n    } :\n    // We always make the call in this RSC world since even if we don't\n    // have a `loader` we may need to get the `element` implementation\n    (_, singleFetch) => callSingleFetch(singleFetch),\n    action: match.clientAction ? (args, singleFetch) => match.clientAction({\n      ...args,\n      serverAction: async () => {\n        preventInvalidServerHandlerCall(\"action\", match.id, match.hasLoader);\n        return await callSingleFetch(singleFetch);\n      }\n    }) : match.hasAction ? (_, singleFetch) => callSingleFetch(singleFetch) : () => {\n      throw noActionDefinedError(\"action\", match.id);\n    },\n    path: match.path,\n    shouldRevalidate: match.shouldRevalidate,\n    // We always have a \"loader\" in this RSC world since even if we don't\n    // have a `loader` we may need to get the `element` implementation\n    hasLoader: true,\n    hasClientLoader: match.clientLoader != null,\n    hasAction: match.hasAction,\n    hasClientAction: match.clientAction != null,\n    hasShouldRevalidate: match.shouldRevalidate != null\n  };\n  if (typeof dataRoute.loader === \"function\") {\n    dataRoute.loader.hydrate = shouldHydrateRouteLoader(match.id, match.clientLoader, match.hasLoader, false);\n  }\n  return dataRoute;\n}\nfunction callSingleFetch(singleFetch) {\n  invariant(typeof singleFetch === \"function\", \"Invalid singleFetch parameter\");\n  return singleFetch();\n}\nfunction preventInvalidServerHandlerCall(type, routeId, hasHandler) {\n  if (!hasHandler) {\n    let fn = type === \"action\" ? \"serverAction()\" : \"serverLoader()\";\n    let msg = `You are trying to call ${fn} on a route that does not have a server ${type} (routeId: \"${routeId}\")`;\n    console.error(msg);\n    throw new ErrorResponseImpl(400, \"Bad Request\", new Error(msg), true);\n  }\n}\nvar nextPaths = /* @__PURE__ */new Set();\nvar discoveredPathsMaxSize = 1e3;\nvar discoveredPaths = /* @__PURE__ */new Set();\nvar URL_LIMIT = 7680;\nfunction getManifestUrl(paths) {\n  if (paths.length === 0) {\n    return null;\n  }\n  if (paths.length === 1) {\n    return new URL(`${paths[0]}.manifest`, window.location.origin);\n  }\n  const globalVar = window;\n  let basename = (globalVar.__reactRouterDataRouter.basename ?? \"\").replace(/^\\/|\\/$/g, \"\");\n  let url = new URL(`${basename}/.manifest`, window.location.origin);\n  url.searchParams.set(\"paths\", paths.sort().join(\",\"));\n  return url;\n}\nasync function fetchAndApplyManifestPatches(paths, createFromReadableStream, fetchImplementation, signal) {\n  let url = getManifestUrl(paths);\n  if (url == null) {\n    return;\n  }\n  if (url.toString().length > URL_LIMIT) {\n    nextPaths.clear();\n    return;\n  }\n  let response = await fetchImplementation(new Request(url, {\n    signal\n  }));\n  if (!response.body || response.status < 200 || response.status >= 300) {\n    throw new Error(\"Unable to fetch new route matches from the server\");\n  }\n  let payload = await createFromReadableStream(response.body, {\n    temporaryReferences: void 0\n  });\n  if (payload.type !== \"manifest\") {\n    throw new Error(\"Failed to patch routes\");\n  }\n  paths.forEach(p => addToFifoQueue(p, discoveredPaths));\n  payload.patches.forEach(p => {\n    window.__reactRouterDataRouter.patchRoutes(p.parentId ?? null, [createRouteFromServerManifest(p)]);\n  });\n}\nfunction addToFifoQueue(path, queue) {\n  if (queue.size >= discoveredPathsMaxSize) {\n    let first = queue.values().next().value;\n    queue.delete(first);\n  }\n  queue.add(path);\n}\nfunction debounce(callback, wait) {\n  let timeoutId;\n  return (...args) => {\n    window.clearTimeout(timeoutId);\n    timeoutId = window.setTimeout(() => callback(...args), wait);\n  };\n}\n\n// lib/rsc/server.ssr.tsx\nimport * as React5 from \"react\";\n\n// lib/rsc/html-stream/server.ts\nvar encoder2 = new TextEncoder();\nvar trailer = \"</body></html>\";\nfunction injectRSCPayload(rscStream) {\n  let decoder = new TextDecoder();\n  let resolveFlightDataPromise;\n  let flightDataPromise = new Promise(resolve => resolveFlightDataPromise = resolve);\n  let startedRSC = false;\n  let buffered = [];\n  let timeout = null;\n  function flushBufferedChunks(controller) {\n    for (let chunk of buffered) {\n      let buf = decoder.decode(chunk, {\n        stream: true\n      });\n      if (buf.endsWith(trailer)) {\n        buf = buf.slice(0, -trailer.length);\n      }\n      controller.enqueue(encoder2.encode(buf));\n    }\n    buffered.length = 0;\n    timeout = null;\n  }\n  return new TransformStream({\n    transform(chunk, controller) {\n      buffered.push(chunk);\n      if (timeout) {\n        return;\n      }\n      timeout = setTimeout(async () => {\n        flushBufferedChunks(controller);\n        if (!startedRSC) {\n          startedRSC = true;\n          writeRSCStream(rscStream, controller).catch(err => controller.error(err)).then(resolveFlightDataPromise);\n        }\n      }, 0);\n    },\n    async flush(controller) {\n      await flightDataPromise;\n      if (timeout) {\n        clearTimeout(timeout);\n        flushBufferedChunks(controller);\n      }\n      controller.enqueue(encoder2.encode(\"</body></html>\"));\n    }\n  });\n}\nasync function writeRSCStream(rscStream, controller) {\n  let decoder = new TextDecoder(\"utf-8\", {\n    fatal: true\n  });\n  const reader = rscStream.getReader();\n  try {\n    let read;\n    while ((read = await reader.read()) && !read.done) {\n      const chunk = read.value;\n      try {\n        writeChunk(JSON.stringify(decoder.decode(chunk, {\n          stream: true\n        })), controller);\n      } catch (err) {\n        let base64 = JSON.stringify(btoa(String.fromCodePoint(...chunk)));\n        writeChunk(`Uint8Array.from(atob(${base64}), m => m.codePointAt(0))`, controller);\n      }\n    }\n  } finally {\n    reader.releaseLock();\n  }\n  let remaining = decoder.decode();\n  if (remaining.length) {\n    writeChunk(JSON.stringify(remaining), controller);\n  }\n}\nfunction writeChunk(chunk, controller) {\n  controller.enqueue(encoder2.encode(`<script>${escapeScript(`(self.__FLIGHT_DATA||=[]).push(${chunk})`)}</script>`));\n}\nfunction escapeScript(script) {\n  return script.replace(/<!--/g, \"<\\\\!--\").replace(/<\\/(script)/gi, \"</\\\\$1\");\n}\n\n// lib/rsc/server.ssr.tsx\nvar REACT_USE = \"use\";\nvar useImpl = React5[REACT_USE];\nfunction useSafe(promise) {\n  if (useImpl) {\n    return useImpl(promise);\n  }\n  throw new Error(\"React Router v7 requires React 19+ for RSC features.\");\n}\nasync function routeRSCServerRequest({\n  request,\n  fetchServer,\n  createFromReadableStream,\n  renderHTML,\n  hydrate = true\n}) {\n  const url = new URL(request.url);\n  const isDataRequest = isReactServerRequest(url);\n  const respondWithRSCPayload = isDataRequest || isManifestRequest(url) || request.headers.has(\"rsc-action-id\");\n  const serverResponse = await fetchServer(request);\n  if (respondWithRSCPayload || serverResponse.headers.get(\"React-Router-Resource\") === \"true\") {\n    return serverResponse;\n  }\n  if (!serverResponse.body) {\n    throw new Error(\"Missing body in server response\");\n  }\n  const detectRedirectResponse = serverResponse.clone();\n  let serverResponseB = null;\n  if (hydrate) {\n    serverResponseB = serverResponse.clone();\n  }\n  const body = serverResponse.body;\n  let buffer;\n  let streamControllers = [];\n  const createStream = () => {\n    if (!buffer) {\n      buffer = [];\n      return body.pipeThrough(new TransformStream({\n        transform(chunk, controller) {\n          buffer.push(chunk);\n          controller.enqueue(chunk);\n          streamControllers.forEach(c => c.enqueue(chunk));\n        },\n        flush() {\n          streamControllers.forEach(c => c.close());\n          streamControllers = [];\n        }\n      }));\n    }\n    return new ReadableStream({\n      start(controller) {\n        buffer.forEach(chunk => controller.enqueue(chunk));\n        streamControllers.push(controller);\n      }\n    });\n  };\n  let deepestRenderedBoundaryId = null;\n  const getPayload = () => {\n    const payloadPromise = Promise.resolve(createFromReadableStream(createStream()));\n    return Object.defineProperties(payloadPromise, {\n      _deepestRenderedBoundaryId: {\n        get() {\n          return deepestRenderedBoundaryId;\n        },\n        set(boundaryId) {\n          deepestRenderedBoundaryId = boundaryId;\n        }\n      },\n      formState: {\n        get() {\n          return payloadPromise.then(payload => payload.type === \"render\" ? payload.formState : void 0);\n        }\n      }\n    });\n  };\n  try {\n    if (!detectRedirectResponse.body) {\n      throw new Error(\"Failed to clone server response\");\n    }\n    const payload = await createFromReadableStream(detectRedirectResponse.body);\n    if (serverResponse.status === SINGLE_FETCH_REDIRECT_STATUS && payload.type === \"redirect\") {\n      const headers2 = new Headers(serverResponse.headers);\n      headers2.delete(\"Content-Encoding\");\n      headers2.delete(\"Content-Length\");\n      headers2.delete(\"Content-Type\");\n      headers2.delete(\"X-Remix-Response\");\n      headers2.set(\"Location\", payload.location);\n      return new Response(serverResponseB?.body || \"\", {\n        headers: headers2,\n        status: payload.status,\n        statusText: serverResponse.statusText\n      });\n    }\n    const html = await renderHTML(getPayload);\n    const headers = new Headers(serverResponse.headers);\n    headers.set(\"Content-Type\", \"text/html; charset=utf-8\");\n    if (!hydrate) {\n      return new Response(html, {\n        status: serverResponse.status,\n        headers\n      });\n    }\n    if (!serverResponseB?.body) {\n      throw new Error(\"Failed to clone server response\");\n    }\n    const body2 = html.pipeThrough(injectRSCPayload(serverResponseB.body));\n    return new Response(body2, {\n      status: serverResponse.status,\n      headers\n    });\n  } catch (reason) {\n    if (reason instanceof Response) {\n      return reason;\n    }\n    try {\n      const status = isRouteErrorResponse(reason) ? reason.status : 500;\n      const html = await renderHTML(() => {\n        const decoded = Promise.resolve(createFromReadableStream(createStream()));\n        const payloadPromise = decoded.then(payload => Object.assign(payload, {\n          status,\n          errors: deepestRenderedBoundaryId ? {\n            [deepestRenderedBoundaryId]: reason\n          } : {}\n        }));\n        return Object.defineProperties(payloadPromise, {\n          _deepestRenderedBoundaryId: {\n            get() {\n              return deepestRenderedBoundaryId;\n            },\n            set(boundaryId) {\n              deepestRenderedBoundaryId = boundaryId;\n            }\n          },\n          formState: {\n            get() {\n              return payloadPromise.then(payload => payload.type === \"render\" ? payload.formState : void 0);\n            }\n          }\n        });\n      });\n      const headers = new Headers(serverResponse.headers);\n      headers.set(\"Content-Type\", \"text/html\");\n      if (!hydrate) {\n        return new Response(html, {\n          status,\n          headers\n        });\n      }\n      if (!serverResponseB?.body) {\n        throw new Error(\"Failed to clone server response\");\n      }\n      const body2 = html.pipeThrough(injectRSCPayload(serverResponseB.body));\n      return new Response(body2, {\n        status,\n        headers\n      });\n    } catch {}\n    throw reason;\n  }\n}\nfunction RSCStaticRouter({\n  getPayload\n}) {\n  const decoded = getPayload();\n  const payload = useSafe(decoded);\n  if (payload.type === \"redirect\") {\n    throw new Response(null, {\n      status: payload.status,\n      headers: {\n        Location: payload.location\n      }\n    });\n  }\n  if (payload.type !== \"render\") return null;\n  let patchedLoaderData = {\n    ...payload.loaderData\n  };\n  for (const match of payload.matches) {\n    if (shouldHydrateRouteLoader(match.id, match.clientLoader, match.hasLoader, false) && (match.hydrateFallbackElement || !match.hasLoader)) {\n      delete patchedLoaderData[match.id];\n    }\n  }\n  const context = {\n    get _deepestRenderedBoundaryId() {\n      return decoded._deepestRenderedBoundaryId ?? null;\n    },\n    set _deepestRenderedBoundaryId(boundaryId) {\n      decoded._deepestRenderedBoundaryId = boundaryId;\n    },\n    actionData: payload.actionData,\n    actionHeaders: {},\n    basename: payload.basename,\n    errors: payload.errors,\n    loaderData: patchedLoaderData,\n    loaderHeaders: {},\n    location: payload.location,\n    statusCode: 200,\n    matches: payload.matches.map(match => ({\n      params: match.params,\n      pathname: match.pathname,\n      pathnameBase: match.pathnameBase,\n      route: {\n        id: match.id,\n        action: match.hasAction || !!match.clientAction,\n        handle: match.handle,\n        hasErrorBoundary: match.hasErrorBoundary,\n        loader: match.hasLoader || !!match.clientLoader,\n        index: match.index,\n        path: match.path,\n        shouldRevalidate: match.shouldRevalidate\n      }\n    }))\n  };\n  const router = createStaticRouter(payload.matches.reduceRight((previous, match) => {\n    const route = {\n      id: match.id,\n      action: match.hasAction || !!match.clientAction,\n      element: match.element,\n      errorElement: match.errorElement,\n      handle: match.handle,\n      hasErrorBoundary: !!match.errorElement,\n      hydrateFallbackElement: match.hydrateFallbackElement,\n      index: match.index,\n      loader: match.hasLoader || !!match.clientLoader,\n      path: match.path,\n      shouldRevalidate: match.shouldRevalidate\n    };\n    if (previous.length > 0) {\n      route.children = previous;\n    }\n    return [route];\n  }, []), context);\n  const frameworkContext = {\n    future: {\n      // These flags have no runtime impact so can always be false.  If we add\n      // flags that drive runtime behavior they'll need to be proxied through.\n      v8_middleware: false,\n      unstable_subResourceIntegrity: false\n    },\n    isSpaMode: false,\n    ssr: true,\n    criticalCss: \"\",\n    manifest: {\n      routes: {},\n      version: \"1\",\n      url: \"\",\n      entry: {\n        module: \"\",\n        imports: []\n      }\n    },\n    routeDiscovery: {\n      mode: \"lazy\",\n      manifestPath: \"/__manifest\"\n    },\n    routeModules: createRSCRouteModules(payload)\n  };\n  return /* @__PURE__ */React5.createElement(RSCRouterContext.Provider, {\n    value: true\n  }, /* @__PURE__ */React5.createElement(RSCRouterGlobalErrorBoundary, {\n    location: payload.location\n  }, /* @__PURE__ */React5.createElement(FrameworkContext.Provider, {\n    value: frameworkContext\n  }, /* @__PURE__ */React5.createElement(StaticRouterProvider, {\n    context,\n    router,\n    hydrate: false,\n    nonce: payload.nonce\n  }))));\n}\nfunction isReactServerRequest(url) {\n  return url.pathname.endsWith(\".rsc\");\n}\nfunction isManifestRequest(url) {\n  return url.pathname.endsWith(\".manifest\");\n}\n\n// lib/rsc/html-stream/browser.ts\nfunction getRSCStream() {\n  let encoder3 = new TextEncoder();\n  let streamController = null;\n  let rscStream = new ReadableStream({\n    start(controller) {\n      if (typeof window === \"undefined\") {\n        return;\n      }\n      let handleChunk = chunk => {\n        if (typeof chunk === \"string\") {\n          controller.enqueue(encoder3.encode(chunk));\n        } else {\n          controller.enqueue(chunk);\n        }\n      };\n      window.__FLIGHT_DATA || (window.__FLIGHT_DATA = []);\n      window.__FLIGHT_DATA.forEach(handleChunk);\n      window.__FLIGHT_DATA.push = chunk => {\n        handleChunk(chunk);\n        return 0;\n      };\n      streamController = controller;\n    }\n  });\n  if (typeof document !== \"undefined\" && document.readyState === \"loading\") {\n    document.addEventListener(\"DOMContentLoaded\", () => {\n      streamController?.close();\n    });\n  } else {\n    streamController?.close();\n  }\n  return rscStream;\n}\n\n// lib/dom/ssr/errors.ts\nfunction deserializeErrors(errors) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    if (val && val.__type === \"RouteErrorResponse\") {\n      serialized[key] = new ErrorResponseImpl(val.status, val.statusText, val.data, val.internal === true);\n    } else if (val && val.__type === \"Error\") {\n      if (val.__subType) {\n        let ErrorConstructor = window[val.__subType];\n        if (typeof ErrorConstructor === \"function\") {\n          try {\n            let error = new ErrorConstructor(val.message);\n            error.stack = val.stack;\n            serialized[key] = error;\n          } catch (e) {}\n        }\n      }\n      if (serialized[key] == null) {\n        let error = new Error(val.message);\n        error.stack = val.stack;\n        serialized[key] = error;\n      }\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\nexport { ServerRouter, createRoutesStub, createCookie, isCookie, ServerMode, setDevServerHooks, createRequestHandler, createSession, isSession, createSessionStorage, createCookieSessionStorage, createMemorySessionStorage, href, getHydrationData, RSCDefaultRootErrorBoundary, createCallServer, RSCHydratedRouter, routeRSCServerRequest, RSCStaticRouter, getRSCStream, deserializeErrors };", "map": {"version": 3, "names": ["ENABLE_DEV_WARNINGS", "ErrorResponseImpl", "FrameworkContext", "NO_BODY_STATUS_CODES", "Outlet", "RSCRouterContext", "RemixErrorBoundary", "RouterContextProvider", "RouterProvider", "SINGLE_FETCH_REDIRECT_STATUS", "SingleFetchRedirectSymbol", "StaticRouterProvider", "StreamTransfer", "UNSTABLE_TransitionEnabledRouterProvider", "convertRoutesToDataRoutes", "createBrowserHistory", "createContext", "createMemoryRouter", "createRequestInit", "createRouter", "createServerRoutes", "createStaticHandler", "createStaticRouter", "decodeViaTurboStream", "encode", "escapeHtml", "getManifestPath", "getSingleFetchDataStrategyImpl", "getStaticContextFromError", "invariant", "isDataWithResponseInit", "isMutationMethod", "isRedirectResponse", "isRedirectStatusCode", "isResponse", "isRouteErrorResponse", "matchRoutes", "noActionDefinedError", "redirect", "redirectDocument", "replace", "setIsHydrated", "shouldHydrateRouteLoader", "singleFetchUrl", "stripBasename", "stripIndexParam", "useRouteError", "warnOnce", "withComponentProps", "withErrorBoundaryProps", "withHydrateFallbackProps", "React", "ServerRouter", "context", "url", "nonce", "URL", "manifest", "routeModules", "criticalCss", "serverHandoffString", "routes", "future", "isSpaMode", "staticHandlerContext", "loaderData", "match", "matches", "routeId", "route", "id", "manifestRoute", "clientLoader", "<PERSON><PERSON><PERSON><PERSON>", "HydrateFallback", "router", "createElement", "Fragment", "Provider", "value", "ssr", "routeDiscovery", "serializeError", "renderMeta", "location", "state", "hydrate", "serverHandoffStream", "Suspense", "identifier", "reader", "<PERSON><PERSON><PERSON><PERSON>", "textDecoder", "TextDecoder", "React2", "createRoutesStub", "_context", "RoutesTestStub", "initialEntries", "initialIndex", "hydrationData", "routerRef", "useRef", "frameworkContextRef", "current", "unstable_subResourceIntegrity", "v8_middleware", "entry", "imports", "module", "version", "mode", "manifestPath", "patched", "processRoutes", "r", "parentId", "map", "Error", "newRoute", "path", "index", "Component", "Error<PERSON>ou<PERSON><PERSON>", "action", "args", "loader", "middleware", "mw", "handle", "shouldRevalidate", "entryRoute", "hasAction", "hasClientAction", "hasClientLoader", "hasClientMiddleware", "hasErrorBou<PERSON>ry", "clientActionModule", "clientLoaderModule", "clientMiddlewareModule", "hydrateFallbackModule", "default", "links", "meta", "children", "parse", "serialize", "encoder", "TextEncoder", "sign", "secret", "data2", "key", "create<PERSON><PERSON>", "signature", "crypto", "subtle", "hash", "btoa", "String", "fromCharCode", "Uint8Array", "unsign", "cookie", "lastIndexOf", "slice", "byteStringToUint8Array", "atob", "valid", "verify", "error", "usages", "importKey", "name", "byteString", "array", "length", "i", "charCodeAt", "createCookie", "cookieOptions", "secrets", "options", "sameSite", "warnOnceAboutExpiresCookie", "expires", "isSigned", "maxAge", "Date", "now", "cookieHeader", "parseOptions", "cookies", "decoded", "decodeCookieValue", "serializeOptions", "encodeCookieValue", "<PERSON><PERSON><PERSON><PERSON>", "object", "encoded", "encodeData", "unsignedValue", "decodeData", "myUnescape", "encodeURIComponent", "JSON", "stringify", "decodeURIComponent", "myEscape", "str", "toString", "result", "chr", "code", "char<PERSON>t", "exec", "hex", "toUpperCase", "part", "parseInt", "createEntryRouteModules", "Object", "keys", "reduce", "memo", "ServerMode", "ServerMode2", "isServerMode", "sanitizeError", "serverMode", "sanitized", "stack", "sanitizeErrors", "errors", "entries", "acc", "assign", "message", "serializeErrors", "serialized", "val", "__type", "__subType", "matchServerRoutes", "pathname", "basename", "params", "callRouteHandler", "handler", "request", "stripRoutesParam", "stripIndexParam2", "init", "status", "Response", "indexValues", "searchParams", "getAll", "delete", "indexValuesToKeep", "indexValue", "push", "<PERSON><PERSON><PERSON>", "append", "method", "body", "headers", "signal", "duplex", "Request", "href", "invariant2", "console", "globalDevServerHooksKey", "setDevServerHooks", "devServerHooks", "globalThis", "getDevServerHooks", "getBuildTimeHeader", "headerName", "process", "env", "IS_RR_BUILD_REQUEST", "get", "e", "groupRoutesByParentId", "values", "for<PERSON>ach", "createRoutes", "routesByParentId", "createStaticHandlerDataRoutes", "commonRoute", "preRenderedData", "decodeURI", "uint8array", "stream", "ReadableStream", "start", "controller", "enqueue", "close", "global", "reload", "data", "caseSensitive", "createServerHandoffString", "serverHandoff", "splitCookiesString", "getDocumentHeaders", "build", "getDocumentHeadersImpl", "m", "getRouteHeadersFn", "_defaultHeaders", "boundaryIdx", "findIndex", "errorHeaders", "actionHeaders", "actionData", "loaderHeaders", "some", "hasOwnProperty", "defaultHeaders", "Headers", "parentHeaders", "idx", "includeErrorHeaders", "includeErrorCookies", "headersFn", "headers2", "prependCookies", "childHeaders", "parentSetCookieString", "childCookies", "Set", "getSetCookie", "has", "SERVER_NO_BODY_STATUS_CODES", "singleFetchAction", "static<PERSON><PERSON><PERSON>", "handlerUrl", "loadContext", "handleError", "handlerRequest", "query", "requestContext", "skipL<PERSON>derError<PERSON><PERSON>bling", "skipRevalidation", "generateMiddlewareResponse", "innerResult", "handleQueryResult", "handleQueryError", "staticContextToResponse", "generateSingleFetchResponse", "statusCode", "err", "singleFetchResult", "singleFetchLoaders", "routesParam", "loadRouteIds", "split", "filterMatchesToLoad", "results", "loadedMatches", "filter", "resultHeaders", "set", "encodeViaTurboStream", "streamTimeout", "generateSingleFetchRedirectResponse", "redirectResponse", "redirect2", "getSingleFetchRedirect", "revalidate", "requestSignal", "AbortController", "timeoutId", "setTimeout", "abort", "addEventListener", "clearTimeout", "plugins", "data3", "statusText", "postPlugins", "fromEntries", "derive", "dataRoutes", "<PERSON><PERSON><PERSON><PERSON>", "aborted", "createRequestHandler", "_build", "requestHandler", "initialContext", "derived", "processRequestError", "returnLastResortErrorResponse", "normalizedBasename", "normalizedPath", "endsWith", "decodedPath", "<PERSON><PERSON><PERSON>", "prerender", "includes", "manifestUrl", "res", "handleManifestRequest", "response", "singleFetchMatches", "handleSingleFetchRequest", "handleDataRequest", "handleResourceRequest", "unstable_getCriticalCss", "getCriticalCss", "handleDocumentRequest", "assets", "patches", "paths", "pathParam", "requestedPaths", "Boolean", "startsWith", "segments", "_", "partialPath", "join", "add", "json", "renderHtml", "isSpaMode2", "baseServerHandoff", "entryContext", "handleDocumentRequestFunction", "errorForSecondRender", "unwrapResponse", "state2", "error2", "queryRoute", "handleQueryRouteResult", "handleQueryRouteError", "errorResponseToJson", "newError", "errorResponse", "contentType", "test", "text", "flash", "createSession", "initialData", "Map", "flashName", "unset", "isSession", "createSessionStorage", "cookieArg", "createData", "readData", "updateData", "deleteData", "warnOnceAboutSigningSessionCookie", "getSession", "commitSession", "session", "destroySession", "createCookieSessionStorage", "serializedCookie", "_session", "createMemorySessionStorage", "Math", "random", "substring", "param", "questionMark", "isRequired", "React4", "ReactDOM", "getHydrationData", "getRouteInfo", "location2", "initialMatches", "routeInfo", "hasHydrateFallback", "React3", "RSCRouterGlobalErrorBoundary", "constructor", "props", "getDerivedStateFromError", "getDerivedStateFromProps", "render", "RSCDefaultRootErrorBoundaryImpl", "renderAppShell", "ErrorWrapper", "title", "lang", "charSet", "content", "style", "fontFamily", "padding", "heyDeveloper", "dangerouslySetInnerHTML", "__html", "fontSize", "errorInstance", "errorString", "background", "color", "overflow", "RSCDefaultRootErrorBoundary", "hasRootLayout", "createRSCRouteModules", "payload", "populateRSCRouteModules", "Array", "isArray", "noopComponent", "createCallServer", "createFromReadableStream", "createTemporaryReferenceSet", "encodeReply", "fetch", "fetchImplementation", "globalVar", "window", "landedActionId", "actionId", "__routerActionID", "temporaryReferences", "payloadPromise", "Accept", "then", "__reactRouterDataRouter", "__set<PERSON><PERSON><PERSON><PERSON><PERSON>", "Promise", "resolve", "type", "navigate", "rerender", "lastMatch", "patchRoutes", "createRouteFromServerManifest", "_internalSetStateDoNotUseOrYouWillBreakYourApp", "catch", "actionResult", "createRouterFromPayload", "getContext", "__reactRouterRouteModules", "patch", "reduceRight", "previous", "childrenToPatch", "history", "find", "hydrateFallbackElement", "patchRoutesOnNavigation", "discoveredPaths", "fetchAndApplyManifestPatches", "dataStrategy", "getRSCSingleFetchDataStrategy", "initialized", "__routerInitialized", "initialize", "lastLoaderData", "subscribe", "_updateRoutesForHMR", "routeUpdateByRouteId", "oldRoutes", "newRoutes", "walkRoutes", "routes2", "routeUpdate", "routeModule", "hasComponent", "clientAction", "element", "errorElement", "updatedRoute", "_internalSetRoutes", "renderedRoutesContext", "getRouter", "M", "hasShouldRevalidate", "getFetchAndDecodeViaRSC", "runClientMiddleware", "renderedRoutesById", "renderedRoutes", "rendered", "targetRoutes", "dataKey", "RSCHydratedRouter", "useMemo", "useEffect", "useLayoutEffect", "setLocation", "useState", "newState", "navigator", "connection", "saveData", "registerElement", "el", "tagName", "getAttribute", "origin", "nextPaths", "fetchPatches", "document", "querySelectorAll", "from", "debouncedFetchPatches", "debounce", "observer", "MutationObserver", "observe", "documentElement", "subtree", "childList", "attributes", "attributeFilter", "frameworkContext", "flushSync", "hasInitialData", "hasInitialError", "initialError", "isHydrationRequest", "dataRoute", "singleFetch", "serverLoader", "preventInvalidServerHandlerCall", "callSingleFetch", "serverAction", "<PERSON><PERSON><PERSON><PERSON>", "fn", "msg", "discoveredPathsMaxSize", "URL_LIMIT", "getManifestUrl", "sort", "clear", "p", "addToFifoQueue", "queue", "size", "first", "next", "callback", "wait", "React5", "encoder2", "trailer", "injectRSCPayload", "rscStream", "decoder", "resolveFlightDataPromise", "flightDataPromise", "startedRSC", "buffered", "timeout", "flushBufferedChunks", "chunk", "buf", "decode", "TransformStream", "transform", "writeRSCStream", "flush", "fatal", "read", "done", "writeChunk", "base64", "fromCodePoint", "releaseLock", "remaining", "escapeScript", "script", "REACT_USE", "useImpl", "useSafe", "promise", "routeRSCServerRequest", "fetchServer", "renderHTML", "isDataRequest", "isReactServerRequest", "respondWithRSCPayload", "isManifestRequest", "serverResponse", "detectRedirectResponse", "clone", "serverResponseB", "buffer", "streamControllers", "createStream", "pipeThrough", "c", "deepestRenderedBoundaryId", "getPayload", "defineProperties", "_deepestRenderedBoundaryId", "boundaryId", "formState", "html", "body2", "reason", "RSCStatic<PERSON><PERSON><PERSON>", "Location", "patchedLoaderData", "pathnameBase", "getRSCStream", "encoder3", "streamController", "handleChunk", "__FLIGHT_DATA", "readyState", "deserializeErrors", "internal", "ErrorConstructor"], "sources": ["D:/Technology/00_SpringSecurity_SpringBoot_2025/react-sso-project/react-spa/node_modules/react-router/dist/development/chunk-65XJMMLO.mjs"], "sourcesContent": ["/**\n * react-router v7.9.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport {\n  ENABLE_DEV_WARNINGS,\n  ErrorResponseImpl,\n  FrameworkContext,\n  NO_BODY_STATUS_CODES,\n  Outlet,\n  RSCRouterContext,\n  RemixErrorBoundary,\n  RouterContextProvider,\n  RouterProvider,\n  SINGLE_FETCH_REDIRECT_STATUS,\n  SingleFetchRedirectSymbol,\n  StaticRouterProvider,\n  StreamTransfer,\n  UNSTABLE_TransitionEnabledRouterProvider,\n  convertRoutesToDataRoutes,\n  createBrowserHistory,\n  createContext,\n  createMemoryRouter,\n  createRequestInit,\n  createRouter,\n  createServerRoutes,\n  createStaticHandler,\n  createStaticRouter,\n  decodeViaTurboStream,\n  encode,\n  escapeHtml,\n  getManifestPath,\n  getSingleFetchDataStrategyImpl,\n  getStaticContextFromError,\n  invariant,\n  isDataWithResponseInit,\n  isMutationMethod,\n  isRedirectResponse,\n  isRedirectStatusCode,\n  isResponse,\n  isRouteErrorResponse,\n  matchRoutes,\n  noActionDefinedError,\n  redirect,\n  redirectDocument,\n  replace,\n  setIsHydrated,\n  shouldHydrateRouteLoader,\n  singleFetchUrl,\n  stripBasename,\n  stripIndexParam,\n  useRouteError,\n  warnOnce,\n  withComponentProps,\n  withErrorBoundaryProps,\n  withHydrateFallbackProps\n} from \"./chunk-NISHYRIK.mjs\";\n\n// lib/dom/ssr/server.tsx\nimport * as React from \"react\";\nfunction ServerRouter({\n  context,\n  url,\n  nonce\n}) {\n  if (typeof url === \"string\") {\n    url = new URL(url);\n  }\n  let { manifest, routeModules, criticalCss, serverHandoffString } = context;\n  let routes = createServerRoutes(\n    manifest.routes,\n    routeModules,\n    context.future,\n    context.isSpaMode\n  );\n  context.staticHandlerContext.loaderData = {\n    ...context.staticHandlerContext.loaderData\n  };\n  for (let match of context.staticHandlerContext.matches) {\n    let routeId = match.route.id;\n    let route = routeModules[routeId];\n    let manifestRoute = context.manifest.routes[routeId];\n    if (route && manifestRoute && shouldHydrateRouteLoader(\n      routeId,\n      route.clientLoader,\n      manifestRoute.hasLoader,\n      context.isSpaMode\n    ) && (route.HydrateFallback || !manifestRoute.hasLoader)) {\n      delete context.staticHandlerContext.loaderData[routeId];\n    }\n  }\n  let router = createStaticRouter(routes, context.staticHandlerContext);\n  return /* @__PURE__ */ React.createElement(React.Fragment, null, /* @__PURE__ */ React.createElement(\n    FrameworkContext.Provider,\n    {\n      value: {\n        manifest,\n        routeModules,\n        criticalCss,\n        serverHandoffString,\n        future: context.future,\n        ssr: context.ssr,\n        isSpaMode: context.isSpaMode,\n        routeDiscovery: context.routeDiscovery,\n        serializeError: context.serializeError,\n        renderMeta: context.renderMeta\n      }\n    },\n    /* @__PURE__ */ React.createElement(RemixErrorBoundary, { location: router.state.location }, /* @__PURE__ */ React.createElement(\n      StaticRouterProvider,\n      {\n        router,\n        context: context.staticHandlerContext,\n        hydrate: false\n      }\n    ))\n  ), context.serverHandoffStream ? /* @__PURE__ */ React.createElement(React.Suspense, null, /* @__PURE__ */ React.createElement(\n    StreamTransfer,\n    {\n      context,\n      identifier: 0,\n      reader: context.serverHandoffStream.getReader(),\n      textDecoder: new TextDecoder(),\n      nonce\n    }\n  )) : null);\n}\n\n// lib/dom/ssr/routes-test-stub.tsx\nimport * as React2 from \"react\";\nfunction createRoutesStub(routes, _context) {\n  return function RoutesTestStub({\n    initialEntries,\n    initialIndex,\n    hydrationData,\n    future\n  }) {\n    let routerRef = React2.useRef();\n    let frameworkContextRef = React2.useRef();\n    if (routerRef.current == null) {\n      frameworkContextRef.current = {\n        future: {\n          unstable_subResourceIntegrity: future?.unstable_subResourceIntegrity === true,\n          v8_middleware: future?.v8_middleware === true\n        },\n        manifest: {\n          routes: {},\n          entry: { imports: [], module: \"\" },\n          url: \"\",\n          version: \"\"\n        },\n        routeModules: {},\n        ssr: false,\n        isSpaMode: false,\n        routeDiscovery: { mode: \"lazy\", manifestPath: \"/__manifest\" }\n      };\n      let patched = processRoutes(\n        // @ts-expect-error `StubRouteObject` is stricter about `loader`/`action`\n        // types compared to `AgnosticRouteObject`\n        convertRoutesToDataRoutes(routes, (r) => r),\n        _context !== void 0 ? _context : future?.v8_middleware ? new RouterContextProvider() : {},\n        frameworkContextRef.current.manifest,\n        frameworkContextRef.current.routeModules\n      );\n      routerRef.current = createMemoryRouter(patched, {\n        initialEntries,\n        initialIndex,\n        hydrationData\n      });\n    }\n    return /* @__PURE__ */ React2.createElement(FrameworkContext.Provider, { value: frameworkContextRef.current }, /* @__PURE__ */ React2.createElement(RouterProvider, { router: routerRef.current }));\n  };\n}\nfunction processRoutes(routes, context, manifest, routeModules, parentId) {\n  return routes.map((route) => {\n    if (!route.id) {\n      throw new Error(\n        \"Expected a route.id in react-router processRoutes() function\"\n      );\n    }\n    let newRoute = {\n      id: route.id,\n      path: route.path,\n      index: route.index,\n      Component: route.Component ? withComponentProps(route.Component) : void 0,\n      HydrateFallback: route.HydrateFallback ? withHydrateFallbackProps(route.HydrateFallback) : void 0,\n      ErrorBoundary: route.ErrorBoundary ? withErrorBoundaryProps(route.ErrorBoundary) : void 0,\n      action: route.action ? (args) => route.action({ ...args, context }) : void 0,\n      loader: route.loader ? (args) => route.loader({ ...args, context }) : void 0,\n      middleware: route.middleware ? route.middleware.map(\n        (mw) => (...args) => mw(\n          { ...args[0], context },\n          args[1]\n        )\n      ) : void 0,\n      handle: route.handle,\n      shouldRevalidate: route.shouldRevalidate\n    };\n    let entryRoute = {\n      id: route.id,\n      path: route.path,\n      index: route.index,\n      parentId,\n      hasAction: route.action != null,\n      hasLoader: route.loader != null,\n      // When testing routes, you should be stubbing loader/action/middleware,\n      // not trying to re-implement the full loader/clientLoader/SSR/hydration\n      // flow. That is better tested via E2E tests.\n      hasClientAction: false,\n      hasClientLoader: false,\n      hasClientMiddleware: false,\n      hasErrorBoundary: route.ErrorBoundary != null,\n      // any need for these?\n      module: \"build/stub-path-to-module.js\",\n      clientActionModule: void 0,\n      clientLoaderModule: void 0,\n      clientMiddlewareModule: void 0,\n      hydrateFallbackModule: void 0\n    };\n    manifest.routes[newRoute.id] = entryRoute;\n    routeModules[route.id] = {\n      default: newRoute.Component || Outlet,\n      ErrorBoundary: newRoute.ErrorBoundary || void 0,\n      handle: route.handle,\n      links: route.links,\n      meta: route.meta,\n      shouldRevalidate: route.shouldRevalidate\n    };\n    if (route.children) {\n      newRoute.children = processRoutes(\n        route.children,\n        context,\n        manifest,\n        routeModules,\n        newRoute.id\n      );\n    }\n    return newRoute;\n  });\n}\n\n// lib/server-runtime/cookies.ts\nimport { parse, serialize } from \"cookie\";\n\n// lib/server-runtime/crypto.ts\nvar encoder = /* @__PURE__ */ new TextEncoder();\nvar sign = async (value, secret) => {\n  let data2 = encoder.encode(value);\n  let key = await createKey(secret, [\"sign\"]);\n  let signature = await crypto.subtle.sign(\"HMAC\", key, data2);\n  let hash = btoa(String.fromCharCode(...new Uint8Array(signature))).replace(\n    /=+$/,\n    \"\"\n  );\n  return value + \".\" + hash;\n};\nvar unsign = async (cookie, secret) => {\n  let index = cookie.lastIndexOf(\".\");\n  let value = cookie.slice(0, index);\n  let hash = cookie.slice(index + 1);\n  let data2 = encoder.encode(value);\n  let key = await createKey(secret, [\"verify\"]);\n  try {\n    let signature = byteStringToUint8Array(atob(hash));\n    let valid = await crypto.subtle.verify(\"HMAC\", key, signature, data2);\n    return valid ? value : false;\n  } catch (error) {\n    return false;\n  }\n};\nvar createKey = async (secret, usages) => crypto.subtle.importKey(\n  \"raw\",\n  encoder.encode(secret),\n  { name: \"HMAC\", hash: \"SHA-256\" },\n  false,\n  usages\n);\nfunction byteStringToUint8Array(byteString) {\n  let array = new Uint8Array(byteString.length);\n  for (let i = 0; i < byteString.length; i++) {\n    array[i] = byteString.charCodeAt(i);\n  }\n  return array;\n}\n\n// lib/server-runtime/cookies.ts\nvar createCookie = (name, cookieOptions = {}) => {\n  let { secrets = [], ...options } = {\n    path: \"/\",\n    sameSite: \"lax\",\n    ...cookieOptions\n  };\n  warnOnceAboutExpiresCookie(name, options.expires);\n  return {\n    get name() {\n      return name;\n    },\n    get isSigned() {\n      return secrets.length > 0;\n    },\n    get expires() {\n      return typeof options.maxAge !== \"undefined\" ? new Date(Date.now() + options.maxAge * 1e3) : options.expires;\n    },\n    async parse(cookieHeader, parseOptions) {\n      if (!cookieHeader) return null;\n      let cookies = parse(cookieHeader, { ...options, ...parseOptions });\n      if (name in cookies) {\n        let value = cookies[name];\n        if (typeof value === \"string\" && value !== \"\") {\n          let decoded = await decodeCookieValue(value, secrets);\n          return decoded;\n        } else {\n          return \"\";\n        }\n      } else {\n        return null;\n      }\n    },\n    async serialize(value, serializeOptions) {\n      return serialize(\n        name,\n        value === \"\" ? \"\" : await encodeCookieValue(value, secrets),\n        {\n          ...options,\n          ...serializeOptions\n        }\n      );\n    }\n  };\n};\nvar isCookie = (object) => {\n  return object != null && typeof object.name === \"string\" && typeof object.isSigned === \"boolean\" && typeof object.parse === \"function\" && typeof object.serialize === \"function\";\n};\nasync function encodeCookieValue(value, secrets) {\n  let encoded = encodeData(value);\n  if (secrets.length > 0) {\n    encoded = await sign(encoded, secrets[0]);\n  }\n  return encoded;\n}\nasync function decodeCookieValue(value, secrets) {\n  if (secrets.length > 0) {\n    for (let secret of secrets) {\n      let unsignedValue = await unsign(value, secret);\n      if (unsignedValue !== false) {\n        return decodeData(unsignedValue);\n      }\n    }\n    return null;\n  }\n  return decodeData(value);\n}\nfunction encodeData(value) {\n  return btoa(myUnescape(encodeURIComponent(JSON.stringify(value))));\n}\nfunction decodeData(value) {\n  try {\n    return JSON.parse(decodeURIComponent(myEscape(atob(value))));\n  } catch (error) {\n    return {};\n  }\n}\nfunction myEscape(value) {\n  let str = value.toString();\n  let result = \"\";\n  let index = 0;\n  let chr, code;\n  while (index < str.length) {\n    chr = str.charAt(index++);\n    if (/[\\w*+\\-./@]/.exec(chr)) {\n      result += chr;\n    } else {\n      code = chr.charCodeAt(0);\n      if (code < 256) {\n        result += \"%\" + hex(code, 2);\n      } else {\n        result += \"%u\" + hex(code, 4).toUpperCase();\n      }\n    }\n  }\n  return result;\n}\nfunction hex(code, length) {\n  let result = code.toString(16);\n  while (result.length < length) result = \"0\" + result;\n  return result;\n}\nfunction myUnescape(value) {\n  let str = value.toString();\n  let result = \"\";\n  let index = 0;\n  let chr, part;\n  while (index < str.length) {\n    chr = str.charAt(index++);\n    if (chr === \"%\") {\n      if (str.charAt(index) === \"u\") {\n        part = str.slice(index + 1, index + 5);\n        if (/^[\\da-f]{4}$/i.exec(part)) {\n          result += String.fromCharCode(parseInt(part, 16));\n          index += 5;\n          continue;\n        }\n      } else {\n        part = str.slice(index, index + 2);\n        if (/^[\\da-f]{2}$/i.exec(part)) {\n          result += String.fromCharCode(parseInt(part, 16));\n          index += 2;\n          continue;\n        }\n      }\n    }\n    result += chr;\n  }\n  return result;\n}\nfunction warnOnceAboutExpiresCookie(name, expires) {\n  warnOnce(\n    !expires,\n    `The \"${name}\" cookie has an \"expires\" property set. This will cause the expires value to not be updated when the session is committed. Instead, you should set the expires value when serializing the cookie. You can use \\`commitSession(session, { expires })\\` if using a session storage object, or \\`cookie.serialize(\"value\", { expires })\\` if you're using the cookie directly.`\n  );\n}\n\n// lib/server-runtime/entry.ts\nfunction createEntryRouteModules(manifest) {\n  return Object.keys(manifest).reduce((memo, routeId) => {\n    let route = manifest[routeId];\n    if (route) {\n      memo[routeId] = route.module;\n    }\n    return memo;\n  }, {});\n}\n\n// lib/server-runtime/mode.ts\nvar ServerMode = /* @__PURE__ */ ((ServerMode2) => {\n  ServerMode2[\"Development\"] = \"development\";\n  ServerMode2[\"Production\"] = \"production\";\n  ServerMode2[\"Test\"] = \"test\";\n  return ServerMode2;\n})(ServerMode || {});\nfunction isServerMode(value) {\n  return value === \"development\" /* Development */ || value === \"production\" /* Production */ || value === \"test\" /* Test */;\n}\n\n// lib/server-runtime/errors.ts\nfunction sanitizeError(error, serverMode) {\n  if (error instanceof Error && serverMode !== \"development\" /* Development */) {\n    let sanitized = new Error(\"Unexpected Server Error\");\n    sanitized.stack = void 0;\n    return sanitized;\n  }\n  return error;\n}\nfunction sanitizeErrors(errors, serverMode) {\n  return Object.entries(errors).reduce((acc, [routeId, error]) => {\n    return Object.assign(acc, { [routeId]: sanitizeError(error, serverMode) });\n  }, {});\n}\nfunction serializeError(error, serverMode) {\n  let sanitized = sanitizeError(error, serverMode);\n  return {\n    message: sanitized.message,\n    stack: sanitized.stack\n  };\n}\nfunction serializeErrors(errors, serverMode) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    if (isRouteErrorResponse(val)) {\n      serialized[key] = { ...val, __type: \"RouteErrorResponse\" };\n    } else if (val instanceof Error) {\n      let sanitized = sanitizeError(val, serverMode);\n      serialized[key] = {\n        message: sanitized.message,\n        stack: sanitized.stack,\n        __type: \"Error\",\n        // If this is a subclass (i.e., ReferenceError), send up the type so we\n        // can re-create the same type during hydration.  This will only apply\n        // in dev mode since all production errors are sanitized to normal\n        // Error instances\n        ...sanitized.name !== \"Error\" ? {\n          __subType: sanitized.name\n        } : {}\n      };\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n\n// lib/server-runtime/routeMatching.ts\nfunction matchServerRoutes(routes, pathname, basename) {\n  let matches = matchRoutes(\n    routes,\n    pathname,\n    basename\n  );\n  if (!matches) return null;\n  return matches.map((match) => ({\n    params: match.params,\n    pathname: match.pathname,\n    route: match.route\n  }));\n}\n\n// lib/server-runtime/data.ts\nasync function callRouteHandler(handler, args) {\n  let result = await handler({\n    request: stripRoutesParam(stripIndexParam2(args.request)),\n    params: args.params,\n    context: args.context\n  });\n  if (isDataWithResponseInit(result) && result.init && result.init.status && isRedirectStatusCode(result.init.status)) {\n    throw new Response(null, result.init);\n  }\n  return result;\n}\nfunction stripIndexParam2(request) {\n  let url = new URL(request.url);\n  let indexValues = url.searchParams.getAll(\"index\");\n  url.searchParams.delete(\"index\");\n  let indexValuesToKeep = [];\n  for (let indexValue of indexValues) {\n    if (indexValue) {\n      indexValuesToKeep.push(indexValue);\n    }\n  }\n  for (let toKeep of indexValuesToKeep) {\n    url.searchParams.append(\"index\", toKeep);\n  }\n  let init = {\n    method: request.method,\n    body: request.body,\n    headers: request.headers,\n    signal: request.signal\n  };\n  if (init.body) {\n    init.duplex = \"half\";\n  }\n  return new Request(url.href, init);\n}\nfunction stripRoutesParam(request) {\n  let url = new URL(request.url);\n  url.searchParams.delete(\"_routes\");\n  let init = {\n    method: request.method,\n    body: request.body,\n    headers: request.headers,\n    signal: request.signal\n  };\n  if (init.body) {\n    init.duplex = \"half\";\n  }\n  return new Request(url.href, init);\n}\n\n// lib/server-runtime/invariant.ts\nfunction invariant2(value, message) {\n  if (value === false || value === null || typeof value === \"undefined\") {\n    console.error(\n      \"The following error is a bug in React Router; please open an issue! https://github.com/remix-run/react-router/issues/new/choose\"\n    );\n    throw new Error(message);\n  }\n}\n\n// lib/server-runtime/dev.ts\nvar globalDevServerHooksKey = \"__reactRouterDevServerHooks\";\nfunction setDevServerHooks(devServerHooks) {\n  globalThis[globalDevServerHooksKey] = devServerHooks;\n}\nfunction getDevServerHooks() {\n  return globalThis[globalDevServerHooksKey];\n}\nfunction getBuildTimeHeader(request, headerName) {\n  if (typeof process !== \"undefined\") {\n    try {\n      if (process.env?.IS_RR_BUILD_REQUEST === \"yes\") {\n        return request.headers.get(headerName);\n      }\n    } catch (e) {\n    }\n  }\n  return null;\n}\n\n// lib/server-runtime/routes.ts\nfunction groupRoutesByParentId(manifest) {\n  let routes = {};\n  Object.values(manifest).forEach((route) => {\n    if (route) {\n      let parentId = route.parentId || \"\";\n      if (!routes[parentId]) {\n        routes[parentId] = [];\n      }\n      routes[parentId].push(route);\n    }\n  });\n  return routes;\n}\nfunction createRoutes(manifest, parentId = \"\", routesByParentId = groupRoutesByParentId(manifest)) {\n  return (routesByParentId[parentId] || []).map((route) => ({\n    ...route,\n    children: createRoutes(manifest, route.id, routesByParentId)\n  }));\n}\nfunction createStaticHandlerDataRoutes(manifest, future, parentId = \"\", routesByParentId = groupRoutesByParentId(manifest)) {\n  return (routesByParentId[parentId] || []).map((route) => {\n    let commonRoute = {\n      // Always include root due to default boundaries\n      hasErrorBoundary: route.id === \"root\" || route.module.ErrorBoundary != null,\n      id: route.id,\n      path: route.path,\n      middleware: route.module.middleware,\n      // Need to use RR's version in the param typed here to permit the optional\n      // context even though we know it'll always be provided in remix\n      loader: route.module.loader ? async (args) => {\n        let preRenderedData = getBuildTimeHeader(\n          args.request,\n          \"X-React-Router-Prerender-Data\"\n        );\n        if (preRenderedData != null) {\n          let encoded = preRenderedData ? decodeURI(preRenderedData) : preRenderedData;\n          invariant2(encoded, \"Missing prerendered data for route\");\n          let uint8array = new TextEncoder().encode(encoded);\n          let stream = new ReadableStream({\n            start(controller) {\n              controller.enqueue(uint8array);\n              controller.close();\n            }\n          });\n          let decoded = await decodeViaTurboStream(stream, global);\n          let data2 = decoded.value;\n          if (data2 && SingleFetchRedirectSymbol in data2) {\n            let result = data2[SingleFetchRedirectSymbol];\n            let init = { status: result.status };\n            if (result.reload) {\n              throw redirectDocument(result.redirect, init);\n            } else if (result.replace) {\n              throw replace(result.redirect, init);\n            } else {\n              throw redirect(result.redirect, init);\n            }\n          } else {\n            invariant2(\n              data2 && route.id in data2,\n              \"Unable to decode prerendered data\"\n            );\n            let result = data2[route.id];\n            invariant2(\n              \"data\" in result,\n              \"Unable to process prerendered data\"\n            );\n            return result.data;\n          }\n        }\n        let val = await callRouteHandler(route.module.loader, args);\n        return val;\n      } : void 0,\n      action: route.module.action ? (args) => callRouteHandler(route.module.action, args) : void 0,\n      handle: route.module.handle\n    };\n    return route.index ? {\n      index: true,\n      ...commonRoute\n    } : {\n      caseSensitive: route.caseSensitive,\n      children: createStaticHandlerDataRoutes(\n        manifest,\n        future,\n        route.id,\n        routesByParentId\n      ),\n      ...commonRoute\n    };\n  });\n}\n\n// lib/server-runtime/serverHandoff.ts\nfunction createServerHandoffString(serverHandoff) {\n  return escapeHtml(JSON.stringify(serverHandoff));\n}\n\n// lib/server-runtime/headers.ts\nimport { splitCookiesString } from \"set-cookie-parser\";\nfunction getDocumentHeaders(context, build) {\n  return getDocumentHeadersImpl(context, (m) => {\n    let route = build.routes[m.route.id];\n    invariant2(route, `Route with id \"${m.route.id}\" not found in build`);\n    return route.module.headers;\n  });\n}\nfunction getDocumentHeadersImpl(context, getRouteHeadersFn, _defaultHeaders) {\n  let boundaryIdx = context.errors ? context.matches.findIndex((m) => context.errors[m.route.id]) : -1;\n  let matches = boundaryIdx >= 0 ? context.matches.slice(0, boundaryIdx + 1) : context.matches;\n  let errorHeaders;\n  if (boundaryIdx >= 0) {\n    let { actionHeaders, actionData, loaderHeaders, loaderData } = context;\n    context.matches.slice(boundaryIdx).some((match) => {\n      let id = match.route.id;\n      if (actionHeaders[id] && (!actionData || !actionData.hasOwnProperty(id))) {\n        errorHeaders = actionHeaders[id];\n      } else if (loaderHeaders[id] && !loaderData.hasOwnProperty(id)) {\n        errorHeaders = loaderHeaders[id];\n      }\n      return errorHeaders != null;\n    });\n  }\n  const defaultHeaders = new Headers(_defaultHeaders);\n  return matches.reduce((parentHeaders, match, idx) => {\n    let { id } = match.route;\n    let loaderHeaders = context.loaderHeaders[id] || new Headers();\n    let actionHeaders = context.actionHeaders[id] || new Headers();\n    let includeErrorHeaders = errorHeaders != null && idx === matches.length - 1;\n    let includeErrorCookies = includeErrorHeaders && errorHeaders !== loaderHeaders && errorHeaders !== actionHeaders;\n    let headersFn = getRouteHeadersFn(match);\n    if (headersFn == null) {\n      let headers2 = new Headers(parentHeaders);\n      if (includeErrorCookies) {\n        prependCookies(errorHeaders, headers2);\n      }\n      prependCookies(actionHeaders, headers2);\n      prependCookies(loaderHeaders, headers2);\n      return headers2;\n    }\n    let headers = new Headers(\n      typeof headersFn === \"function\" ? headersFn({\n        loaderHeaders,\n        parentHeaders,\n        actionHeaders,\n        errorHeaders: includeErrorHeaders ? errorHeaders : void 0\n      }) : headersFn\n    );\n    if (includeErrorCookies) {\n      prependCookies(errorHeaders, headers);\n    }\n    prependCookies(actionHeaders, headers);\n    prependCookies(loaderHeaders, headers);\n    prependCookies(parentHeaders, headers);\n    return headers;\n  }, new Headers(defaultHeaders));\n}\nfunction prependCookies(parentHeaders, childHeaders) {\n  let parentSetCookieString = parentHeaders.get(\"Set-Cookie\");\n  if (parentSetCookieString) {\n    let cookies = splitCookiesString(parentSetCookieString);\n    let childCookies = new Set(childHeaders.getSetCookie());\n    cookies.forEach((cookie) => {\n      if (!childCookies.has(cookie)) {\n        childHeaders.append(\"Set-Cookie\", cookie);\n      }\n    });\n  }\n}\n\n// lib/server-runtime/single-fetch.ts\nvar SERVER_NO_BODY_STATUS_CODES = /* @__PURE__ */ new Set([\n  ...NO_BODY_STATUS_CODES,\n  304\n]);\nasync function singleFetchAction(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) {\n  try {\n    let handlerRequest = new Request(handlerUrl, {\n      method: request.method,\n      body: request.body,\n      headers: request.headers,\n      signal: request.signal,\n      ...request.body ? { duplex: \"half\" } : void 0\n    });\n    let result = await staticHandler.query(handlerRequest, {\n      requestContext: loadContext,\n      skipLoaderErrorBubbling: true,\n      skipRevalidation: true,\n      generateMiddlewareResponse: build.future.v8_middleware ? async (query) => {\n        try {\n          let innerResult = await query(handlerRequest);\n          return handleQueryResult(innerResult);\n        } catch (error) {\n          return handleQueryError(error);\n        }\n      } : void 0\n    });\n    return handleQueryResult(result);\n  } catch (error) {\n    return handleQueryError(error);\n  }\n  function handleQueryResult(result) {\n    return isResponse(result) ? result : staticContextToResponse(result);\n  }\n  function handleQueryError(error) {\n    handleError(error);\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: { error },\n      headers: new Headers(),\n      status: 500\n    });\n  }\n  function staticContextToResponse(context) {\n    let headers = getDocumentHeaders(context, build);\n    if (isRedirectStatusCode(context.statusCode) && headers.has(\"Location\")) {\n      return new Response(null, { status: context.statusCode, headers });\n    }\n    if (context.errors) {\n      Object.values(context.errors).forEach((err) => {\n        if (!isRouteErrorResponse(err) || err.error) {\n          handleError(err);\n        }\n      });\n      context.errors = sanitizeErrors(context.errors, serverMode);\n    }\n    let singleFetchResult;\n    if (context.errors) {\n      singleFetchResult = { error: Object.values(context.errors)[0] };\n    } else {\n      singleFetchResult = {\n        data: Object.values(context.actionData || {})[0]\n      };\n    }\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: singleFetchResult,\n      headers,\n      status: context.statusCode\n    });\n  }\n}\nasync function singleFetchLoaders(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) {\n  let routesParam = new URL(request.url).searchParams.get(\"_routes\");\n  let loadRouteIds = routesParam ? new Set(routesParam.split(\",\")) : null;\n  try {\n    let handlerRequest = new Request(handlerUrl, {\n      headers: request.headers,\n      signal: request.signal\n    });\n    let result = await staticHandler.query(handlerRequest, {\n      requestContext: loadContext,\n      filterMatchesToLoad: (m) => !loadRouteIds || loadRouteIds.has(m.route.id),\n      skipLoaderErrorBubbling: true,\n      generateMiddlewareResponse: build.future.v8_middleware ? async (query) => {\n        try {\n          let innerResult = await query(handlerRequest);\n          return handleQueryResult(innerResult);\n        } catch (error) {\n          return handleQueryError(error);\n        }\n      } : void 0\n    });\n    return handleQueryResult(result);\n  } catch (error) {\n    return handleQueryError(error);\n  }\n  function handleQueryResult(result) {\n    return isResponse(result) ? result : staticContextToResponse(result);\n  }\n  function handleQueryError(error) {\n    handleError(error);\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: { error },\n      headers: new Headers(),\n      status: 500\n    });\n  }\n  function staticContextToResponse(context) {\n    let headers = getDocumentHeaders(context, build);\n    if (isRedirectStatusCode(context.statusCode) && headers.has(\"Location\")) {\n      return new Response(null, { status: context.statusCode, headers });\n    }\n    if (context.errors) {\n      Object.values(context.errors).forEach((err) => {\n        if (!isRouteErrorResponse(err) || err.error) {\n          handleError(err);\n        }\n      });\n      context.errors = sanitizeErrors(context.errors, serverMode);\n    }\n    let results = {};\n    let loadedMatches = new Set(\n      context.matches.filter(\n        (m) => loadRouteIds ? loadRouteIds.has(m.route.id) : m.route.loader != null\n      ).map((m) => m.route.id)\n    );\n    if (context.errors) {\n      for (let [id, error] of Object.entries(context.errors)) {\n        results[id] = { error };\n      }\n    }\n    for (let [id, data2] of Object.entries(context.loaderData)) {\n      if (!(id in results) && loadedMatches.has(id)) {\n        results[id] = { data: data2 };\n      }\n    }\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: results,\n      headers,\n      status: context.statusCode\n    });\n  }\n}\nfunction generateSingleFetchResponse(request, build, serverMode, {\n  result,\n  headers,\n  status\n}) {\n  let resultHeaders = new Headers(headers);\n  resultHeaders.set(\"X-Remix-Response\", \"yes\");\n  if (SERVER_NO_BODY_STATUS_CODES.has(status)) {\n    return new Response(null, { status, headers: resultHeaders });\n  }\n  resultHeaders.set(\"Content-Type\", \"text/x-script\");\n  resultHeaders.delete(\"Content-Length\");\n  return new Response(\n    encodeViaTurboStream(\n      result,\n      request.signal,\n      build.entry.module.streamTimeout,\n      serverMode\n    ),\n    {\n      status: status || 200,\n      headers: resultHeaders\n    }\n  );\n}\nfunction generateSingleFetchRedirectResponse(redirectResponse, request, build, serverMode) {\n  let redirect2 = getSingleFetchRedirect(\n    redirectResponse.status,\n    redirectResponse.headers,\n    build.basename\n  );\n  let headers = new Headers(redirectResponse.headers);\n  headers.delete(\"Location\");\n  headers.set(\"Content-Type\", \"text/x-script\");\n  return generateSingleFetchResponse(request, build, serverMode, {\n    result: request.method === \"GET\" ? { [SingleFetchRedirectSymbol]: redirect2 } : redirect2,\n    headers,\n    status: SINGLE_FETCH_REDIRECT_STATUS\n  });\n}\nfunction getSingleFetchRedirect(status, headers, basename) {\n  let redirect2 = headers.get(\"Location\");\n  if (basename) {\n    redirect2 = stripBasename(redirect2, basename) || redirect2;\n  }\n  return {\n    redirect: redirect2,\n    status,\n    revalidate: (\n      // Technically X-Remix-Revalidate isn't needed here - that was an implementation\n      // detail of ?_data requests as our way to tell the front end to revalidate when\n      // we didn't have a response body to include that information in.\n      // With single fetch, we tell the front end via this revalidate boolean field.\n      // However, we're respecting it for now because it may be something folks have\n      // used in their own responses\n      // TODO(v3): Consider removing or making this official public API\n      headers.has(\"X-Remix-Revalidate\") || headers.has(\"Set-Cookie\")\n    ),\n    reload: headers.has(\"X-Remix-Reload-Document\"),\n    replace: headers.has(\"X-Remix-Replace\")\n  };\n}\nfunction encodeViaTurboStream(data2, requestSignal, streamTimeout, serverMode) {\n  let controller = new AbortController();\n  let timeoutId = setTimeout(\n    () => controller.abort(new Error(\"Server Timeout\")),\n    typeof streamTimeout === \"number\" ? streamTimeout : 4950\n  );\n  requestSignal.addEventListener(\"abort\", () => clearTimeout(timeoutId));\n  return encode(data2, {\n    signal: controller.signal,\n    plugins: [\n      (value) => {\n        if (value instanceof Error) {\n          let { name, message, stack } = serverMode === \"production\" /* Production */ ? sanitizeError(value, serverMode) : value;\n          return [\"SanitizedError\", name, message, stack];\n        }\n        if (value instanceof ErrorResponseImpl) {\n          let { data: data3, status, statusText } = value;\n          return [\"ErrorResponse\", data3, status, statusText];\n        }\n        if (value && typeof value === \"object\" && SingleFetchRedirectSymbol in value) {\n          return [\"SingleFetchRedirect\", value[SingleFetchRedirectSymbol]];\n        }\n      }\n    ],\n    postPlugins: [\n      (value) => {\n        if (!value) return;\n        if (typeof value !== \"object\") return;\n        return [\n          \"SingleFetchClassInstance\",\n          Object.fromEntries(Object.entries(value))\n        ];\n      },\n      () => [\"SingleFetchFallback\"]\n    ]\n  });\n}\n\n// lib/server-runtime/server.ts\nfunction derive(build, mode) {\n  let routes = createRoutes(build.routes);\n  let dataRoutes = createStaticHandlerDataRoutes(build.routes, build.future);\n  let serverMode = isServerMode(mode) ? mode : \"production\" /* Production */;\n  let staticHandler = createStaticHandler(dataRoutes, {\n    basename: build.basename\n  });\n  let errorHandler = build.entry.module.handleError || ((error, { request }) => {\n    if (serverMode !== \"test\" /* Test */ && !request.signal.aborted) {\n      console.error(\n        // @ts-expect-error This is \"private\" from users but intended for internal use\n        isRouteErrorResponse(error) && error.error ? error.error : error\n      );\n    }\n  });\n  return {\n    routes,\n    dataRoutes,\n    serverMode,\n    staticHandler,\n    errorHandler\n  };\n}\nvar createRequestHandler = (build, mode) => {\n  let _build;\n  let routes;\n  let serverMode;\n  let staticHandler;\n  let errorHandler;\n  return async function requestHandler(request, initialContext) {\n    _build = typeof build === \"function\" ? await build() : build;\n    if (typeof build === \"function\") {\n      let derived = derive(_build, mode);\n      routes = derived.routes;\n      serverMode = derived.serverMode;\n      staticHandler = derived.staticHandler;\n      errorHandler = derived.errorHandler;\n    } else if (!routes || !serverMode || !staticHandler || !errorHandler) {\n      let derived = derive(_build, mode);\n      routes = derived.routes;\n      serverMode = derived.serverMode;\n      staticHandler = derived.staticHandler;\n      errorHandler = derived.errorHandler;\n    }\n    let params = {};\n    let loadContext;\n    let handleError = (error) => {\n      if (mode === \"development\" /* Development */) {\n        getDevServerHooks()?.processRequestError?.(error);\n      }\n      errorHandler(error, {\n        context: loadContext,\n        params,\n        request\n      });\n    };\n    if (_build.future.v8_middleware) {\n      if (initialContext && !(initialContext instanceof RouterContextProvider)) {\n        let error = new Error(\n          \"Invalid `context` value provided to `handleRequest`. When middleware is enabled you must return an instance of `RouterContextProvider` from your `getLoadContext` function.\"\n        );\n        handleError(error);\n        return returnLastResortErrorResponse(error, serverMode);\n      }\n      loadContext = initialContext || new RouterContextProvider();\n    } else {\n      loadContext = initialContext || {};\n    }\n    let url = new URL(request.url);\n    let normalizedBasename = _build.basename || \"/\";\n    let normalizedPath = url.pathname;\n    if (stripBasename(normalizedPath, normalizedBasename) === \"/_root.data\") {\n      normalizedPath = normalizedBasename;\n    } else if (normalizedPath.endsWith(\".data\")) {\n      normalizedPath = normalizedPath.replace(/\\.data$/, \"\");\n    }\n    if (stripBasename(normalizedPath, normalizedBasename) !== \"/\" && normalizedPath.endsWith(\"/\")) {\n      normalizedPath = normalizedPath.slice(0, -1);\n    }\n    let isSpaMode = getBuildTimeHeader(request, \"X-React-Router-SPA-Mode\") === \"yes\";\n    if (!_build.ssr) {\n      let decodedPath = decodeURI(normalizedPath);\n      if (normalizedBasename !== \"/\") {\n        let strippedPath = stripBasename(decodedPath, normalizedBasename);\n        if (strippedPath == null) {\n          errorHandler(\n            new ErrorResponseImpl(\n              404,\n              \"Not Found\",\n              `Refusing to prerender the \\`${decodedPath}\\` path because it does not start with the basename \\`${normalizedBasename}\\``\n            ),\n            {\n              context: loadContext,\n              params,\n              request\n            }\n          );\n          return new Response(\"Not Found\", {\n            status: 404,\n            statusText: \"Not Found\"\n          });\n        }\n        decodedPath = strippedPath;\n      }\n      if (_build.prerender.length === 0) {\n        isSpaMode = true;\n      } else if (!_build.prerender.includes(decodedPath) && !_build.prerender.includes(decodedPath + \"/\")) {\n        if (url.pathname.endsWith(\".data\")) {\n          errorHandler(\n            new ErrorResponseImpl(\n              404,\n              \"Not Found\",\n              `Refusing to SSR the path \\`${decodedPath}\\` because \\`ssr:false\\` is set and the path is not included in the \\`prerender\\` config, so in production the path will be a 404.`\n            ),\n            {\n              context: loadContext,\n              params,\n              request\n            }\n          );\n          return new Response(\"Not Found\", {\n            status: 404,\n            statusText: \"Not Found\"\n          });\n        } else {\n          isSpaMode = true;\n        }\n      }\n    }\n    let manifestUrl = getManifestPath(\n      _build.routeDiscovery.manifestPath,\n      normalizedBasename\n    );\n    if (url.pathname === manifestUrl) {\n      try {\n        let res = await handleManifestRequest(_build, routes, url);\n        return res;\n      } catch (e) {\n        handleError(e);\n        return new Response(\"Unknown Server Error\", { status: 500 });\n      }\n    }\n    let matches = matchServerRoutes(routes, normalizedPath, _build.basename);\n    if (matches && matches.length > 0) {\n      Object.assign(params, matches[0].params);\n    }\n    let response;\n    if (url.pathname.endsWith(\".data\")) {\n      let handlerUrl = new URL(request.url);\n      handlerUrl.pathname = normalizedPath;\n      let singleFetchMatches = matchServerRoutes(\n        routes,\n        handlerUrl.pathname,\n        _build.basename\n      );\n      response = await handleSingleFetchRequest(\n        serverMode,\n        _build,\n        staticHandler,\n        request,\n        handlerUrl,\n        loadContext,\n        handleError\n      );\n      if (isRedirectResponse(response)) {\n        response = generateSingleFetchRedirectResponse(\n          response,\n          request,\n          _build,\n          serverMode\n        );\n      }\n      if (_build.entry.module.handleDataRequest) {\n        response = await _build.entry.module.handleDataRequest(response, {\n          context: loadContext,\n          params: singleFetchMatches ? singleFetchMatches[0].params : {},\n          request\n        });\n        if (isRedirectResponse(response)) {\n          response = generateSingleFetchRedirectResponse(\n            response,\n            request,\n            _build,\n            serverMode\n          );\n        }\n      }\n    } else if (!isSpaMode && matches && matches[matches.length - 1].route.module.default == null && matches[matches.length - 1].route.module.ErrorBoundary == null) {\n      response = await handleResourceRequest(\n        serverMode,\n        _build,\n        staticHandler,\n        matches.slice(-1)[0].route.id,\n        request,\n        loadContext,\n        handleError\n      );\n    } else {\n      let { pathname } = url;\n      let criticalCss = void 0;\n      if (_build.unstable_getCriticalCss) {\n        criticalCss = await _build.unstable_getCriticalCss({ pathname });\n      } else if (mode === \"development\" /* Development */ && getDevServerHooks()?.getCriticalCss) {\n        criticalCss = await getDevServerHooks()?.getCriticalCss?.(pathname);\n      }\n      response = await handleDocumentRequest(\n        serverMode,\n        _build,\n        staticHandler,\n        request,\n        loadContext,\n        handleError,\n        isSpaMode,\n        criticalCss\n      );\n    }\n    if (request.method === \"HEAD\") {\n      return new Response(null, {\n        headers: response.headers,\n        status: response.status,\n        statusText: response.statusText\n      });\n    }\n    return response;\n  };\n};\nasync function handleManifestRequest(build, routes, url) {\n  if (build.assets.version !== url.searchParams.get(\"version\")) {\n    return new Response(null, {\n      status: 204,\n      headers: {\n        \"X-Remix-Reload-Document\": \"true\"\n      }\n    });\n  }\n  let patches = {};\n  if (url.searchParams.has(\"paths\")) {\n    let paths = /* @__PURE__ */ new Set();\n    let pathParam = url.searchParams.get(\"paths\") || \"\";\n    let requestedPaths = pathParam.split(\",\").filter(Boolean);\n    requestedPaths.forEach((path) => {\n      if (!path.startsWith(\"/\")) {\n        path = `/${path}`;\n      }\n      let segments = path.split(\"/\").slice(1);\n      segments.forEach((_, i) => {\n        let partialPath = segments.slice(0, i + 1).join(\"/\");\n        paths.add(`/${partialPath}`);\n      });\n    });\n    for (let path of paths) {\n      let matches = matchServerRoutes(routes, path, build.basename);\n      if (matches) {\n        for (let match of matches) {\n          let routeId = match.route.id;\n          let route = build.assets.routes[routeId];\n          if (route) {\n            patches[routeId] = route;\n          }\n        }\n      }\n    }\n    return Response.json(patches, {\n      headers: {\n        \"Cache-Control\": \"public, max-age=31536000, immutable\"\n      }\n    });\n  }\n  return new Response(\"Invalid Request\", { status: 400 });\n}\nasync function handleSingleFetchRequest(serverMode, build, staticHandler, request, handlerUrl, loadContext, handleError) {\n  let response = request.method !== \"GET\" ? await singleFetchAction(\n    build,\n    serverMode,\n    staticHandler,\n    request,\n    handlerUrl,\n    loadContext,\n    handleError\n  ) : await singleFetchLoaders(\n    build,\n    serverMode,\n    staticHandler,\n    request,\n    handlerUrl,\n    loadContext,\n    handleError\n  );\n  return response;\n}\nasync function handleDocumentRequest(serverMode, build, staticHandler, request, loadContext, handleError, isSpaMode, criticalCss) {\n  try {\n    let result = await staticHandler.query(request, {\n      requestContext: loadContext,\n      generateMiddlewareResponse: build.future.v8_middleware ? async (query) => {\n        try {\n          let innerResult = await query(request);\n          if (!isResponse(innerResult)) {\n            innerResult = await renderHtml(innerResult, isSpaMode);\n          }\n          return innerResult;\n        } catch (error) {\n          handleError(error);\n          return new Response(null, { status: 500 });\n        }\n      } : void 0\n    });\n    if (!isResponse(result)) {\n      result = await renderHtml(result, isSpaMode);\n    }\n    return result;\n  } catch (error) {\n    handleError(error);\n    return new Response(null, { status: 500 });\n  }\n  async function renderHtml(context, isSpaMode2) {\n    let headers = getDocumentHeaders(context, build);\n    if (SERVER_NO_BODY_STATUS_CODES.has(context.statusCode)) {\n      return new Response(null, { status: context.statusCode, headers });\n    }\n    if (context.errors) {\n      Object.values(context.errors).forEach((err) => {\n        if (!isRouteErrorResponse(err) || err.error) {\n          handleError(err);\n        }\n      });\n      context.errors = sanitizeErrors(context.errors, serverMode);\n    }\n    let state = {\n      loaderData: context.loaderData,\n      actionData: context.actionData,\n      errors: serializeErrors(context.errors, serverMode)\n    };\n    let baseServerHandoff = {\n      basename: build.basename,\n      future: build.future,\n      routeDiscovery: build.routeDiscovery,\n      ssr: build.ssr,\n      isSpaMode: isSpaMode2\n    };\n    let entryContext = {\n      manifest: build.assets,\n      routeModules: createEntryRouteModules(build.routes),\n      staticHandlerContext: context,\n      criticalCss,\n      serverHandoffString: createServerHandoffString({\n        ...baseServerHandoff,\n        criticalCss\n      }),\n      serverHandoffStream: encodeViaTurboStream(\n        state,\n        request.signal,\n        build.entry.module.streamTimeout,\n        serverMode\n      ),\n      renderMeta: {},\n      future: build.future,\n      ssr: build.ssr,\n      routeDiscovery: build.routeDiscovery,\n      isSpaMode: isSpaMode2,\n      serializeError: (err) => serializeError(err, serverMode)\n    };\n    let handleDocumentRequestFunction = build.entry.module.default;\n    try {\n      return await handleDocumentRequestFunction(\n        request,\n        context.statusCode,\n        headers,\n        entryContext,\n        loadContext\n      );\n    } catch (error) {\n      handleError(error);\n      let errorForSecondRender = error;\n      if (isResponse(error)) {\n        try {\n          let data2 = await unwrapResponse(error);\n          errorForSecondRender = new ErrorResponseImpl(\n            error.status,\n            error.statusText,\n            data2\n          );\n        } catch (e) {\n        }\n      }\n      context = getStaticContextFromError(\n        staticHandler.dataRoutes,\n        context,\n        errorForSecondRender\n      );\n      if (context.errors) {\n        context.errors = sanitizeErrors(context.errors, serverMode);\n      }\n      let state2 = {\n        loaderData: context.loaderData,\n        actionData: context.actionData,\n        errors: serializeErrors(context.errors, serverMode)\n      };\n      entryContext = {\n        ...entryContext,\n        staticHandlerContext: context,\n        serverHandoffString: createServerHandoffString(baseServerHandoff),\n        serverHandoffStream: encodeViaTurboStream(\n          state2,\n          request.signal,\n          build.entry.module.streamTimeout,\n          serverMode\n        ),\n        renderMeta: {}\n      };\n      try {\n        return await handleDocumentRequestFunction(\n          request,\n          context.statusCode,\n          headers,\n          entryContext,\n          loadContext\n        );\n      } catch (error2) {\n        handleError(error2);\n        return returnLastResortErrorResponse(error2, serverMode);\n      }\n    }\n  }\n}\nasync function handleResourceRequest(serverMode, build, staticHandler, routeId, request, loadContext, handleError) {\n  try {\n    let result = await staticHandler.queryRoute(request, {\n      routeId,\n      requestContext: loadContext,\n      generateMiddlewareResponse: build.future.v8_middleware ? async (queryRoute) => {\n        try {\n          let innerResult = await queryRoute(request);\n          return handleQueryRouteResult(innerResult);\n        } catch (error) {\n          return handleQueryRouteError(error);\n        }\n      } : void 0\n    });\n    return handleQueryRouteResult(result);\n  } catch (error) {\n    return handleQueryRouteError(error);\n  }\n  function handleQueryRouteResult(result) {\n    if (isResponse(result)) {\n      return result;\n    }\n    if (typeof result === \"string\") {\n      return new Response(result);\n    }\n    return Response.json(result);\n  }\n  function handleQueryRouteError(error) {\n    if (isResponse(error)) {\n      return error;\n    }\n    if (isRouteErrorResponse(error)) {\n      handleError(error);\n      return errorResponseToJson(error, serverMode);\n    }\n    if (error instanceof Error && error.message === \"Expected a response from queryRoute\") {\n      let newError = new Error(\n        \"Expected a Response to be returned from resource route handler\"\n      );\n      handleError(newError);\n      return returnLastResortErrorResponse(newError, serverMode);\n    }\n    handleError(error);\n    return returnLastResortErrorResponse(error, serverMode);\n  }\n}\nfunction errorResponseToJson(errorResponse, serverMode) {\n  return Response.json(\n    serializeError(\n      // @ts-expect-error This is \"private\" from users but intended for internal use\n      errorResponse.error || new Error(\"Unexpected Server Error\"),\n      serverMode\n    ),\n    {\n      status: errorResponse.status,\n      statusText: errorResponse.statusText\n    }\n  );\n}\nfunction returnLastResortErrorResponse(error, serverMode) {\n  let message = \"Unexpected Server Error\";\n  if (serverMode !== \"production\" /* Production */) {\n    message += `\n\n${String(error)}`;\n  }\n  return new Response(message, {\n    status: 500,\n    headers: {\n      \"Content-Type\": \"text/plain\"\n    }\n  });\n}\nfunction unwrapResponse(response) {\n  let contentType = response.headers.get(\"Content-Type\");\n  return contentType && /\\bapplication\\/json\\b/.test(contentType) ? response.body == null ? null : response.json() : response.text();\n}\n\n// lib/server-runtime/sessions.ts\nfunction flash(name) {\n  return `__flash_${name}__`;\n}\nvar createSession = (initialData = {}, id = \"\") => {\n  let map = new Map(Object.entries(initialData));\n  return {\n    get id() {\n      return id;\n    },\n    get data() {\n      return Object.fromEntries(map);\n    },\n    has(name) {\n      return map.has(name) || map.has(flash(name));\n    },\n    get(name) {\n      if (map.has(name)) return map.get(name);\n      let flashName = flash(name);\n      if (map.has(flashName)) {\n        let value = map.get(flashName);\n        map.delete(flashName);\n        return value;\n      }\n      return void 0;\n    },\n    set(name, value) {\n      map.set(name, value);\n    },\n    flash(name, value) {\n      map.set(flash(name), value);\n    },\n    unset(name) {\n      map.delete(name);\n    }\n  };\n};\nvar isSession = (object) => {\n  return object != null && typeof object.id === \"string\" && typeof object.data !== \"undefined\" && typeof object.has === \"function\" && typeof object.get === \"function\" && typeof object.set === \"function\" && typeof object.flash === \"function\" && typeof object.unset === \"function\";\n};\nfunction createSessionStorage({\n  cookie: cookieArg,\n  createData,\n  readData,\n  updateData,\n  deleteData\n}) {\n  let cookie = isCookie(cookieArg) ? cookieArg : createCookie(cookieArg?.name || \"__session\", cookieArg);\n  warnOnceAboutSigningSessionCookie(cookie);\n  return {\n    async getSession(cookieHeader, options) {\n      let id = cookieHeader && await cookie.parse(cookieHeader, options);\n      let data2 = id && await readData(id);\n      return createSession(data2 || {}, id || \"\");\n    },\n    async commitSession(session, options) {\n      let { id, data: data2 } = session;\n      let expires = options?.maxAge != null ? new Date(Date.now() + options.maxAge * 1e3) : options?.expires != null ? options.expires : cookie.expires;\n      if (id) {\n        await updateData(id, data2, expires);\n      } else {\n        id = await createData(data2, expires);\n      }\n      return cookie.serialize(id, options);\n    },\n    async destroySession(session, options) {\n      await deleteData(session.id);\n      return cookie.serialize(\"\", {\n        ...options,\n        maxAge: void 0,\n        expires: /* @__PURE__ */ new Date(0)\n      });\n    }\n  };\n}\nfunction warnOnceAboutSigningSessionCookie(cookie) {\n  warnOnce(\n    cookie.isSigned,\n    `The \"${cookie.name}\" cookie is not signed, but session cookies should be signed to prevent tampering on the client before they are sent back to the server. See https://reactrouter.com/explanation/sessions-and-cookies#signing-cookies for more information.`\n  );\n}\n\n// lib/server-runtime/sessions/cookieStorage.ts\nfunction createCookieSessionStorage({ cookie: cookieArg } = {}) {\n  let cookie = isCookie(cookieArg) ? cookieArg : createCookie(cookieArg?.name || \"__session\", cookieArg);\n  warnOnceAboutSigningSessionCookie(cookie);\n  return {\n    async getSession(cookieHeader, options) {\n      return createSession(\n        cookieHeader && await cookie.parse(cookieHeader, options) || {}\n      );\n    },\n    async commitSession(session, options) {\n      let serializedCookie = await cookie.serialize(session.data, options);\n      if (serializedCookie.length > 4096) {\n        throw new Error(\n          \"Cookie length will exceed browser maximum. Length: \" + serializedCookie.length\n        );\n      }\n      return serializedCookie;\n    },\n    async destroySession(_session, options) {\n      return cookie.serialize(\"\", {\n        ...options,\n        maxAge: void 0,\n        expires: /* @__PURE__ */ new Date(0)\n      });\n    }\n  };\n}\n\n// lib/server-runtime/sessions/memoryStorage.ts\nfunction createMemorySessionStorage({ cookie } = {}) {\n  let map = /* @__PURE__ */ new Map();\n  return createSessionStorage({\n    cookie,\n    async createData(data2, expires) {\n      let id = Math.random().toString(36).substring(2, 10);\n      map.set(id, { data: data2, expires });\n      return id;\n    },\n    async readData(id) {\n      if (map.has(id)) {\n        let { data: data2, expires } = map.get(id);\n        if (!expires || expires > /* @__PURE__ */ new Date()) {\n          return data2;\n        }\n        if (expires) map.delete(id);\n      }\n      return null;\n    },\n    async updateData(id, data2, expires) {\n      map.set(id, { data: data2, expires });\n    },\n    async deleteData(id) {\n      map.delete(id);\n    }\n  });\n}\n\n// lib/href.ts\nfunction href(path, ...args) {\n  let params = args[0];\n  let result = path.replace(/\\/*\\*?$/, \"\").replace(\n    /\\/:([\\w-]+)(\\?)?/g,\n    // same regex as in .\\router\\utils.ts: compilePath().\n    (_, param, questionMark) => {\n      const isRequired = questionMark === void 0;\n      const value = params ? params[param] : void 0;\n      if (isRequired && value === void 0) {\n        throw new Error(\n          `Path '${path}' requires param '${param}' but it was not provided`\n        );\n      }\n      return value === void 0 ? \"\" : \"/\" + value;\n    }\n  );\n  if (path.endsWith(\"*\")) {\n    const value = params ? params[\"*\"] : void 0;\n    if (value !== void 0) {\n      result += \"/\" + value;\n    }\n  }\n  return result || \"/\";\n}\n\n// lib/rsc/browser.tsx\nimport * as React4 from \"react\";\nimport * as ReactDOM from \"react-dom\";\n\n// lib/dom/ssr/hydration.tsx\nfunction getHydrationData({\n  state,\n  routes,\n  getRouteInfo,\n  location: location2,\n  basename,\n  isSpaMode\n}) {\n  let hydrationData = {\n    ...state,\n    loaderData: { ...state.loaderData }\n  };\n  let initialMatches = matchRoutes(routes, location2, basename);\n  if (initialMatches) {\n    for (let match of initialMatches) {\n      let routeId = match.route.id;\n      let routeInfo = getRouteInfo(routeId);\n      if (shouldHydrateRouteLoader(\n        routeId,\n        routeInfo.clientLoader,\n        routeInfo.hasLoader,\n        isSpaMode\n      ) && (routeInfo.hasHydrateFallback || !routeInfo.hasLoader)) {\n        delete hydrationData.loaderData[routeId];\n      } else if (!routeInfo.hasLoader) {\n        hydrationData.loaderData[routeId] = null;\n      }\n    }\n  }\n  return hydrationData;\n}\n\n// lib/rsc/errorBoundaries.tsx\nimport React3 from \"react\";\nvar RSCRouterGlobalErrorBoundary = class extends React3.Component {\n  constructor(props) {\n    super(props);\n    this.state = { error: null, location: props.location };\n  }\n  static getDerivedStateFromError(error) {\n    return { error };\n  }\n  static getDerivedStateFromProps(props, state) {\n    if (state.location !== props.location) {\n      return { error: null, location: props.location };\n    }\n    return { error: state.error, location: state.location };\n  }\n  render() {\n    if (this.state.error) {\n      return /* @__PURE__ */ React3.createElement(\n        RSCDefaultRootErrorBoundaryImpl,\n        {\n          error: this.state.error,\n          renderAppShell: true\n        }\n      );\n    } else {\n      return this.props.children;\n    }\n  }\n};\nfunction ErrorWrapper({\n  renderAppShell,\n  title,\n  children\n}) {\n  if (!renderAppShell) {\n    return children;\n  }\n  return /* @__PURE__ */ React3.createElement(\"html\", { lang: \"en\" }, /* @__PURE__ */ React3.createElement(\"head\", null, /* @__PURE__ */ React3.createElement(\"meta\", { charSet: \"utf-8\" }), /* @__PURE__ */ React3.createElement(\n    \"meta\",\n    {\n      name: \"viewport\",\n      content: \"width=device-width,initial-scale=1,viewport-fit=cover\"\n    }\n  ), /* @__PURE__ */ React3.createElement(\"title\", null, title)), /* @__PURE__ */ React3.createElement(\"body\", null, /* @__PURE__ */ React3.createElement(\"main\", { style: { fontFamily: \"system-ui, sans-serif\", padding: \"2rem\" } }, children)));\n}\nfunction RSCDefaultRootErrorBoundaryImpl({\n  error,\n  renderAppShell\n}) {\n  console.error(error);\n  let heyDeveloper = /* @__PURE__ */ React3.createElement(\n    \"script\",\n    {\n      dangerouslySetInnerHTML: {\n        __html: `\n        console.log(\n          \"\\u{1F4BF} Hey developer \\u{1F44B}. You can provide a way better UX than this when your app throws errors. Check out https://reactrouter.com/how-to/error-boundary for more information.\"\n        );\n      `\n      }\n    }\n  );\n  if (isRouteErrorResponse(error)) {\n    return /* @__PURE__ */ React3.createElement(\n      ErrorWrapper,\n      {\n        renderAppShell,\n        title: \"Unhandled Thrown Response!\"\n      },\n      /* @__PURE__ */ React3.createElement(\"h1\", { style: { fontSize: \"24px\" } }, error.status, \" \", error.statusText),\n      ENABLE_DEV_WARNINGS ? heyDeveloper : null\n    );\n  }\n  let errorInstance;\n  if (error instanceof Error) {\n    errorInstance = error;\n  } else {\n    let errorString = error == null ? \"Unknown Error\" : typeof error === \"object\" && \"toString\" in error ? error.toString() : JSON.stringify(error);\n    errorInstance = new Error(errorString);\n  }\n  return /* @__PURE__ */ React3.createElement(ErrorWrapper, { renderAppShell, title: \"Application Error!\" }, /* @__PURE__ */ React3.createElement(\"h1\", { style: { fontSize: \"24px\" } }, \"Application Error\"), /* @__PURE__ */ React3.createElement(\n    \"pre\",\n    {\n      style: {\n        padding: \"2rem\",\n        background: \"hsla(10, 50%, 50%, 0.1)\",\n        color: \"red\",\n        overflow: \"auto\"\n      }\n    },\n    errorInstance.stack\n  ), heyDeveloper);\n}\nfunction RSCDefaultRootErrorBoundary({\n  hasRootLayout\n}) {\n  let error = useRouteError();\n  if (hasRootLayout === void 0) {\n    throw new Error(\"Missing 'hasRootLayout' prop\");\n  }\n  return /* @__PURE__ */ React3.createElement(\n    RSCDefaultRootErrorBoundaryImpl,\n    {\n      renderAppShell: !hasRootLayout,\n      error\n    }\n  );\n}\n\n// lib/rsc/route-modules.ts\nfunction createRSCRouteModules(payload) {\n  const routeModules = {};\n  for (const match of payload.matches) {\n    populateRSCRouteModules(routeModules, match);\n  }\n  return routeModules;\n}\nfunction populateRSCRouteModules(routeModules, matches) {\n  matches = Array.isArray(matches) ? matches : [matches];\n  for (const match of matches) {\n    routeModules[match.id] = {\n      links: match.links,\n      meta: match.meta,\n      default: noopComponent\n    };\n  }\n}\nvar noopComponent = () => null;\n\n// lib/rsc/browser.tsx\nfunction createCallServer({\n  createFromReadableStream,\n  createTemporaryReferenceSet,\n  encodeReply,\n  fetch: fetchImplementation = fetch\n}) {\n  const globalVar = window;\n  let landedActionId = 0;\n  return async (id, args) => {\n    let actionId = globalVar.__routerActionID = (globalVar.__routerActionID ?? (globalVar.__routerActionID = 0)) + 1;\n    const temporaryReferences = createTemporaryReferenceSet();\n    const payloadPromise = fetchImplementation(\n      new Request(location.href, {\n        body: await encodeReply(args, { temporaryReferences }),\n        method: \"POST\",\n        headers: {\n          Accept: \"text/x-component\",\n          \"rsc-action-id\": id\n        }\n      })\n    ).then((response) => {\n      if (!response.body) {\n        throw new Error(\"No response body\");\n      }\n      return createFromReadableStream(response.body, {\n        temporaryReferences\n      });\n    });\n    globalVar.__reactRouterDataRouter.__setPendingRerender(\n      Promise.resolve(payloadPromise).then(async (payload) => {\n        if (payload.type === \"redirect\") {\n          if (payload.reload) {\n            window.location.href = payload.location;\n            return () => {\n            };\n          }\n          return () => {\n            globalVar.__reactRouterDataRouter.navigate(payload.location, {\n              replace: payload.replace\n            });\n          };\n        }\n        if (payload.type !== \"action\") {\n          throw new Error(\"Unexpected payload type\");\n        }\n        const rerender = await payload.rerender;\n        if (rerender && landedActionId < actionId && globalVar.__routerActionID <= actionId) {\n          if (rerender.type === \"redirect\") {\n            if (rerender.reload) {\n              window.location.href = rerender.location;\n              return;\n            }\n            return () => {\n              globalVar.__reactRouterDataRouter.navigate(rerender.location, {\n                replace: rerender.replace\n              });\n            };\n          }\n          return () => {\n            let lastMatch;\n            for (const match of rerender.matches) {\n              globalVar.__reactRouterDataRouter.patchRoutes(\n                lastMatch?.id ?? null,\n                [createRouteFromServerManifest(match)],\n                true\n              );\n              lastMatch = match;\n            }\n            window.__reactRouterDataRouter._internalSetStateDoNotUseOrYouWillBreakYourApp(\n              {\n                loaderData: Object.assign(\n                  {},\n                  globalVar.__reactRouterDataRouter.state.loaderData,\n                  rerender.loaderData\n                ),\n                errors: rerender.errors ? Object.assign(\n                  {},\n                  globalVar.__reactRouterDataRouter.state.errors,\n                  rerender.errors\n                ) : null\n              }\n            );\n          };\n        }\n        return () => {\n        };\n      }).catch(() => {\n      })\n    );\n    return payloadPromise.then((payload) => {\n      if (payload.type !== \"action\" && payload.type !== \"redirect\") {\n        throw new Error(\"Unexpected payload type\");\n      }\n      return payload.actionResult;\n    });\n  };\n}\nfunction createRouterFromPayload({\n  fetchImplementation,\n  createFromReadableStream,\n  getContext,\n  payload\n}) {\n  const globalVar = window;\n  if (globalVar.__reactRouterDataRouter && globalVar.__reactRouterRouteModules)\n    return {\n      router: globalVar.__reactRouterDataRouter,\n      routeModules: globalVar.__reactRouterRouteModules\n    };\n  if (payload.type !== \"render\") throw new Error(\"Invalid payload type\");\n  globalVar.__reactRouterRouteModules = globalVar.__reactRouterRouteModules ?? {};\n  populateRSCRouteModules(globalVar.__reactRouterRouteModules, payload.matches);\n  let patches = /* @__PURE__ */ new Map();\n  payload.patches?.forEach((patch) => {\n    invariant(patch.parentId, \"Invalid patch parentId\");\n    if (!patches.has(patch.parentId)) {\n      patches.set(patch.parentId, []);\n    }\n    patches.get(patch.parentId)?.push(patch);\n  });\n  let routes = payload.matches.reduceRight((previous, match) => {\n    const route = createRouteFromServerManifest(\n      match,\n      payload\n    );\n    if (previous.length > 0) {\n      route.children = previous;\n      let childrenToPatch = patches.get(match.id);\n      if (childrenToPatch) {\n        route.children.push(\n          ...childrenToPatch.map((r) => createRouteFromServerManifest(r))\n        );\n      }\n    }\n    return [route];\n  }, []);\n  globalVar.__reactRouterDataRouter = createRouter({\n    routes,\n    getContext,\n    basename: payload.basename,\n    history: createBrowserHistory(),\n    hydrationData: getHydrationData({\n      state: {\n        loaderData: payload.loaderData,\n        actionData: payload.actionData,\n        errors: payload.errors\n      },\n      routes,\n      getRouteInfo: (routeId) => {\n        let match = payload.matches.find((m) => m.id === routeId);\n        invariant(match, \"Route not found in payload\");\n        return {\n          clientLoader: match.clientLoader,\n          hasLoader: match.hasLoader,\n          hasHydrateFallback: match.hydrateFallbackElement != null\n        };\n      },\n      location: payload.location,\n      basename: payload.basename,\n      isSpaMode: false\n    }),\n    async patchRoutesOnNavigation({ path, signal }) {\n      if (discoveredPaths.has(path)) {\n        return;\n      }\n      await fetchAndApplyManifestPatches(\n        [path],\n        createFromReadableStream,\n        fetchImplementation,\n        signal\n      );\n    },\n    // FIXME: Pass `build.ssr` into this function\n    dataStrategy: getRSCSingleFetchDataStrategy(\n      () => globalVar.__reactRouterDataRouter,\n      true,\n      payload.basename,\n      createFromReadableStream,\n      fetchImplementation\n    )\n  });\n  if (globalVar.__reactRouterDataRouter.state.initialized) {\n    globalVar.__routerInitialized = true;\n    globalVar.__reactRouterDataRouter.initialize();\n  } else {\n    globalVar.__routerInitialized = false;\n  }\n  let lastLoaderData = void 0;\n  globalVar.__reactRouterDataRouter.subscribe(({ loaderData, actionData }) => {\n    if (lastLoaderData !== loaderData) {\n      globalVar.__routerActionID = (globalVar.__routerActionID ?? (globalVar.__routerActionID = 0)) + 1;\n    }\n  });\n  globalVar.__reactRouterDataRouter._updateRoutesForHMR = (routeUpdateByRouteId) => {\n    const oldRoutes = window.__reactRouterDataRouter.routes;\n    const newRoutes = [];\n    function walkRoutes(routes2, parentId) {\n      return routes2.map((route) => {\n        const routeUpdate = routeUpdateByRouteId.get(route.id);\n        if (routeUpdate) {\n          const {\n            routeModule,\n            hasAction,\n            hasComponent,\n            hasErrorBoundary,\n            hasLoader\n          } = routeUpdate;\n          const newRoute = createRouteFromServerManifest({\n            clientAction: routeModule.clientAction,\n            clientLoader: routeModule.clientLoader,\n            element: route.element,\n            errorElement: route.errorElement,\n            handle: route.handle,\n            hasAction,\n            hasComponent,\n            hasErrorBoundary,\n            hasLoader,\n            hydrateFallbackElement: route.hydrateFallbackElement,\n            id: route.id,\n            index: route.index,\n            links: routeModule.links,\n            meta: routeModule.meta,\n            parentId,\n            path: route.path,\n            shouldRevalidate: routeModule.shouldRevalidate\n          });\n          if (route.children) {\n            newRoute.children = walkRoutes(route.children, route.id);\n          }\n          return newRoute;\n        }\n        const updatedRoute = { ...route };\n        if (route.children) {\n          updatedRoute.children = walkRoutes(route.children, route.id);\n        }\n        return updatedRoute;\n      });\n    }\n    newRoutes.push(\n      ...walkRoutes(oldRoutes, void 0)\n    );\n    window.__reactRouterDataRouter._internalSetRoutes(newRoutes);\n  };\n  return {\n    router: globalVar.__reactRouterDataRouter,\n    routeModules: globalVar.__reactRouterRouteModules\n  };\n}\nvar renderedRoutesContext = createContext();\nfunction getRSCSingleFetchDataStrategy(getRouter, ssr, basename, createFromReadableStream, fetchImplementation) {\n  let dataStrategy = getSingleFetchDataStrategyImpl(\n    getRouter,\n    (match) => {\n      let M = match;\n      return {\n        hasLoader: M.route.hasLoader,\n        hasClientLoader: M.route.hasClientLoader,\n        hasComponent: M.route.hasComponent,\n        hasAction: M.route.hasAction,\n        hasClientAction: M.route.hasClientAction,\n        hasShouldRevalidate: M.route.hasShouldRevalidate\n      };\n    },\n    // pass map into fetchAndDecode so it can add payloads\n    getFetchAndDecodeViaRSC(createFromReadableStream, fetchImplementation),\n    ssr,\n    basename,\n    // If the route has a component but we don't have an element, we need to hit\n    // the server loader flow regardless of whether the client loader calls\n    // `serverLoader` or not, otherwise we'll have nothing to render.\n    (match) => {\n      let M = match;\n      return M.route.hasComponent && !M.route.element;\n    }\n  );\n  return async (args) => args.runClientMiddleware(async () => {\n    let context = args.context;\n    context.set(renderedRoutesContext, []);\n    let results = await dataStrategy(args);\n    const renderedRoutesById = /* @__PURE__ */ new Map();\n    for (const route of context.get(renderedRoutesContext)) {\n      if (!renderedRoutesById.has(route.id)) {\n        renderedRoutesById.set(route.id, []);\n      }\n      renderedRoutesById.get(route.id).push(route);\n    }\n    for (const match of args.matches) {\n      const renderedRoutes = renderedRoutesById.get(match.route.id);\n      if (renderedRoutes) {\n        for (const rendered of renderedRoutes) {\n          window.__reactRouterDataRouter.patchRoutes(\n            rendered.parentId ?? null,\n            [createRouteFromServerManifest(rendered)],\n            true\n          );\n        }\n      }\n    }\n    return results;\n  });\n}\nfunction getFetchAndDecodeViaRSC(createFromReadableStream, fetchImplementation) {\n  return async (args, basename, targetRoutes) => {\n    let { request, context } = args;\n    let url = singleFetchUrl(request.url, basename, \"rsc\");\n    if (request.method === \"GET\") {\n      url = stripIndexParam(url);\n      if (targetRoutes) {\n        url.searchParams.set(\"_routes\", targetRoutes.join(\",\"));\n      }\n    }\n    let res = await fetchImplementation(\n      new Request(url, await createRequestInit(request))\n    );\n    if (res.status >= 400 && !res.headers.has(\"X-Remix-Response\")) {\n      throw new ErrorResponseImpl(res.status, res.statusText, await res.text());\n    }\n    invariant(res.body, \"No response body to decode\");\n    try {\n      const payload = await createFromReadableStream(res.body, {\n        temporaryReferences: void 0\n      });\n      if (payload.type === \"redirect\") {\n        return {\n          status: res.status,\n          data: {\n            redirect: {\n              redirect: payload.location,\n              reload: payload.reload,\n              replace: payload.replace,\n              revalidate: false,\n              status: payload.status\n            }\n          }\n        };\n      }\n      if (payload.type !== \"render\") {\n        throw new Error(\"Unexpected payload type\");\n      }\n      context.get(renderedRoutesContext).push(...payload.matches);\n      let results = { routes: {} };\n      const dataKey = isMutationMethod(request.method) ? \"actionData\" : \"loaderData\";\n      for (let [routeId, data2] of Object.entries(payload[dataKey] || {})) {\n        results.routes[routeId] = { data: data2 };\n      }\n      if (payload.errors) {\n        for (let [routeId, error] of Object.entries(payload.errors)) {\n          results.routes[routeId] = { error };\n        }\n      }\n      return { status: res.status, data: results };\n    } catch (e) {\n      throw new Error(\"Unable to decode RSC response\");\n    }\n  };\n}\nfunction RSCHydratedRouter({\n  createFromReadableStream,\n  fetch: fetchImplementation = fetch,\n  payload,\n  routeDiscovery = \"eager\",\n  getContext\n}) {\n  if (payload.type !== \"render\") throw new Error(\"Invalid payload type\");\n  let { router, routeModules } = React4.useMemo(\n    () => createRouterFromPayload({\n      payload,\n      fetchImplementation,\n      getContext,\n      createFromReadableStream\n    }),\n    [createFromReadableStream, payload, fetchImplementation, getContext]\n  );\n  React4.useEffect(() => {\n    setIsHydrated();\n  }, []);\n  React4.useLayoutEffect(() => {\n    const globalVar = window;\n    if (!globalVar.__routerInitialized) {\n      globalVar.__routerInitialized = true;\n      globalVar.__reactRouterDataRouter.initialize();\n    }\n  }, []);\n  let [location2, setLocation] = React4.useState(router.state.location);\n  React4.useLayoutEffect(\n    () => router.subscribe((newState) => {\n      if (newState.location !== location2) {\n        setLocation(newState.location);\n      }\n    }),\n    [router, location2]\n  );\n  React4.useEffect(() => {\n    if (routeDiscovery === \"lazy\" || // @ts-expect-error - TS doesn't know about this yet\n    window.navigator?.connection?.saveData === true) {\n      return;\n    }\n    function registerElement(el) {\n      let path = el.tagName === \"FORM\" ? el.getAttribute(\"action\") : el.getAttribute(\"href\");\n      if (!path) {\n        return;\n      }\n      let pathname = el.tagName === \"A\" ? el.pathname : new URL(path, window.location.origin).pathname;\n      if (!discoveredPaths.has(pathname)) {\n        nextPaths.add(pathname);\n      }\n    }\n    async function fetchPatches() {\n      document.querySelectorAll(\"a[data-discover], form[data-discover]\").forEach(registerElement);\n      let paths = Array.from(nextPaths.keys()).filter((path) => {\n        if (discoveredPaths.has(path)) {\n          nextPaths.delete(path);\n          return false;\n        }\n        return true;\n      });\n      if (paths.length === 0) {\n        return;\n      }\n      try {\n        await fetchAndApplyManifestPatches(\n          paths,\n          createFromReadableStream,\n          fetchImplementation\n        );\n      } catch (e) {\n        console.error(\"Failed to fetch manifest patches\", e);\n      }\n    }\n    let debouncedFetchPatches = debounce(fetchPatches, 100);\n    fetchPatches();\n    let observer = new MutationObserver(() => debouncedFetchPatches());\n    observer.observe(document.documentElement, {\n      subtree: true,\n      childList: true,\n      attributes: true,\n      attributeFilter: [\"data-discover\", \"href\", \"action\"]\n    });\n  }, [routeDiscovery, createFromReadableStream, fetchImplementation]);\n  const frameworkContext = {\n    future: {\n      // These flags have no runtime impact so can always be false.  If we add\n      // flags that drive runtime behavior they'll need to be proxied through.\n      v8_middleware: false,\n      unstable_subResourceIntegrity: false\n    },\n    isSpaMode: false,\n    ssr: true,\n    criticalCss: \"\",\n    manifest: {\n      routes: {},\n      version: \"1\",\n      url: \"\",\n      entry: {\n        module: \"\",\n        imports: []\n      }\n    },\n    routeDiscovery: { mode: \"lazy\", manifestPath: \"/__manifest\" },\n    routeModules\n  };\n  return /* @__PURE__ */ React4.createElement(RSCRouterContext.Provider, { value: true }, /* @__PURE__ */ React4.createElement(RSCRouterGlobalErrorBoundary, { location: location2 }, /* @__PURE__ */ React4.createElement(FrameworkContext.Provider, { value: frameworkContext }, /* @__PURE__ */ React4.createElement(UNSTABLE_TransitionEnabledRouterProvider, { router, flushSync: ReactDOM.flushSync }))));\n}\nfunction createRouteFromServerManifest(match, payload) {\n  let hasInitialData = payload && match.id in payload.loaderData;\n  let initialData = payload?.loaderData[match.id];\n  let hasInitialError = payload?.errors && match.id in payload.errors;\n  let initialError = payload?.errors?.[match.id];\n  let isHydrationRequest = match.clientLoader?.hydrate === true || !match.hasLoader || // If the route has a component but we don't have an element, we need to hit\n  // the server loader flow regardless of whether the client loader calls\n  // `serverLoader` or not, otherwise we'll have nothing to render.\n  match.hasComponent && !match.element;\n  invariant(window.__reactRouterRouteModules);\n  populateRSCRouteModules(window.__reactRouterRouteModules, match);\n  let dataRoute = {\n    id: match.id,\n    element: match.element,\n    errorElement: match.errorElement,\n    handle: match.handle,\n    hasErrorBoundary: match.hasErrorBoundary,\n    hydrateFallbackElement: match.hydrateFallbackElement,\n    index: match.index,\n    loader: match.clientLoader ? async (args, singleFetch) => {\n      try {\n        let result = await match.clientLoader({\n          ...args,\n          serverLoader: () => {\n            preventInvalidServerHandlerCall(\n              \"loader\",\n              match.id,\n              match.hasLoader\n            );\n            if (isHydrationRequest) {\n              if (hasInitialData) {\n                return initialData;\n              }\n              if (hasInitialError) {\n                throw initialError;\n              }\n            }\n            return callSingleFetch(singleFetch);\n          }\n        });\n        return result;\n      } finally {\n        isHydrationRequest = false;\n      }\n    } : (\n      // We always make the call in this RSC world since even if we don't\n      // have a `loader` we may need to get the `element` implementation\n      (_, singleFetch) => callSingleFetch(singleFetch)\n    ),\n    action: match.clientAction ? (args, singleFetch) => match.clientAction({\n      ...args,\n      serverAction: async () => {\n        preventInvalidServerHandlerCall(\n          \"action\",\n          match.id,\n          match.hasLoader\n        );\n        return await callSingleFetch(singleFetch);\n      }\n    }) : match.hasAction ? (_, singleFetch) => callSingleFetch(singleFetch) : () => {\n      throw noActionDefinedError(\"action\", match.id);\n    },\n    path: match.path,\n    shouldRevalidate: match.shouldRevalidate,\n    // We always have a \"loader\" in this RSC world since even if we don't\n    // have a `loader` we may need to get the `element` implementation\n    hasLoader: true,\n    hasClientLoader: match.clientLoader != null,\n    hasAction: match.hasAction,\n    hasClientAction: match.clientAction != null,\n    hasShouldRevalidate: match.shouldRevalidate != null\n  };\n  if (typeof dataRoute.loader === \"function\") {\n    dataRoute.loader.hydrate = shouldHydrateRouteLoader(\n      match.id,\n      match.clientLoader,\n      match.hasLoader,\n      false\n    );\n  }\n  return dataRoute;\n}\nfunction callSingleFetch(singleFetch) {\n  invariant(typeof singleFetch === \"function\", \"Invalid singleFetch parameter\");\n  return singleFetch();\n}\nfunction preventInvalidServerHandlerCall(type, routeId, hasHandler) {\n  if (!hasHandler) {\n    let fn = type === \"action\" ? \"serverAction()\" : \"serverLoader()\";\n    let msg = `You are trying to call ${fn} on a route that does not have a server ${type} (routeId: \"${routeId}\")`;\n    console.error(msg);\n    throw new ErrorResponseImpl(400, \"Bad Request\", new Error(msg), true);\n  }\n}\nvar nextPaths = /* @__PURE__ */ new Set();\nvar discoveredPathsMaxSize = 1e3;\nvar discoveredPaths = /* @__PURE__ */ new Set();\nvar URL_LIMIT = 7680;\nfunction getManifestUrl(paths) {\n  if (paths.length === 0) {\n    return null;\n  }\n  if (paths.length === 1) {\n    return new URL(`${paths[0]}.manifest`, window.location.origin);\n  }\n  const globalVar = window;\n  let basename = (globalVar.__reactRouterDataRouter.basename ?? \"\").replace(\n    /^\\/|\\/$/g,\n    \"\"\n  );\n  let url = new URL(`${basename}/.manifest`, window.location.origin);\n  url.searchParams.set(\"paths\", paths.sort().join(\",\"));\n  return url;\n}\nasync function fetchAndApplyManifestPatches(paths, createFromReadableStream, fetchImplementation, signal) {\n  let url = getManifestUrl(paths);\n  if (url == null) {\n    return;\n  }\n  if (url.toString().length > URL_LIMIT) {\n    nextPaths.clear();\n    return;\n  }\n  let response = await fetchImplementation(new Request(url, { signal }));\n  if (!response.body || response.status < 200 || response.status >= 300) {\n    throw new Error(\"Unable to fetch new route matches from the server\");\n  }\n  let payload = await createFromReadableStream(response.body, {\n    temporaryReferences: void 0\n  });\n  if (payload.type !== \"manifest\") {\n    throw new Error(\"Failed to patch routes\");\n  }\n  paths.forEach((p) => addToFifoQueue(p, discoveredPaths));\n  payload.patches.forEach((p) => {\n    window.__reactRouterDataRouter.patchRoutes(\n      p.parentId ?? null,\n      [createRouteFromServerManifest(p)]\n    );\n  });\n}\nfunction addToFifoQueue(path, queue) {\n  if (queue.size >= discoveredPathsMaxSize) {\n    let first = queue.values().next().value;\n    queue.delete(first);\n  }\n  queue.add(path);\n}\nfunction debounce(callback, wait) {\n  let timeoutId;\n  return (...args) => {\n    window.clearTimeout(timeoutId);\n    timeoutId = window.setTimeout(() => callback(...args), wait);\n  };\n}\n\n// lib/rsc/server.ssr.tsx\nimport * as React5 from \"react\";\n\n// lib/rsc/html-stream/server.ts\nvar encoder2 = new TextEncoder();\nvar trailer = \"</body></html>\";\nfunction injectRSCPayload(rscStream) {\n  let decoder = new TextDecoder();\n  let resolveFlightDataPromise;\n  let flightDataPromise = new Promise(\n    (resolve) => resolveFlightDataPromise = resolve\n  );\n  let startedRSC = false;\n  let buffered = [];\n  let timeout = null;\n  function flushBufferedChunks(controller) {\n    for (let chunk of buffered) {\n      let buf = decoder.decode(chunk, { stream: true });\n      if (buf.endsWith(trailer)) {\n        buf = buf.slice(0, -trailer.length);\n      }\n      controller.enqueue(encoder2.encode(buf));\n    }\n    buffered.length = 0;\n    timeout = null;\n  }\n  return new TransformStream({\n    transform(chunk, controller) {\n      buffered.push(chunk);\n      if (timeout) {\n        return;\n      }\n      timeout = setTimeout(async () => {\n        flushBufferedChunks(controller);\n        if (!startedRSC) {\n          startedRSC = true;\n          writeRSCStream(rscStream, controller).catch((err) => controller.error(err)).then(resolveFlightDataPromise);\n        }\n      }, 0);\n    },\n    async flush(controller) {\n      await flightDataPromise;\n      if (timeout) {\n        clearTimeout(timeout);\n        flushBufferedChunks(controller);\n      }\n      controller.enqueue(encoder2.encode(\"</body></html>\"));\n    }\n  });\n}\nasync function writeRSCStream(rscStream, controller) {\n  let decoder = new TextDecoder(\"utf-8\", { fatal: true });\n  const reader = rscStream.getReader();\n  try {\n    let read;\n    while ((read = await reader.read()) && !read.done) {\n      const chunk = read.value;\n      try {\n        writeChunk(\n          JSON.stringify(decoder.decode(chunk, { stream: true })),\n          controller\n        );\n      } catch (err) {\n        let base64 = JSON.stringify(btoa(String.fromCodePoint(...chunk)));\n        writeChunk(\n          `Uint8Array.from(atob(${base64}), m => m.codePointAt(0))`,\n          controller\n        );\n      }\n    }\n  } finally {\n    reader.releaseLock();\n  }\n  let remaining = decoder.decode();\n  if (remaining.length) {\n    writeChunk(JSON.stringify(remaining), controller);\n  }\n}\nfunction writeChunk(chunk, controller) {\n  controller.enqueue(\n    encoder2.encode(\n      `<script>${escapeScript(\n        `(self.__FLIGHT_DATA||=[]).push(${chunk})`\n      )}</script>`\n    )\n  );\n}\nfunction escapeScript(script) {\n  return script.replace(/<!--/g, \"<\\\\!--\").replace(/<\\/(script)/gi, \"</\\\\$1\");\n}\n\n// lib/rsc/server.ssr.tsx\nvar REACT_USE = \"use\";\nvar useImpl = React5[REACT_USE];\nfunction useSafe(promise) {\n  if (useImpl) {\n    return useImpl(promise);\n  }\n  throw new Error(\"React Router v7 requires React 19+ for RSC features.\");\n}\nasync function routeRSCServerRequest({\n  request,\n  fetchServer,\n  createFromReadableStream,\n  renderHTML,\n  hydrate = true\n}) {\n  const url = new URL(request.url);\n  const isDataRequest = isReactServerRequest(url);\n  const respondWithRSCPayload = isDataRequest || isManifestRequest(url) || request.headers.has(\"rsc-action-id\");\n  const serverResponse = await fetchServer(request);\n  if (respondWithRSCPayload || serverResponse.headers.get(\"React-Router-Resource\") === \"true\") {\n    return serverResponse;\n  }\n  if (!serverResponse.body) {\n    throw new Error(\"Missing body in server response\");\n  }\n  const detectRedirectResponse = serverResponse.clone();\n  let serverResponseB = null;\n  if (hydrate) {\n    serverResponseB = serverResponse.clone();\n  }\n  const body = serverResponse.body;\n  let buffer;\n  let streamControllers = [];\n  const createStream = () => {\n    if (!buffer) {\n      buffer = [];\n      return body.pipeThrough(\n        new TransformStream({\n          transform(chunk, controller) {\n            buffer.push(chunk);\n            controller.enqueue(chunk);\n            streamControllers.forEach((c) => c.enqueue(chunk));\n          },\n          flush() {\n            streamControllers.forEach((c) => c.close());\n            streamControllers = [];\n          }\n        })\n      );\n    }\n    return new ReadableStream({\n      start(controller) {\n        buffer.forEach((chunk) => controller.enqueue(chunk));\n        streamControllers.push(controller);\n      }\n    });\n  };\n  let deepestRenderedBoundaryId = null;\n  const getPayload = () => {\n    const payloadPromise = Promise.resolve(\n      createFromReadableStream(createStream())\n    );\n    return Object.defineProperties(payloadPromise, {\n      _deepestRenderedBoundaryId: {\n        get() {\n          return deepestRenderedBoundaryId;\n        },\n        set(boundaryId) {\n          deepestRenderedBoundaryId = boundaryId;\n        }\n      },\n      formState: {\n        get() {\n          return payloadPromise.then(\n            (payload) => payload.type === \"render\" ? payload.formState : void 0\n          );\n        }\n      }\n    });\n  };\n  try {\n    if (!detectRedirectResponse.body) {\n      throw new Error(\"Failed to clone server response\");\n    }\n    const payload = await createFromReadableStream(\n      detectRedirectResponse.body\n    );\n    if (serverResponse.status === SINGLE_FETCH_REDIRECT_STATUS && payload.type === \"redirect\") {\n      const headers2 = new Headers(serverResponse.headers);\n      headers2.delete(\"Content-Encoding\");\n      headers2.delete(\"Content-Length\");\n      headers2.delete(\"Content-Type\");\n      headers2.delete(\"X-Remix-Response\");\n      headers2.set(\"Location\", payload.location);\n      return new Response(serverResponseB?.body || \"\", {\n        headers: headers2,\n        status: payload.status,\n        statusText: serverResponse.statusText\n      });\n    }\n    const html = await renderHTML(getPayload);\n    const headers = new Headers(serverResponse.headers);\n    headers.set(\"Content-Type\", \"text/html; charset=utf-8\");\n    if (!hydrate) {\n      return new Response(html, {\n        status: serverResponse.status,\n        headers\n      });\n    }\n    if (!serverResponseB?.body) {\n      throw new Error(\"Failed to clone server response\");\n    }\n    const body2 = html.pipeThrough(injectRSCPayload(serverResponseB.body));\n    return new Response(body2, {\n      status: serverResponse.status,\n      headers\n    });\n  } catch (reason) {\n    if (reason instanceof Response) {\n      return reason;\n    }\n    try {\n      const status = isRouteErrorResponse(reason) ? reason.status : 500;\n      const html = await renderHTML(() => {\n        const decoded = Promise.resolve(\n          createFromReadableStream(createStream())\n        );\n        const payloadPromise = decoded.then(\n          (payload) => Object.assign(payload, {\n            status,\n            errors: deepestRenderedBoundaryId ? {\n              [deepestRenderedBoundaryId]: reason\n            } : {}\n          })\n        );\n        return Object.defineProperties(payloadPromise, {\n          _deepestRenderedBoundaryId: {\n            get() {\n              return deepestRenderedBoundaryId;\n            },\n            set(boundaryId) {\n              deepestRenderedBoundaryId = boundaryId;\n            }\n          },\n          formState: {\n            get() {\n              return payloadPromise.then(\n                (payload) => payload.type === \"render\" ? payload.formState : void 0\n              );\n            }\n          }\n        });\n      });\n      const headers = new Headers(serverResponse.headers);\n      headers.set(\"Content-Type\", \"text/html\");\n      if (!hydrate) {\n        return new Response(html, {\n          status,\n          headers\n        });\n      }\n      if (!serverResponseB?.body) {\n        throw new Error(\"Failed to clone server response\");\n      }\n      const body2 = html.pipeThrough(injectRSCPayload(serverResponseB.body));\n      return new Response(body2, {\n        status,\n        headers\n      });\n    } catch {\n    }\n    throw reason;\n  }\n}\nfunction RSCStaticRouter({ getPayload }) {\n  const decoded = getPayload();\n  const payload = useSafe(decoded);\n  if (payload.type === \"redirect\") {\n    throw new Response(null, {\n      status: payload.status,\n      headers: {\n        Location: payload.location\n      }\n    });\n  }\n  if (payload.type !== \"render\") return null;\n  let patchedLoaderData = { ...payload.loaderData };\n  for (const match of payload.matches) {\n    if (shouldHydrateRouteLoader(\n      match.id,\n      match.clientLoader,\n      match.hasLoader,\n      false\n    ) && (match.hydrateFallbackElement || !match.hasLoader)) {\n      delete patchedLoaderData[match.id];\n    }\n  }\n  const context = {\n    get _deepestRenderedBoundaryId() {\n      return decoded._deepestRenderedBoundaryId ?? null;\n    },\n    set _deepestRenderedBoundaryId(boundaryId) {\n      decoded._deepestRenderedBoundaryId = boundaryId;\n    },\n    actionData: payload.actionData,\n    actionHeaders: {},\n    basename: payload.basename,\n    errors: payload.errors,\n    loaderData: patchedLoaderData,\n    loaderHeaders: {},\n    location: payload.location,\n    statusCode: 200,\n    matches: payload.matches.map((match) => ({\n      params: match.params,\n      pathname: match.pathname,\n      pathnameBase: match.pathnameBase,\n      route: {\n        id: match.id,\n        action: match.hasAction || !!match.clientAction,\n        handle: match.handle,\n        hasErrorBoundary: match.hasErrorBoundary,\n        loader: match.hasLoader || !!match.clientLoader,\n        index: match.index,\n        path: match.path,\n        shouldRevalidate: match.shouldRevalidate\n      }\n    }))\n  };\n  const router = createStaticRouter(\n    payload.matches.reduceRight((previous, match) => {\n      const route = {\n        id: match.id,\n        action: match.hasAction || !!match.clientAction,\n        element: match.element,\n        errorElement: match.errorElement,\n        handle: match.handle,\n        hasErrorBoundary: !!match.errorElement,\n        hydrateFallbackElement: match.hydrateFallbackElement,\n        index: match.index,\n        loader: match.hasLoader || !!match.clientLoader,\n        path: match.path,\n        shouldRevalidate: match.shouldRevalidate\n      };\n      if (previous.length > 0) {\n        route.children = previous;\n      }\n      return [route];\n    }, []),\n    context\n  );\n  const frameworkContext = {\n    future: {\n      // These flags have no runtime impact so can always be false.  If we add\n      // flags that drive runtime behavior they'll need to be proxied through.\n      v8_middleware: false,\n      unstable_subResourceIntegrity: false\n    },\n    isSpaMode: false,\n    ssr: true,\n    criticalCss: \"\",\n    manifest: {\n      routes: {},\n      version: \"1\",\n      url: \"\",\n      entry: {\n        module: \"\",\n        imports: []\n      }\n    },\n    routeDiscovery: { mode: \"lazy\", manifestPath: \"/__manifest\" },\n    routeModules: createRSCRouteModules(payload)\n  };\n  return /* @__PURE__ */ React5.createElement(RSCRouterContext.Provider, { value: true }, /* @__PURE__ */ React5.createElement(RSCRouterGlobalErrorBoundary, { location: payload.location }, /* @__PURE__ */ React5.createElement(FrameworkContext.Provider, { value: frameworkContext }, /* @__PURE__ */ React5.createElement(\n    StaticRouterProvider,\n    {\n      context,\n      router,\n      hydrate: false,\n      nonce: payload.nonce\n    }\n  ))));\n}\nfunction isReactServerRequest(url) {\n  return url.pathname.endsWith(\".rsc\");\n}\nfunction isManifestRequest(url) {\n  return url.pathname.endsWith(\".manifest\");\n}\n\n// lib/rsc/html-stream/browser.ts\nfunction getRSCStream() {\n  let encoder3 = new TextEncoder();\n  let streamController = null;\n  let rscStream = new ReadableStream({\n    start(controller) {\n      if (typeof window === \"undefined\") {\n        return;\n      }\n      let handleChunk = (chunk) => {\n        if (typeof chunk === \"string\") {\n          controller.enqueue(encoder3.encode(chunk));\n        } else {\n          controller.enqueue(chunk);\n        }\n      };\n      window.__FLIGHT_DATA || (window.__FLIGHT_DATA = []);\n      window.__FLIGHT_DATA.forEach(handleChunk);\n      window.__FLIGHT_DATA.push = (chunk) => {\n        handleChunk(chunk);\n        return 0;\n      };\n      streamController = controller;\n    }\n  });\n  if (typeof document !== \"undefined\" && document.readyState === \"loading\") {\n    document.addEventListener(\"DOMContentLoaded\", () => {\n      streamController?.close();\n    });\n  } else {\n    streamController?.close();\n  }\n  return rscStream;\n}\n\n// lib/dom/ssr/errors.ts\nfunction deserializeErrors(errors) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    if (val && val.__type === \"RouteErrorResponse\") {\n      serialized[key] = new ErrorResponseImpl(\n        val.status,\n        val.statusText,\n        val.data,\n        val.internal === true\n      );\n    } else if (val && val.__type === \"Error\") {\n      if (val.__subType) {\n        let ErrorConstructor = window[val.__subType];\n        if (typeof ErrorConstructor === \"function\") {\n          try {\n            let error = new ErrorConstructor(val.message);\n            error.stack = val.stack;\n            serialized[key] = error;\n          } catch (e) {\n          }\n        }\n      }\n      if (serialized[key] == null) {\n        let error = new Error(val.message);\n        error.stack = val.stack;\n        serialized[key] = error;\n      }\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n\nexport {\n  ServerRouter,\n  createRoutesStub,\n  createCookie,\n  isCookie,\n  ServerMode,\n  setDevServerHooks,\n  createRequestHandler,\n  createSession,\n  isSession,\n  createSessionStorage,\n  createCookieSessionStorage,\n  createMemorySessionStorage,\n  href,\n  getHydrationData,\n  RSCDefaultRootErrorBoundary,\n  createCallServer,\n  RSCHydratedRouter,\n  routeRSCServerRequest,\n  RSCStaticRouter,\n  getRSCStream,\n  deserializeErrors\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SACEA,mBAAmB,EACnBC,iBAAiB,EACjBC,gBAAgB,EAChBC,oBAAoB,EACpBC,MAAM,EACNC,gBAAgB,EAChBC,kBAAkB,EAClBC,qBAAqB,EACrBC,cAAc,EACdC,4BAA4B,EAC5BC,yBAAyB,EACzBC,oBAAoB,EACpBC,cAAc,EACdC,wCAAwC,EACxCC,yBAAyB,EACzBC,oBAAoB,EACpBC,aAAa,EACbC,kBAAkB,EAClBC,iBAAiB,EACjBC,YAAY,EACZC,kBAAkB,EAClBC,mBAAmB,EACnBC,kBAAkB,EAClBC,oBAAoB,EACpBC,MAAM,EACNC,UAAU,EACVC,eAAe,EACfC,8BAA8B,EAC9BC,yBAAyB,EACzBC,SAAS,EACTC,sBAAsB,EACtBC,gBAAgB,EAChBC,kBAAkB,EAClBC,oBAAoB,EACpBC,UAAU,EACVC,oBAAoB,EACpBC,WAAW,EACXC,oBAAoB,EACpBC,QAAQ,EACRC,gBAAgB,EAChBC,OAAO,EACPC,aAAa,EACbC,wBAAwB,EACxBC,cAAc,EACdC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,QAAQ,EACRC,kBAAkB,EAClBC,sBAAsB,EACtBC,wBAAwB,QACnB,sBAAsB;;AAE7B;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAYA,CAAC;EACpBC,OAAO;EACPC,GAAG;EACHC;AACF,CAAC,EAAE;EACD,IAAI,OAAOD,GAAG,KAAK,QAAQ,EAAE;IAC3BA,GAAG,GAAG,IAAIE,GAAG,CAACF,GAAG,CAAC;EACpB;EACA,IAAI;IAAEG,QAAQ;IAAEC,YAAY;IAAEC,WAAW;IAAEC;EAAoB,CAAC,GAAGP,OAAO;EAC1E,IAAIQ,MAAM,GAAGzC,kBAAkB,CAC7BqC,QAAQ,CAACI,MAAM,EACfH,YAAY,EACZL,OAAO,CAACS,MAAM,EACdT,OAAO,CAACU,SACV,CAAC;EACDV,OAAO,CAACW,oBAAoB,CAACC,UAAU,GAAG;IACxC,GAAGZ,OAAO,CAACW,oBAAoB,CAACC;EAClC,CAAC;EACD,KAAK,IAAIC,KAAK,IAAIb,OAAO,CAACW,oBAAoB,CAACG,OAAO,EAAE;IACtD,IAAIC,OAAO,GAAGF,KAAK,CAACG,KAAK,CAACC,EAAE;IAC5B,IAAID,KAAK,GAAGX,YAAY,CAACU,OAAO,CAAC;IACjC,IAAIG,aAAa,GAAGlB,OAAO,CAACI,QAAQ,CAACI,MAAM,CAACO,OAAO,CAAC;IACpD,IAAIC,KAAK,IAAIE,aAAa,IAAI7B,wBAAwB,CACpD0B,OAAO,EACPC,KAAK,CAACG,YAAY,EAClBD,aAAa,CAACE,SAAS,EACvBpB,OAAO,CAACU,SACV,CAAC,KAAKM,KAAK,CAACK,eAAe,IAAI,CAACH,aAAa,CAACE,SAAS,CAAC,EAAE;MACxD,OAAOpB,OAAO,CAACW,oBAAoB,CAACC,UAAU,CAACG,OAAO,CAAC;IACzD;EACF;EACA,IAAIO,MAAM,GAAGrD,kBAAkB,CAACuC,MAAM,EAAER,OAAO,CAACW,oBAAoB,CAAC;EACrE,OAAO,eAAgBb,KAAK,CAACyB,aAAa,CAACzB,KAAK,CAAC0B,QAAQ,EAAE,IAAI,EAAE,eAAgB1B,KAAK,CAACyB,aAAa,CAClG1E,gBAAgB,CAAC4E,QAAQ,EACzB;IACEC,KAAK,EAAE;MACLtB,QAAQ;MACRC,YAAY;MACZC,WAAW;MACXC,mBAAmB;MACnBE,MAAM,EAAET,OAAO,CAACS,MAAM;MACtBkB,GAAG,EAAE3B,OAAO,CAAC2B,GAAG;MAChBjB,SAAS,EAAEV,OAAO,CAACU,SAAS;MAC5BkB,cAAc,EAAE5B,OAAO,CAAC4B,cAAc;MACtCC,cAAc,EAAE7B,OAAO,CAAC6B,cAAc;MACtCC,UAAU,EAAE9B,OAAO,CAAC8B;IACtB;EACF,CAAC,EACD,eAAgBhC,KAAK,CAACyB,aAAa,CAACtE,kBAAkB,EAAE;IAAE8E,QAAQ,EAAET,MAAM,CAACU,KAAK,CAACD;EAAS,CAAC,EAAE,eAAgBjC,KAAK,CAACyB,aAAa,CAC9HjE,oBAAoB,EACpB;IACEgE,MAAM;IACNtB,OAAO,EAAEA,OAAO,CAACW,oBAAoB;IACrCsB,OAAO,EAAE;EACX,CACF,CAAC,CACH,CAAC,EAAEjC,OAAO,CAACkC,mBAAmB,GAAG,eAAgBpC,KAAK,CAACyB,aAAa,CAACzB,KAAK,CAACqC,QAAQ,EAAE,IAAI,EAAE,eAAgBrC,KAAK,CAACyB,aAAa,CAC5HhE,cAAc,EACd;IACEyC,OAAO;IACPoC,UAAU,EAAE,CAAC;IACbC,MAAM,EAAErC,OAAO,CAACkC,mBAAmB,CAACI,SAAS,CAAC,CAAC;IAC/CC,WAAW,EAAE,IAAIC,WAAW,CAAC,CAAC;IAC9BtC;EACF,CACF,CAAC,CAAC,GAAG,IAAI,CAAC;AACZ;;AAEA;AACA,OAAO,KAAKuC,MAAM,MAAM,OAAO;AAC/B,SAASC,gBAAgBA,CAAClC,MAAM,EAAEmC,QAAQ,EAAE;EAC1C,OAAO,SAASC,cAAcA,CAAC;IAC7BC,cAAc;IACdC,YAAY;IACZC,aAAa;IACbtC;EACF,CAAC,EAAE;IACD,IAAIuC,SAAS,GAAGP,MAAM,CAACQ,MAAM,CAAC,CAAC;IAC/B,IAAIC,mBAAmB,GAAGT,MAAM,CAACQ,MAAM,CAAC,CAAC;IACzC,IAAID,SAAS,CAACG,OAAO,IAAI,IAAI,EAAE;MAC7BD,mBAAmB,CAACC,OAAO,GAAG;QAC5B1C,MAAM,EAAE;UACN2C,6BAA6B,EAAE3C,MAAM,EAAE2C,6BAA6B,KAAK,IAAI;UAC7EC,aAAa,EAAE5C,MAAM,EAAE4C,aAAa,KAAK;QAC3C,CAAC;QACDjD,QAAQ,EAAE;UACRI,MAAM,EAAE,CAAC,CAAC;UACV8C,KAAK,EAAE;YAAEC,OAAO,EAAE,EAAE;YAAEC,MAAM,EAAE;UAAG,CAAC;UAClCvD,GAAG,EAAE,EAAE;UACPwD,OAAO,EAAE;QACX,CAAC;QACDpD,YAAY,EAAE,CAAC,CAAC;QAChBsB,GAAG,EAAE,KAAK;QACVjB,SAAS,EAAE,KAAK;QAChBkB,cAAc,EAAE;UAAE8B,IAAI,EAAE,MAAM;UAAEC,YAAY,EAAE;QAAc;MAC9D,CAAC;MACD,IAAIC,OAAO,GAAGC,aAAa;MACzB;MACA;MACApG,yBAAyB,CAAC+C,MAAM,EAAGsD,CAAC,IAAKA,CAAC,CAAC,EAC3CnB,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAGlC,MAAM,EAAE4C,aAAa,GAAG,IAAInG,qBAAqB,CAAC,CAAC,GAAG,CAAC,CAAC,EACzFgG,mBAAmB,CAACC,OAAO,CAAC/C,QAAQ,EACpC8C,mBAAmB,CAACC,OAAO,CAAC9C,YAC9B,CAAC;MACD2C,SAAS,CAACG,OAAO,GAAGvF,kBAAkB,CAACgG,OAAO,EAAE;QAC9Cf,cAAc;QACdC,YAAY;QACZC;MACF,CAAC,CAAC;IACJ;IACA,OAAO,eAAgBN,MAAM,CAAClB,aAAa,CAAC1E,gBAAgB,CAAC4E,QAAQ,EAAE;MAAEC,KAAK,EAAEwB,mBAAmB,CAACC;IAAQ,CAAC,EAAE,eAAgBV,MAAM,CAAClB,aAAa,CAACpE,cAAc,EAAE;MAAEmE,MAAM,EAAE0B,SAAS,CAACG;IAAQ,CAAC,CAAC,CAAC;EACrM,CAAC;AACH;AACA,SAASU,aAAaA,CAACrD,MAAM,EAAER,OAAO,EAAEI,QAAQ,EAAEC,YAAY,EAAE0D,QAAQ,EAAE;EACxE,OAAOvD,MAAM,CAACwD,GAAG,CAAEhD,KAAK,IAAK;IAC3B,IAAI,CAACA,KAAK,CAACC,EAAE,EAAE;MACb,MAAM,IAAIgD,KAAK,CACb,8DACF,CAAC;IACH;IACA,IAAIC,QAAQ,GAAG;MACbjD,EAAE,EAAED,KAAK,CAACC,EAAE;MACZkD,IAAI,EAAEnD,KAAK,CAACmD,IAAI;MAChBC,KAAK,EAAEpD,KAAK,CAACoD,KAAK;MAClBC,SAAS,EAAErD,KAAK,CAACqD,SAAS,GAAG1E,kBAAkB,CAACqB,KAAK,CAACqD,SAAS,CAAC,GAAG,KAAK,CAAC;MACzEhD,eAAe,EAAEL,KAAK,CAACK,eAAe,GAAGxB,wBAAwB,CAACmB,KAAK,CAACK,eAAe,CAAC,GAAG,KAAK,CAAC;MACjGiD,aAAa,EAAEtD,KAAK,CAACsD,aAAa,GAAG1E,sBAAsB,CAACoB,KAAK,CAACsD,aAAa,CAAC,GAAG,KAAK,CAAC;MACzFC,MAAM,EAAEvD,KAAK,CAACuD,MAAM,GAAIC,IAAI,IAAKxD,KAAK,CAACuD,MAAM,CAAC;QAAE,GAAGC,IAAI;QAAExE;MAAQ,CAAC,CAAC,GAAG,KAAK,CAAC;MAC5EyE,MAAM,EAAEzD,KAAK,CAACyD,MAAM,GAAID,IAAI,IAAKxD,KAAK,CAACyD,MAAM,CAAC;QAAE,GAAGD,IAAI;QAAExE;MAAQ,CAAC,CAAC,GAAG,KAAK,CAAC;MAC5E0E,UAAU,EAAE1D,KAAK,CAAC0D,UAAU,GAAG1D,KAAK,CAAC0D,UAAU,CAACV,GAAG,CAChDW,EAAE,IAAK,CAAC,GAAGH,IAAI,KAAKG,EAAE,CACrB;QAAE,GAAGH,IAAI,CAAC,CAAC,CAAC;QAAExE;MAAQ,CAAC,EACvBwE,IAAI,CAAC,CAAC,CACR,CACF,CAAC,GAAG,KAAK,CAAC;MACVI,MAAM,EAAE5D,KAAK,CAAC4D,MAAM;MACpBC,gBAAgB,EAAE7D,KAAK,CAAC6D;IAC1B,CAAC;IACD,IAAIC,UAAU,GAAG;MACf7D,EAAE,EAAED,KAAK,CAACC,EAAE;MACZkD,IAAI,EAAEnD,KAAK,CAACmD,IAAI;MAChBC,KAAK,EAAEpD,KAAK,CAACoD,KAAK;MAClBL,QAAQ;MACRgB,SAAS,EAAE/D,KAAK,CAACuD,MAAM,IAAI,IAAI;MAC/BnD,SAAS,EAAEJ,KAAK,CAACyD,MAAM,IAAI,IAAI;MAC/B;MACA;MACA;MACAO,eAAe,EAAE,KAAK;MACtBC,eAAe,EAAE,KAAK;MACtBC,mBAAmB,EAAE,KAAK;MAC1BC,gBAAgB,EAAEnE,KAAK,CAACsD,aAAa,IAAI,IAAI;MAC7C;MACAd,MAAM,EAAE,8BAA8B;MACtC4B,kBAAkB,EAAE,KAAK,CAAC;MAC1BC,kBAAkB,EAAE,KAAK,CAAC;MAC1BC,sBAAsB,EAAE,KAAK,CAAC;MAC9BC,qBAAqB,EAAE,KAAK;IAC9B,CAAC;IACDnF,QAAQ,CAACI,MAAM,CAAC0D,QAAQ,CAACjD,EAAE,CAAC,GAAG6D,UAAU;IACzCzE,YAAY,CAACW,KAAK,CAACC,EAAE,CAAC,GAAG;MACvBuE,OAAO,EAAEtB,QAAQ,CAACG,SAAS,IAAItH,MAAM;MACrCuH,aAAa,EAAEJ,QAAQ,CAACI,aAAa,IAAI,KAAK,CAAC;MAC/CM,MAAM,EAAE5D,KAAK,CAAC4D,MAAM;MACpBa,KAAK,EAAEzE,KAAK,CAACyE,KAAK;MAClBC,IAAI,EAAE1E,KAAK,CAAC0E,IAAI;MAChBb,gBAAgB,EAAE7D,KAAK,CAAC6D;IAC1B,CAAC;IACD,IAAI7D,KAAK,CAAC2E,QAAQ,EAAE;MAClBzB,QAAQ,CAACyB,QAAQ,GAAG9B,aAAa,CAC/B7C,KAAK,CAAC2E,QAAQ,EACd3F,OAAO,EACPI,QAAQ,EACRC,YAAY,EACZ6D,QAAQ,CAACjD,EACX,CAAC;IACH;IACA,OAAOiD,QAAQ;EACjB,CAAC,CAAC;AACJ;;AAEA;AACA,SAAS0B,KAAK,EAAEC,SAAS,QAAQ,QAAQ;;AAEzC;AACA,IAAIC,OAAO,GAAG,eAAgB,IAAIC,WAAW,CAAC,CAAC;AAC/C,IAAIC,IAAI,GAAG,MAAAA,CAAOtE,KAAK,EAAEuE,MAAM,KAAK;EAClC,IAAIC,KAAK,GAAGJ,OAAO,CAAC3H,MAAM,CAACuD,KAAK,CAAC;EACjC,IAAIyE,GAAG,GAAG,MAAMC,SAAS,CAACH,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC;EAC3C,IAAII,SAAS,GAAG,MAAMC,MAAM,CAACC,MAAM,CAACP,IAAI,CAAC,MAAM,EAAEG,GAAG,EAAED,KAAK,CAAC;EAC5D,IAAIM,IAAI,GAAGC,IAAI,CAACC,MAAM,CAACC,YAAY,CAAC,GAAG,IAAIC,UAAU,CAACP,SAAS,CAAC,CAAC,CAAC,CAAClH,OAAO,CACxE,KAAK,EACL,EACF,CAAC;EACD,OAAOuC,KAAK,GAAG,GAAG,GAAG8E,IAAI;AAC3B,CAAC;AACD,IAAIK,MAAM,GAAG,MAAAA,CAAOC,MAAM,EAAEb,MAAM,KAAK;EACrC,IAAI7B,KAAK,GAAG0C,MAAM,CAACC,WAAW,CAAC,GAAG,CAAC;EACnC,IAAIrF,KAAK,GAAGoF,MAAM,CAACE,KAAK,CAAC,CAAC,EAAE5C,KAAK,CAAC;EAClC,IAAIoC,IAAI,GAAGM,MAAM,CAACE,KAAK,CAAC5C,KAAK,GAAG,CAAC,CAAC;EAClC,IAAI8B,KAAK,GAAGJ,OAAO,CAAC3H,MAAM,CAACuD,KAAK,CAAC;EACjC,IAAIyE,GAAG,GAAG,MAAMC,SAAS,CAACH,MAAM,EAAE,CAAC,QAAQ,CAAC,CAAC;EAC7C,IAAI;IACF,IAAII,SAAS,GAAGY,sBAAsB,CAACC,IAAI,CAACV,IAAI,CAAC,CAAC;IAClD,IAAIW,KAAK,GAAG,MAAMb,MAAM,CAACC,MAAM,CAACa,MAAM,CAAC,MAAM,EAAEjB,GAAG,EAAEE,SAAS,EAAEH,KAAK,CAAC;IACrE,OAAOiB,KAAK,GAAGzF,KAAK,GAAG,KAAK;EAC9B,CAAC,CAAC,OAAO2F,KAAK,EAAE;IACd,OAAO,KAAK;EACd;AACF,CAAC;AACD,IAAIjB,SAAS,GAAG,MAAAA,CAAOH,MAAM,EAAEqB,MAAM,KAAKhB,MAAM,CAACC,MAAM,CAACgB,SAAS,CAC/D,KAAK,EACLzB,OAAO,CAAC3H,MAAM,CAAC8H,MAAM,CAAC,EACtB;EAAEuB,IAAI,EAAE,MAAM;EAAEhB,IAAI,EAAE;AAAU,CAAC,EACjC,KAAK,EACLc,MACF,CAAC;AACD,SAASL,sBAAsBA,CAACQ,UAAU,EAAE;EAC1C,IAAIC,KAAK,GAAG,IAAId,UAAU,CAACa,UAAU,CAACE,MAAM,CAAC;EAC7C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,UAAU,CAACE,MAAM,EAAEC,CAAC,EAAE,EAAE;IAC1CF,KAAK,CAACE,CAAC,CAAC,GAAGH,UAAU,CAACI,UAAU,CAACD,CAAC,CAAC;EACrC;EACA,OAAOF,KAAK;AACd;;AAEA;AACA,IAAII,YAAY,GAAGA,CAACN,IAAI,EAAEO,aAAa,GAAG,CAAC,CAAC,KAAK;EAC/C,IAAI;IAAEC,OAAO,GAAG,EAAE;IAAE,GAAGC;EAAQ,CAAC,GAAG;IACjC9D,IAAI,EAAE,GAAG;IACT+D,QAAQ,EAAE,KAAK;IACf,GAAGH;EACL,CAAC;EACDI,0BAA0B,CAACX,IAAI,EAAES,OAAO,CAACG,OAAO,CAAC;EACjD,OAAO;IACL,IAAIZ,IAAIA,CAAA,EAAG;MACT,OAAOA,IAAI;IACb,CAAC;IACD,IAAIa,QAAQA,CAAA,EAAG;MACb,OAAOL,OAAO,CAACL,MAAM,GAAG,CAAC;IAC3B,CAAC;IACD,IAAIS,OAAOA,CAAA,EAAG;MACZ,OAAO,OAAOH,OAAO,CAACK,MAAM,KAAK,WAAW,GAAG,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGP,OAAO,CAACK,MAAM,GAAG,GAAG,CAAC,GAAGL,OAAO,CAACG,OAAO;IAC9G,CAAC;IACD,MAAMxC,KAAKA,CAAC6C,YAAY,EAAEC,YAAY,EAAE;MACtC,IAAI,CAACD,YAAY,EAAE,OAAO,IAAI;MAC9B,IAAIE,OAAO,GAAG/C,KAAK,CAAC6C,YAAY,EAAE;QAAE,GAAGR,OAAO;QAAE,GAAGS;MAAa,CAAC,CAAC;MAClE,IAAIlB,IAAI,IAAImB,OAAO,EAAE;QACnB,IAAIjH,KAAK,GAAGiH,OAAO,CAACnB,IAAI,CAAC;QACzB,IAAI,OAAO9F,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,EAAE,EAAE;UAC7C,IAAIkH,OAAO,GAAG,MAAMC,iBAAiB,CAACnH,KAAK,EAAEsG,OAAO,CAAC;UACrD,OAAOY,OAAO;QAChB,CAAC,MAAM;UACL,OAAO,EAAE;QACX;MACF,CAAC,MAAM;QACL,OAAO,IAAI;MACb;IACF,CAAC;IACD,MAAM/C,SAASA,CAACnE,KAAK,EAAEoH,gBAAgB,EAAE;MACvC,OAAOjD,SAAS,CACd2B,IAAI,EACJ9F,KAAK,KAAK,EAAE,GAAG,EAAE,GAAG,MAAMqH,iBAAiB,CAACrH,KAAK,EAAEsG,OAAO,CAAC,EAC3D;QACE,GAAGC,OAAO;QACV,GAAGa;MACL,CACF,CAAC;IACH;EACF,CAAC;AACH,CAAC;AACD,IAAIE,QAAQ,GAAIC,MAAM,IAAK;EACzB,OAAOA,MAAM,IAAI,IAAI,IAAI,OAAOA,MAAM,CAACzB,IAAI,KAAK,QAAQ,IAAI,OAAOyB,MAAM,CAACZ,QAAQ,KAAK,SAAS,IAAI,OAAOY,MAAM,CAACrD,KAAK,KAAK,UAAU,IAAI,OAAOqD,MAAM,CAACpD,SAAS,KAAK,UAAU;AAClL,CAAC;AACD,eAAekD,iBAAiBA,CAACrH,KAAK,EAAEsG,OAAO,EAAE;EAC/C,IAAIkB,OAAO,GAAGC,UAAU,CAACzH,KAAK,CAAC;EAC/B,IAAIsG,OAAO,CAACL,MAAM,GAAG,CAAC,EAAE;IACtBuB,OAAO,GAAG,MAAMlD,IAAI,CAACkD,OAAO,EAAElB,OAAO,CAAC,CAAC,CAAC,CAAC;EAC3C;EACA,OAAOkB,OAAO;AAChB;AACA,eAAeL,iBAAiBA,CAACnH,KAAK,EAAEsG,OAAO,EAAE;EAC/C,IAAIA,OAAO,CAACL,MAAM,GAAG,CAAC,EAAE;IACtB,KAAK,IAAI1B,MAAM,IAAI+B,OAAO,EAAE;MAC1B,IAAIoB,aAAa,GAAG,MAAMvC,MAAM,CAACnF,KAAK,EAAEuE,MAAM,CAAC;MAC/C,IAAImD,aAAa,KAAK,KAAK,EAAE;QAC3B,OAAOC,UAAU,CAACD,aAAa,CAAC;MAClC;IACF;IACA,OAAO,IAAI;EACb;EACA,OAAOC,UAAU,CAAC3H,KAAK,CAAC;AAC1B;AACA,SAASyH,UAAUA,CAACzH,KAAK,EAAE;EACzB,OAAO+E,IAAI,CAAC6C,UAAU,CAACC,kBAAkB,CAACC,IAAI,CAACC,SAAS,CAAC/H,KAAK,CAAC,CAAC,CAAC,CAAC;AACpE;AACA,SAAS2H,UAAUA,CAAC3H,KAAK,EAAE;EACzB,IAAI;IACF,OAAO8H,IAAI,CAAC5D,KAAK,CAAC8D,kBAAkB,CAACC,QAAQ,CAACzC,IAAI,CAACxF,KAAK,CAAC,CAAC,CAAC,CAAC;EAC9D,CAAC,CAAC,OAAO2F,KAAK,EAAE;IACd,OAAO,CAAC,CAAC;EACX;AACF;AACA,SAASsC,QAAQA,CAACjI,KAAK,EAAE;EACvB,IAAIkI,GAAG,GAAGlI,KAAK,CAACmI,QAAQ,CAAC,CAAC;EAC1B,IAAIC,MAAM,GAAG,EAAE;EACf,IAAI1F,KAAK,GAAG,CAAC;EACb,IAAI2F,GAAG,EAAEC,IAAI;EACb,OAAO5F,KAAK,GAAGwF,GAAG,CAACjC,MAAM,EAAE;IACzBoC,GAAG,GAAGH,GAAG,CAACK,MAAM,CAAC7F,KAAK,EAAE,CAAC;IACzB,IAAI,aAAa,CAAC8F,IAAI,CAACH,GAAG,CAAC,EAAE;MAC3BD,MAAM,IAAIC,GAAG;IACf,CAAC,MAAM;MACLC,IAAI,GAAGD,GAAG,CAAClC,UAAU,CAAC,CAAC,CAAC;MACxB,IAAImC,IAAI,GAAG,GAAG,EAAE;QACdF,MAAM,IAAI,GAAG,GAAGK,GAAG,CAACH,IAAI,EAAE,CAAC,CAAC;MAC9B,CAAC,MAAM;QACLF,MAAM,IAAI,IAAI,GAAGK,GAAG,CAACH,IAAI,EAAE,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC;MAC7C;IACF;EACF;EACA,OAAON,MAAM;AACf;AACA,SAASK,GAAGA,CAACH,IAAI,EAAErC,MAAM,EAAE;EACzB,IAAImC,MAAM,GAAGE,IAAI,CAACH,QAAQ,CAAC,EAAE,CAAC;EAC9B,OAAOC,MAAM,CAACnC,MAAM,GAAGA,MAAM,EAAEmC,MAAM,GAAG,GAAG,GAAGA,MAAM;EACpD,OAAOA,MAAM;AACf;AACA,SAASR,UAAUA,CAAC5H,KAAK,EAAE;EACzB,IAAIkI,GAAG,GAAGlI,KAAK,CAACmI,QAAQ,CAAC,CAAC;EAC1B,IAAIC,MAAM,GAAG,EAAE;EACf,IAAI1F,KAAK,GAAG,CAAC;EACb,IAAI2F,GAAG,EAAEM,IAAI;EACb,OAAOjG,KAAK,GAAGwF,GAAG,CAACjC,MAAM,EAAE;IACzBoC,GAAG,GAAGH,GAAG,CAACK,MAAM,CAAC7F,KAAK,EAAE,CAAC;IACzB,IAAI2F,GAAG,KAAK,GAAG,EAAE;MACf,IAAIH,GAAG,CAACK,MAAM,CAAC7F,KAAK,CAAC,KAAK,GAAG,EAAE;QAC7BiG,IAAI,GAAGT,GAAG,CAAC5C,KAAK,CAAC5C,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,CAAC,CAAC;QACtC,IAAI,eAAe,CAAC8F,IAAI,CAACG,IAAI,CAAC,EAAE;UAC9BP,MAAM,IAAIpD,MAAM,CAACC,YAAY,CAAC2D,QAAQ,CAACD,IAAI,EAAE,EAAE,CAAC,CAAC;UACjDjG,KAAK,IAAI,CAAC;UACV;QACF;MACF,CAAC,MAAM;QACLiG,IAAI,GAAGT,GAAG,CAAC5C,KAAK,CAAC5C,KAAK,EAAEA,KAAK,GAAG,CAAC,CAAC;QAClC,IAAI,eAAe,CAAC8F,IAAI,CAACG,IAAI,CAAC,EAAE;UAC9BP,MAAM,IAAIpD,MAAM,CAACC,YAAY,CAAC2D,QAAQ,CAACD,IAAI,EAAE,EAAE,CAAC,CAAC;UACjDjG,KAAK,IAAI,CAAC;UACV;QACF;MACF;IACF;IACA0F,MAAM,IAAIC,GAAG;EACf;EACA,OAAOD,MAAM;AACf;AACA,SAAS3B,0BAA0BA,CAACX,IAAI,EAAEY,OAAO,EAAE;EACjD1I,QAAQ,CACN,CAAC0I,OAAO,EACR,QAAQZ,IAAI,6WACd,CAAC;AACH;;AAEA;AACA,SAAS+C,uBAAuBA,CAACnK,QAAQ,EAAE;EACzC,OAAOoK,MAAM,CAACC,IAAI,CAACrK,QAAQ,CAAC,CAACsK,MAAM,CAAC,CAACC,IAAI,EAAE5J,OAAO,KAAK;IACrD,IAAIC,KAAK,GAAGZ,QAAQ,CAACW,OAAO,CAAC;IAC7B,IAAIC,KAAK,EAAE;MACT2J,IAAI,CAAC5J,OAAO,CAAC,GAAGC,KAAK,CAACwC,MAAM;IAC9B;IACA,OAAOmH,IAAI;EACb,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;;AAEA;AACA,IAAIC,UAAU,GAAG,eAAgB,CAAEC,WAAW,IAAK;EACjDA,WAAW,CAAC,aAAa,CAAC,GAAG,aAAa;EAC1CA,WAAW,CAAC,YAAY,CAAC,GAAG,YAAY;EACxCA,WAAW,CAAC,MAAM,CAAC,GAAG,MAAM;EAC5B,OAAOA,WAAW;AACpB,CAAC,EAAED,UAAU,IAAI,CAAC,CAAC,CAAC;AACpB,SAASE,YAAYA,CAACpJ,KAAK,EAAE;EAC3B,OAAOA,KAAK,KAAK,aAAa,CAAC,qBAAqBA,KAAK,KAAK,YAAY,CAAC,oBAAoBA,KAAK,KAAK,MAAM,CAAC;AAClH;;AAEA;AACA,SAASqJ,aAAaA,CAAC1D,KAAK,EAAE2D,UAAU,EAAE;EACxC,IAAI3D,KAAK,YAAYpD,KAAK,IAAI+G,UAAU,KAAK,aAAa,CAAC,mBAAmB;IAC5E,IAAIC,SAAS,GAAG,IAAIhH,KAAK,CAAC,yBAAyB,CAAC;IACpDgH,SAAS,CAACC,KAAK,GAAG,KAAK,CAAC;IACxB,OAAOD,SAAS;EAClB;EACA,OAAO5D,KAAK;AACd;AACA,SAAS8D,cAAcA,CAACC,MAAM,EAAEJ,UAAU,EAAE;EAC1C,OAAOR,MAAM,CAACa,OAAO,CAACD,MAAM,CAAC,CAACV,MAAM,CAAC,CAACY,GAAG,EAAE,CAACvK,OAAO,EAAEsG,KAAK,CAAC,KAAK;IAC9D,OAAOmD,MAAM,CAACe,MAAM,CAACD,GAAG,EAAE;MAAE,CAACvK,OAAO,GAAGgK,aAAa,CAAC1D,KAAK,EAAE2D,UAAU;IAAE,CAAC,CAAC;EAC5E,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;AACA,SAASnJ,cAAcA,CAACwF,KAAK,EAAE2D,UAAU,EAAE;EACzC,IAAIC,SAAS,GAAGF,aAAa,CAAC1D,KAAK,EAAE2D,UAAU,CAAC;EAChD,OAAO;IACLQ,OAAO,EAAEP,SAAS,CAACO,OAAO;IAC1BN,KAAK,EAAED,SAAS,CAACC;EACnB,CAAC;AACH;AACA,SAASO,eAAeA,CAACL,MAAM,EAAEJ,UAAU,EAAE;EAC3C,IAAI,CAACI,MAAM,EAAE,OAAO,IAAI;EACxB,IAAIC,OAAO,GAAGb,MAAM,CAACa,OAAO,CAACD,MAAM,CAAC;EACpC,IAAIM,UAAU,GAAG,CAAC,CAAC;EACnB,KAAK,IAAI,CAACvF,GAAG,EAAEwF,GAAG,CAAC,IAAIN,OAAO,EAAE;IAC9B,IAAIvM,oBAAoB,CAAC6M,GAAG,CAAC,EAAE;MAC7BD,UAAU,CAACvF,GAAG,CAAC,GAAG;QAAE,GAAGwF,GAAG;QAAEC,MAAM,EAAE;MAAqB,CAAC;IAC5D,CAAC,MAAM,IAAID,GAAG,YAAY1H,KAAK,EAAE;MAC/B,IAAIgH,SAAS,GAAGF,aAAa,CAACY,GAAG,EAAEX,UAAU,CAAC;MAC9CU,UAAU,CAACvF,GAAG,CAAC,GAAG;QAChBqF,OAAO,EAAEP,SAAS,CAACO,OAAO;QAC1BN,KAAK,EAAED,SAAS,CAACC,KAAK;QACtBU,MAAM,EAAE,OAAO;QACf;QACA;QACA;QACA;QACA,IAAGX,SAAS,CAACzD,IAAI,KAAK,OAAO,GAAG;UAC9BqE,SAAS,EAAEZ,SAAS,CAACzD;QACvB,CAAC,GAAG,CAAC,CAAC;MACR,CAAC;IACH,CAAC,MAAM;MACLkE,UAAU,CAACvF,GAAG,CAAC,GAAGwF,GAAG;IACvB;EACF;EACA,OAAOD,UAAU;AACnB;;AAEA;AACA,SAASI,iBAAiBA,CAACtL,MAAM,EAAEuL,QAAQ,EAAEC,QAAQ,EAAE;EACrD,IAAIlL,OAAO,GAAG/B,WAAW,CACvByB,MAAM,EACNuL,QAAQ,EACRC,QACF,CAAC;EACD,IAAI,CAAClL,OAAO,EAAE,OAAO,IAAI;EACzB,OAAOA,OAAO,CAACkD,GAAG,CAAEnD,KAAK,KAAM;IAC7BoL,MAAM,EAAEpL,KAAK,CAACoL,MAAM;IACpBF,QAAQ,EAAElL,KAAK,CAACkL,QAAQ;IACxB/K,KAAK,EAAEH,KAAK,CAACG;EACf,CAAC,CAAC,CAAC;AACL;;AAEA;AACA,eAAekL,gBAAgBA,CAACC,OAAO,EAAE3H,IAAI,EAAE;EAC7C,IAAIsF,MAAM,GAAG,MAAMqC,OAAO,CAAC;IACzBC,OAAO,EAAEC,gBAAgB,CAACC,gBAAgB,CAAC9H,IAAI,CAAC4H,OAAO,CAAC,CAAC;IACzDH,MAAM,EAAEzH,IAAI,CAACyH,MAAM;IACnBjM,OAAO,EAAEwE,IAAI,CAACxE;EAChB,CAAC,CAAC;EACF,IAAIvB,sBAAsB,CAACqL,MAAM,CAAC,IAAIA,MAAM,CAACyC,IAAI,IAAIzC,MAAM,CAACyC,IAAI,CAACC,MAAM,IAAI5N,oBAAoB,CAACkL,MAAM,CAACyC,IAAI,CAACC,MAAM,CAAC,EAAE;IACnH,MAAM,IAAIC,QAAQ,CAAC,IAAI,EAAE3C,MAAM,CAACyC,IAAI,CAAC;EACvC;EACA,OAAOzC,MAAM;AACf;AACA,SAASwC,gBAAgBA,CAACF,OAAO,EAAE;EACjC,IAAInM,GAAG,GAAG,IAAIE,GAAG,CAACiM,OAAO,CAACnM,GAAG,CAAC;EAC9B,IAAIyM,WAAW,GAAGzM,GAAG,CAAC0M,YAAY,CAACC,MAAM,CAAC,OAAO,CAAC;EAClD3M,GAAG,CAAC0M,YAAY,CAACE,MAAM,CAAC,OAAO,CAAC;EAChC,IAAIC,iBAAiB,GAAG,EAAE;EAC1B,KAAK,IAAIC,UAAU,IAAIL,WAAW,EAAE;IAClC,IAAIK,UAAU,EAAE;MACdD,iBAAiB,CAACE,IAAI,CAACD,UAAU,CAAC;IACpC;EACF;EACA,KAAK,IAAIE,MAAM,IAAIH,iBAAiB,EAAE;IACpC7M,GAAG,CAAC0M,YAAY,CAACO,MAAM,CAAC,OAAO,EAAED,MAAM,CAAC;EAC1C;EACA,IAAIV,IAAI,GAAG;IACTY,MAAM,EAAEf,OAAO,CAACe,MAAM;IACtBC,IAAI,EAAEhB,OAAO,CAACgB,IAAI;IAClBC,OAAO,EAAEjB,OAAO,CAACiB,OAAO;IACxBC,MAAM,EAAElB,OAAO,CAACkB;EAClB,CAAC;EACD,IAAIf,IAAI,CAACa,IAAI,EAAE;IACbb,IAAI,CAACgB,MAAM,GAAG,MAAM;EACtB;EACA,OAAO,IAAIC,OAAO,CAACvN,GAAG,CAACwN,IAAI,EAAElB,IAAI,CAAC;AACpC;AACA,SAASF,gBAAgBA,CAACD,OAAO,EAAE;EACjC,IAAInM,GAAG,GAAG,IAAIE,GAAG,CAACiM,OAAO,CAACnM,GAAG,CAAC;EAC9BA,GAAG,CAAC0M,YAAY,CAACE,MAAM,CAAC,SAAS,CAAC;EAClC,IAAIN,IAAI,GAAG;IACTY,MAAM,EAAEf,OAAO,CAACe,MAAM;IACtBC,IAAI,EAAEhB,OAAO,CAACgB,IAAI;IAClBC,OAAO,EAAEjB,OAAO,CAACiB,OAAO;IACxBC,MAAM,EAAElB,OAAO,CAACkB;EAClB,CAAC;EACD,IAAIf,IAAI,CAACa,IAAI,EAAE;IACbb,IAAI,CAACgB,MAAM,GAAG,MAAM;EACtB;EACA,OAAO,IAAIC,OAAO,CAACvN,GAAG,CAACwN,IAAI,EAAElB,IAAI,CAAC;AACpC;;AAEA;AACA,SAASmB,UAAUA,CAAChM,KAAK,EAAE8J,OAAO,EAAE;EAClC,IAAI9J,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,WAAW,EAAE;IACrEiM,OAAO,CAACtG,KAAK,CACX,iIACF,CAAC;IACD,MAAM,IAAIpD,KAAK,CAACuH,OAAO,CAAC;EAC1B;AACF;;AAEA;AACA,IAAIoC,uBAAuB,GAAG,6BAA6B;AAC3D,SAASC,iBAAiBA,CAACC,cAAc,EAAE;EACzCC,UAAU,CAACH,uBAAuB,CAAC,GAAGE,cAAc;AACtD;AACA,SAASE,iBAAiBA,CAAA,EAAG;EAC3B,OAAOD,UAAU,CAACH,uBAAuB,CAAC;AAC5C;AACA,SAASK,kBAAkBA,CAAC7B,OAAO,EAAE8B,UAAU,EAAE;EAC/C,IAAI,OAAOC,OAAO,KAAK,WAAW,EAAE;IAClC,IAAI;MACF,IAAIA,OAAO,CAACC,GAAG,EAAEC,mBAAmB,KAAK,KAAK,EAAE;QAC9C,OAAOjC,OAAO,CAACiB,OAAO,CAACiB,GAAG,CAACJ,UAAU,CAAC;MACxC;IACF,CAAC,CAAC,OAAOK,CAAC,EAAE,CACZ;EACF;EACA,OAAO,IAAI;AACb;;AAEA;AACA,SAASC,qBAAqBA,CAACpO,QAAQ,EAAE;EACvC,IAAII,MAAM,GAAG,CAAC,CAAC;EACfgK,MAAM,CAACiE,MAAM,CAACrO,QAAQ,CAAC,CAACsO,OAAO,CAAE1N,KAAK,IAAK;IACzC,IAAIA,KAAK,EAAE;MACT,IAAI+C,QAAQ,GAAG/C,KAAK,CAAC+C,QAAQ,IAAI,EAAE;MACnC,IAAI,CAACvD,MAAM,CAACuD,QAAQ,CAAC,EAAE;QACrBvD,MAAM,CAACuD,QAAQ,CAAC,GAAG,EAAE;MACvB;MACAvD,MAAM,CAACuD,QAAQ,CAAC,CAACiJ,IAAI,CAAChM,KAAK,CAAC;IAC9B;EACF,CAAC,CAAC;EACF,OAAOR,MAAM;AACf;AACA,SAASmO,YAAYA,CAACvO,QAAQ,EAAE2D,QAAQ,GAAG,EAAE,EAAE6K,gBAAgB,GAAGJ,qBAAqB,CAACpO,QAAQ,CAAC,EAAE;EACjG,OAAO,CAACwO,gBAAgB,CAAC7K,QAAQ,CAAC,IAAI,EAAE,EAAEC,GAAG,CAAEhD,KAAK,KAAM;IACxD,GAAGA,KAAK;IACR2E,QAAQ,EAAEgJ,YAAY,CAACvO,QAAQ,EAAEY,KAAK,CAACC,EAAE,EAAE2N,gBAAgB;EAC7D,CAAC,CAAC,CAAC;AACL;AACA,SAASC,6BAA6BA,CAACzO,QAAQ,EAAEK,MAAM,EAAEsD,QAAQ,GAAG,EAAE,EAAE6K,gBAAgB,GAAGJ,qBAAqB,CAACpO,QAAQ,CAAC,EAAE;EAC1H,OAAO,CAACwO,gBAAgB,CAAC7K,QAAQ,CAAC,IAAI,EAAE,EAAEC,GAAG,CAAEhD,KAAK,IAAK;IACvD,IAAI8N,WAAW,GAAG;MAChB;MACA3J,gBAAgB,EAAEnE,KAAK,CAACC,EAAE,KAAK,MAAM,IAAID,KAAK,CAACwC,MAAM,CAACc,aAAa,IAAI,IAAI;MAC3ErD,EAAE,EAAED,KAAK,CAACC,EAAE;MACZkD,IAAI,EAAEnD,KAAK,CAACmD,IAAI;MAChBO,UAAU,EAAE1D,KAAK,CAACwC,MAAM,CAACkB,UAAU;MACnC;MACA;MACAD,MAAM,EAAEzD,KAAK,CAACwC,MAAM,CAACiB,MAAM,GAAG,MAAOD,IAAI,IAAK;QAC5C,IAAIuK,eAAe,GAAGd,kBAAkB,CACtCzJ,IAAI,CAAC4H,OAAO,EACZ,+BACF,CAAC;QACD,IAAI2C,eAAe,IAAI,IAAI,EAAE;UAC3B,IAAI7F,OAAO,GAAG6F,eAAe,GAAGC,SAAS,CAACD,eAAe,CAAC,GAAGA,eAAe;UAC5ErB,UAAU,CAACxE,OAAO,EAAE,oCAAoC,CAAC;UACzD,IAAI+F,UAAU,GAAG,IAAIlJ,WAAW,CAAC,CAAC,CAAC5H,MAAM,CAAC+K,OAAO,CAAC;UAClD,IAAIgG,MAAM,GAAG,IAAIC,cAAc,CAAC;YAC9BC,KAAKA,CAACC,UAAU,EAAE;cAChBA,UAAU,CAACC,OAAO,CAACL,UAAU,CAAC;cAC9BI,UAAU,CAACE,KAAK,CAAC,CAAC;YACpB;UACF,CAAC,CAAC;UACF,IAAI3G,OAAO,GAAG,MAAM1K,oBAAoB,CAACgR,MAAM,EAAEM,MAAM,CAAC;UACxD,IAAItJ,KAAK,GAAG0C,OAAO,CAAClH,KAAK;UACzB,IAAIwE,KAAK,IAAI7I,yBAAyB,IAAI6I,KAAK,EAAE;YAC/C,IAAI4D,MAAM,GAAG5D,KAAK,CAAC7I,yBAAyB,CAAC;YAC7C,IAAIkP,IAAI,GAAG;cAAEC,MAAM,EAAE1C,MAAM,CAAC0C;YAAO,CAAC;YACpC,IAAI1C,MAAM,CAAC2F,MAAM,EAAE;cACjB,MAAMvQ,gBAAgB,CAAC4K,MAAM,CAAC7K,QAAQ,EAAEsN,IAAI,CAAC;YAC/C,CAAC,MAAM,IAAIzC,MAAM,CAAC3K,OAAO,EAAE;cACzB,MAAMA,OAAO,CAAC2K,MAAM,CAAC7K,QAAQ,EAAEsN,IAAI,CAAC;YACtC,CAAC,MAAM;cACL,MAAMtN,QAAQ,CAAC6K,MAAM,CAAC7K,QAAQ,EAAEsN,IAAI,CAAC;YACvC;UACF,CAAC,MAAM;YACLmB,UAAU,CACRxH,KAAK,IAAIlF,KAAK,CAACC,EAAE,IAAIiF,KAAK,EAC1B,mCACF,CAAC;YACD,IAAI4D,MAAM,GAAG5D,KAAK,CAAClF,KAAK,CAACC,EAAE,CAAC;YAC5ByM,UAAU,CACR,MAAM,IAAI5D,MAAM,EAChB,oCACF,CAAC;YACD,OAAOA,MAAM,CAAC4F,IAAI;UACpB;QACF;QACA,IAAI/D,GAAG,GAAG,MAAMO,gBAAgB,CAAClL,KAAK,CAACwC,MAAM,CAACiB,MAAM,EAAED,IAAI,CAAC;QAC3D,OAAOmH,GAAG;MACZ,CAAC,GAAG,KAAK,CAAC;MACVpH,MAAM,EAAEvD,KAAK,CAACwC,MAAM,CAACe,MAAM,GAAIC,IAAI,IAAK0H,gBAAgB,CAAClL,KAAK,CAACwC,MAAM,CAACe,MAAM,EAAEC,IAAI,CAAC,GAAG,KAAK,CAAC;MAC5FI,MAAM,EAAE5D,KAAK,CAACwC,MAAM,CAACoB;IACvB,CAAC;IACD,OAAO5D,KAAK,CAACoD,KAAK,GAAG;MACnBA,KAAK,EAAE,IAAI;MACX,GAAG0K;IACL,CAAC,GAAG;MACFa,aAAa,EAAE3O,KAAK,CAAC2O,aAAa;MAClChK,QAAQ,EAAEkJ,6BAA6B,CACrCzO,QAAQ,EACRK,MAAM,EACNO,KAAK,CAACC,EAAE,EACR2N,gBACF,CAAC;MACD,GAAGE;IACL,CAAC;EACH,CAAC,CAAC;AACJ;;AAEA;AACA,SAASc,yBAAyBA,CAACC,aAAa,EAAE;EAChD,OAAOzR,UAAU,CAACoL,IAAI,CAACC,SAAS,CAACoG,aAAa,CAAC,CAAC;AAClD;;AAEA;AACA,SAASC,kBAAkB,QAAQ,mBAAmB;AACtD,SAASC,kBAAkBA,CAAC/P,OAAO,EAAEgQ,KAAK,EAAE;EAC1C,OAAOC,sBAAsB,CAACjQ,OAAO,EAAGkQ,CAAC,IAAK;IAC5C,IAAIlP,KAAK,GAAGgP,KAAK,CAACxP,MAAM,CAAC0P,CAAC,CAAClP,KAAK,CAACC,EAAE,CAAC;IACpCyM,UAAU,CAAC1M,KAAK,EAAE,kBAAkBkP,CAAC,CAAClP,KAAK,CAACC,EAAE,sBAAsB,CAAC;IACrE,OAAOD,KAAK,CAACwC,MAAM,CAAC6J,OAAO;EAC7B,CAAC,CAAC;AACJ;AACA,SAAS4C,sBAAsBA,CAACjQ,OAAO,EAAEmQ,iBAAiB,EAAEC,eAAe,EAAE;EAC3E,IAAIC,WAAW,GAAGrQ,OAAO,CAACoL,MAAM,GAAGpL,OAAO,CAACc,OAAO,CAACwP,SAAS,CAAEJ,CAAC,IAAKlQ,OAAO,CAACoL,MAAM,CAAC8E,CAAC,CAAClP,KAAK,CAACC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;EACpG,IAAIH,OAAO,GAAGuP,WAAW,IAAI,CAAC,GAAGrQ,OAAO,CAACc,OAAO,CAACkG,KAAK,CAAC,CAAC,EAAEqJ,WAAW,GAAG,CAAC,CAAC,GAAGrQ,OAAO,CAACc,OAAO;EAC5F,IAAIyP,YAAY;EAChB,IAAIF,WAAW,IAAI,CAAC,EAAE;IACpB,IAAI;MAAEG,aAAa;MAAEC,UAAU;MAAEC,aAAa;MAAE9P;IAAW,CAAC,GAAGZ,OAAO;IACtEA,OAAO,CAACc,OAAO,CAACkG,KAAK,CAACqJ,WAAW,CAAC,CAACM,IAAI,CAAE9P,KAAK,IAAK;MACjD,IAAII,EAAE,GAAGJ,KAAK,CAACG,KAAK,CAACC,EAAE;MACvB,IAAIuP,aAAa,CAACvP,EAAE,CAAC,KAAK,CAACwP,UAAU,IAAI,CAACA,UAAU,CAACG,cAAc,CAAC3P,EAAE,CAAC,CAAC,EAAE;QACxEsP,YAAY,GAAGC,aAAa,CAACvP,EAAE,CAAC;MAClC,CAAC,MAAM,IAAIyP,aAAa,CAACzP,EAAE,CAAC,IAAI,CAACL,UAAU,CAACgQ,cAAc,CAAC3P,EAAE,CAAC,EAAE;QAC9DsP,YAAY,GAAGG,aAAa,CAACzP,EAAE,CAAC;MAClC;MACA,OAAOsP,YAAY,IAAI,IAAI;IAC7B,CAAC,CAAC;EACJ;EACA,MAAMM,cAAc,GAAG,IAAIC,OAAO,CAACV,eAAe,CAAC;EACnD,OAAOtP,OAAO,CAAC4J,MAAM,CAAC,CAACqG,aAAa,EAAElQ,KAAK,EAAEmQ,GAAG,KAAK;IACnD,IAAI;MAAE/P;IAAG,CAAC,GAAGJ,KAAK,CAACG,KAAK;IACxB,IAAI0P,aAAa,GAAG1Q,OAAO,CAAC0Q,aAAa,CAACzP,EAAE,CAAC,IAAI,IAAI6P,OAAO,CAAC,CAAC;IAC9D,IAAIN,aAAa,GAAGxQ,OAAO,CAACwQ,aAAa,CAACvP,EAAE,CAAC,IAAI,IAAI6P,OAAO,CAAC,CAAC;IAC9D,IAAIG,mBAAmB,GAAGV,YAAY,IAAI,IAAI,IAAIS,GAAG,KAAKlQ,OAAO,CAAC6G,MAAM,GAAG,CAAC;IAC5E,IAAIuJ,mBAAmB,GAAGD,mBAAmB,IAAIV,YAAY,KAAKG,aAAa,IAAIH,YAAY,KAAKC,aAAa;IACjH,IAAIW,SAAS,GAAGhB,iBAAiB,CAACtP,KAAK,CAAC;IACxC,IAAIsQ,SAAS,IAAI,IAAI,EAAE;MACrB,IAAIC,QAAQ,GAAG,IAAIN,OAAO,CAACC,aAAa,CAAC;MACzC,IAAIG,mBAAmB,EAAE;QACvBG,cAAc,CAACd,YAAY,EAAEa,QAAQ,CAAC;MACxC;MACAC,cAAc,CAACb,aAAa,EAAEY,QAAQ,CAAC;MACvCC,cAAc,CAACX,aAAa,EAAEU,QAAQ,CAAC;MACvC,OAAOA,QAAQ;IACjB;IACA,IAAI/D,OAAO,GAAG,IAAIyD,OAAO,CACvB,OAAOK,SAAS,KAAK,UAAU,GAAGA,SAAS,CAAC;MAC1CT,aAAa;MACbK,aAAa;MACbP,aAAa;MACbD,YAAY,EAAEU,mBAAmB,GAAGV,YAAY,GAAG,KAAK;IAC1D,CAAC,CAAC,GAAGY,SACP,CAAC;IACD,IAAID,mBAAmB,EAAE;MACvBG,cAAc,CAACd,YAAY,EAAElD,OAAO,CAAC;IACvC;IACAgE,cAAc,CAACb,aAAa,EAAEnD,OAAO,CAAC;IACtCgE,cAAc,CAACX,aAAa,EAAErD,OAAO,CAAC;IACtCgE,cAAc,CAACN,aAAa,EAAE1D,OAAO,CAAC;IACtC,OAAOA,OAAO;EAChB,CAAC,EAAE,IAAIyD,OAAO,CAACD,cAAc,CAAC,CAAC;AACjC;AACA,SAASQ,cAAcA,CAACN,aAAa,EAAEO,YAAY,EAAE;EACnD,IAAIC,qBAAqB,GAAGR,aAAa,CAACzC,GAAG,CAAC,YAAY,CAAC;EAC3D,IAAIiD,qBAAqB,EAAE;IACzB,IAAI5I,OAAO,GAAGmH,kBAAkB,CAACyB,qBAAqB,CAAC;IACvD,IAAIC,YAAY,GAAG,IAAIC,GAAG,CAACH,YAAY,CAACI,YAAY,CAAC,CAAC,CAAC;IACvD/I,OAAO,CAAC+F,OAAO,CAAE5H,MAAM,IAAK;MAC1B,IAAI,CAAC0K,YAAY,CAACG,GAAG,CAAC7K,MAAM,CAAC,EAAE;QAC7BwK,YAAY,CAACpE,MAAM,CAAC,YAAY,EAAEpG,MAAM,CAAC;MAC3C;IACF,CAAC,CAAC;EACJ;AACF;;AAEA;AACA,IAAI8K,2BAA2B,GAAG,eAAgB,IAAIH,GAAG,CAAC,CACxD,GAAG3U,oBAAoB,EACvB,GAAG,CACJ,CAAC;AACF,eAAe+U,iBAAiBA,CAAC7B,KAAK,EAAEhF,UAAU,EAAE8G,aAAa,EAAE1F,OAAO,EAAE2F,UAAU,EAAEC,WAAW,EAAEC,WAAW,EAAE;EAChH,IAAI;IACF,IAAIC,cAAc,GAAG,IAAI1E,OAAO,CAACuE,UAAU,EAAE;MAC3C5E,MAAM,EAAEf,OAAO,CAACe,MAAM;MACtBC,IAAI,EAAEhB,OAAO,CAACgB,IAAI;MAClBC,OAAO,EAAEjB,OAAO,CAACiB,OAAO;MACxBC,MAAM,EAAElB,OAAO,CAACkB,MAAM;MACtB,IAAGlB,OAAO,CAACgB,IAAI,GAAG;QAAEG,MAAM,EAAE;MAAO,CAAC,GAAG,KAAK,CAAC;IAC/C,CAAC,CAAC;IACF,IAAIzD,MAAM,GAAG,MAAMgI,aAAa,CAACK,KAAK,CAACD,cAAc,EAAE;MACrDE,cAAc,EAAEJ,WAAW;MAC3BK,uBAAuB,EAAE,IAAI;MAC7BC,gBAAgB,EAAE,IAAI;MACtBC,0BAA0B,EAAEvC,KAAK,CAACvP,MAAM,CAAC4C,aAAa,GAAG,MAAO8O,KAAK,IAAK;QACxE,IAAI;UACF,IAAIK,WAAW,GAAG,MAAML,KAAK,CAACD,cAAc,CAAC;UAC7C,OAAOO,iBAAiB,CAACD,WAAW,CAAC;QACvC,CAAC,CAAC,OAAOnL,KAAK,EAAE;UACd,OAAOqL,gBAAgB,CAACrL,KAAK,CAAC;QAChC;MACF,CAAC,GAAG,KAAK;IACX,CAAC,CAAC;IACF,OAAOoL,iBAAiB,CAAC3I,MAAM,CAAC;EAClC,CAAC,CAAC,OAAOzC,KAAK,EAAE;IACd,OAAOqL,gBAAgB,CAACrL,KAAK,CAAC;EAChC;EACA,SAASoL,iBAAiBA,CAAC3I,MAAM,EAAE;IACjC,OAAOjL,UAAU,CAACiL,MAAM,CAAC,GAAGA,MAAM,GAAG6I,uBAAuB,CAAC7I,MAAM,CAAC;EACtE;EACA,SAAS4I,gBAAgBA,CAACrL,KAAK,EAAE;IAC/B4K,WAAW,CAAC5K,KAAK,CAAC;IAClB,OAAOuL,2BAA2B,CAACxG,OAAO,EAAE4D,KAAK,EAAEhF,UAAU,EAAE;MAC7DlB,MAAM,EAAE;QAAEzC;MAAM,CAAC;MACjBgG,OAAO,EAAE,IAAIyD,OAAO,CAAC,CAAC;MACtBtE,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;EACA,SAASmG,uBAAuBA,CAAC3S,OAAO,EAAE;IACxC,IAAIqN,OAAO,GAAG0C,kBAAkB,CAAC/P,OAAO,EAAEgQ,KAAK,CAAC;IAChD,IAAIpR,oBAAoB,CAACoB,OAAO,CAAC6S,UAAU,CAAC,IAAIxF,OAAO,CAACsE,GAAG,CAAC,UAAU,CAAC,EAAE;MACvE,OAAO,IAAIlF,QAAQ,CAAC,IAAI,EAAE;QAAED,MAAM,EAAExM,OAAO,CAAC6S,UAAU;QAAExF;MAAQ,CAAC,CAAC;IACpE;IACA,IAAIrN,OAAO,CAACoL,MAAM,EAAE;MAClBZ,MAAM,CAACiE,MAAM,CAACzO,OAAO,CAACoL,MAAM,CAAC,CAACsD,OAAO,CAAEoE,GAAG,IAAK;QAC7C,IAAI,CAAChU,oBAAoB,CAACgU,GAAG,CAAC,IAAIA,GAAG,CAACzL,KAAK,EAAE;UAC3C4K,WAAW,CAACa,GAAG,CAAC;QAClB;MACF,CAAC,CAAC;MACF9S,OAAO,CAACoL,MAAM,GAAGD,cAAc,CAACnL,OAAO,CAACoL,MAAM,EAAEJ,UAAU,CAAC;IAC7D;IACA,IAAI+H,iBAAiB;IACrB,IAAI/S,OAAO,CAACoL,MAAM,EAAE;MAClB2H,iBAAiB,GAAG;QAAE1L,KAAK,EAAEmD,MAAM,CAACiE,MAAM,CAACzO,OAAO,CAACoL,MAAM,CAAC,CAAC,CAAC;MAAE,CAAC;IACjE,CAAC,MAAM;MACL2H,iBAAiB,GAAG;QAClBrD,IAAI,EAAElF,MAAM,CAACiE,MAAM,CAACzO,OAAO,CAACyQ,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;MACjD,CAAC;IACH;IACA,OAAOmC,2BAA2B,CAACxG,OAAO,EAAE4D,KAAK,EAAEhF,UAAU,EAAE;MAC7DlB,MAAM,EAAEiJ,iBAAiB;MACzB1F,OAAO;MACPb,MAAM,EAAExM,OAAO,CAAC6S;IAClB,CAAC,CAAC;EACJ;AACF;AACA,eAAeG,kBAAkBA,CAAChD,KAAK,EAAEhF,UAAU,EAAE8G,aAAa,EAAE1F,OAAO,EAAE2F,UAAU,EAAEC,WAAW,EAAEC,WAAW,EAAE;EACjH,IAAIgB,WAAW,GAAG,IAAI9S,GAAG,CAACiM,OAAO,CAACnM,GAAG,CAAC,CAAC0M,YAAY,CAAC2B,GAAG,CAAC,SAAS,CAAC;EAClE,IAAI4E,YAAY,GAAGD,WAAW,GAAG,IAAIxB,GAAG,CAACwB,WAAW,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI;EACvE,IAAI;IACF,IAAIjB,cAAc,GAAG,IAAI1E,OAAO,CAACuE,UAAU,EAAE;MAC3C1E,OAAO,EAAEjB,OAAO,CAACiB,OAAO;MACxBC,MAAM,EAAElB,OAAO,CAACkB;IAClB,CAAC,CAAC;IACF,IAAIxD,MAAM,GAAG,MAAMgI,aAAa,CAACK,KAAK,CAACD,cAAc,EAAE;MACrDE,cAAc,EAAEJ,WAAW;MAC3BoB,mBAAmB,EAAGlD,CAAC,IAAK,CAACgD,YAAY,IAAIA,YAAY,CAACvB,GAAG,CAACzB,CAAC,CAAClP,KAAK,CAACC,EAAE,CAAC;MACzEoR,uBAAuB,EAAE,IAAI;MAC7BE,0BAA0B,EAAEvC,KAAK,CAACvP,MAAM,CAAC4C,aAAa,GAAG,MAAO8O,KAAK,IAAK;QACxE,IAAI;UACF,IAAIK,WAAW,GAAG,MAAML,KAAK,CAACD,cAAc,CAAC;UAC7C,OAAOO,iBAAiB,CAACD,WAAW,CAAC;QACvC,CAAC,CAAC,OAAOnL,KAAK,EAAE;UACd,OAAOqL,gBAAgB,CAACrL,KAAK,CAAC;QAChC;MACF,CAAC,GAAG,KAAK;IACX,CAAC,CAAC;IACF,OAAOoL,iBAAiB,CAAC3I,MAAM,CAAC;EAClC,CAAC,CAAC,OAAOzC,KAAK,EAAE;IACd,OAAOqL,gBAAgB,CAACrL,KAAK,CAAC;EAChC;EACA,SAASoL,iBAAiBA,CAAC3I,MAAM,EAAE;IACjC,OAAOjL,UAAU,CAACiL,MAAM,CAAC,GAAGA,MAAM,GAAG6I,uBAAuB,CAAC7I,MAAM,CAAC;EACtE;EACA,SAAS4I,gBAAgBA,CAACrL,KAAK,EAAE;IAC/B4K,WAAW,CAAC5K,KAAK,CAAC;IAClB,OAAOuL,2BAA2B,CAACxG,OAAO,EAAE4D,KAAK,EAAEhF,UAAU,EAAE;MAC7DlB,MAAM,EAAE;QAAEzC;MAAM,CAAC;MACjBgG,OAAO,EAAE,IAAIyD,OAAO,CAAC,CAAC;MACtBtE,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;EACA,SAASmG,uBAAuBA,CAAC3S,OAAO,EAAE;IACxC,IAAIqN,OAAO,GAAG0C,kBAAkB,CAAC/P,OAAO,EAAEgQ,KAAK,CAAC;IAChD,IAAIpR,oBAAoB,CAACoB,OAAO,CAAC6S,UAAU,CAAC,IAAIxF,OAAO,CAACsE,GAAG,CAAC,UAAU,CAAC,EAAE;MACvE,OAAO,IAAIlF,QAAQ,CAAC,IAAI,EAAE;QAAED,MAAM,EAAExM,OAAO,CAAC6S,UAAU;QAAExF;MAAQ,CAAC,CAAC;IACpE;IACA,IAAIrN,OAAO,CAACoL,MAAM,EAAE;MAClBZ,MAAM,CAACiE,MAAM,CAACzO,OAAO,CAACoL,MAAM,CAAC,CAACsD,OAAO,CAAEoE,GAAG,IAAK;QAC7C,IAAI,CAAChU,oBAAoB,CAACgU,GAAG,CAAC,IAAIA,GAAG,CAACzL,KAAK,EAAE;UAC3C4K,WAAW,CAACa,GAAG,CAAC;QAClB;MACF,CAAC,CAAC;MACF9S,OAAO,CAACoL,MAAM,GAAGD,cAAc,CAACnL,OAAO,CAACoL,MAAM,EAAEJ,UAAU,CAAC;IAC7D;IACA,IAAIqI,OAAO,GAAG,CAAC,CAAC;IAChB,IAAIC,aAAa,GAAG,IAAI7B,GAAG,CACzBzR,OAAO,CAACc,OAAO,CAACyS,MAAM,CACnBrD,CAAC,IAAKgD,YAAY,GAAGA,YAAY,CAACvB,GAAG,CAACzB,CAAC,CAAClP,KAAK,CAACC,EAAE,CAAC,GAAGiP,CAAC,CAAClP,KAAK,CAACyD,MAAM,IAAI,IACzE,CAAC,CAACT,GAAG,CAAEkM,CAAC,IAAKA,CAAC,CAAClP,KAAK,CAACC,EAAE,CACzB,CAAC;IACD,IAAIjB,OAAO,CAACoL,MAAM,EAAE;MAClB,KAAK,IAAI,CAACnK,EAAE,EAAEoG,KAAK,CAAC,IAAImD,MAAM,CAACa,OAAO,CAACrL,OAAO,CAACoL,MAAM,CAAC,EAAE;QACtDiI,OAAO,CAACpS,EAAE,CAAC,GAAG;UAAEoG;QAAM,CAAC;MACzB;IACF;IACA,KAAK,IAAI,CAACpG,EAAE,EAAEiF,KAAK,CAAC,IAAIsE,MAAM,CAACa,OAAO,CAACrL,OAAO,CAACY,UAAU,CAAC,EAAE;MAC1D,IAAI,EAAEK,EAAE,IAAIoS,OAAO,CAAC,IAAIC,aAAa,CAAC3B,GAAG,CAAC1Q,EAAE,CAAC,EAAE;QAC7CoS,OAAO,CAACpS,EAAE,CAAC,GAAG;UAAEyO,IAAI,EAAExJ;QAAM,CAAC;MAC/B;IACF;IACA,OAAO0M,2BAA2B,CAACxG,OAAO,EAAE4D,KAAK,EAAEhF,UAAU,EAAE;MAC7DlB,MAAM,EAAEuJ,OAAO;MACfhG,OAAO;MACPb,MAAM,EAAExM,OAAO,CAAC6S;IAClB,CAAC,CAAC;EACJ;AACF;AACA,SAASD,2BAA2BA,CAACxG,OAAO,EAAE4D,KAAK,EAAEhF,UAAU,EAAE;EAC/DlB,MAAM;EACNuD,OAAO;EACPb;AACF,CAAC,EAAE;EACD,IAAIgH,aAAa,GAAG,IAAI1C,OAAO,CAACzD,OAAO,CAAC;EACxCmG,aAAa,CAACC,GAAG,CAAC,kBAAkB,EAAE,KAAK,CAAC;EAC5C,IAAI7B,2BAA2B,CAACD,GAAG,CAACnF,MAAM,CAAC,EAAE;IAC3C,OAAO,IAAIC,QAAQ,CAAC,IAAI,EAAE;MAAED,MAAM;MAAEa,OAAO,EAAEmG;IAAc,CAAC,CAAC;EAC/D;EACAA,aAAa,CAACC,GAAG,CAAC,cAAc,EAAE,eAAe,CAAC;EAClDD,aAAa,CAAC3G,MAAM,CAAC,gBAAgB,CAAC;EACtC,OAAO,IAAIJ,QAAQ,CACjBiH,oBAAoB,CAClB5J,MAAM,EACNsC,OAAO,CAACkB,MAAM,EACd0C,KAAK,CAAC1M,KAAK,CAACE,MAAM,CAACmQ,aAAa,EAChC3I,UACF,CAAC,EACD;IACEwB,MAAM,EAAEA,MAAM,IAAI,GAAG;IACrBa,OAAO,EAAEmG;EACX,CACF,CAAC;AACH;AACA,SAASI,mCAAmCA,CAACC,gBAAgB,EAAEzH,OAAO,EAAE4D,KAAK,EAAEhF,UAAU,EAAE;EACzF,IAAI8I,SAAS,GAAGC,sBAAsB,CACpCF,gBAAgB,CAACrH,MAAM,EACvBqH,gBAAgB,CAACxG,OAAO,EACxB2C,KAAK,CAAChE,QACR,CAAC;EACD,IAAIqB,OAAO,GAAG,IAAIyD,OAAO,CAAC+C,gBAAgB,CAACxG,OAAO,CAAC;EACnDA,OAAO,CAACR,MAAM,CAAC,UAAU,CAAC;EAC1BQ,OAAO,CAACoG,GAAG,CAAC,cAAc,EAAE,eAAe,CAAC;EAC5C,OAAOb,2BAA2B,CAACxG,OAAO,EAAE4D,KAAK,EAAEhF,UAAU,EAAE;IAC7DlB,MAAM,EAAEsC,OAAO,CAACe,MAAM,KAAK,KAAK,GAAG;MAAE,CAAC9P,yBAAyB,GAAGyW;IAAU,CAAC,GAAGA,SAAS;IACzFzG,OAAO;IACPb,MAAM,EAAEpP;EACV,CAAC,CAAC;AACJ;AACA,SAAS2W,sBAAsBA,CAACvH,MAAM,EAAEa,OAAO,EAAErB,QAAQ,EAAE;EACzD,IAAI8H,SAAS,GAAGzG,OAAO,CAACiB,GAAG,CAAC,UAAU,CAAC;EACvC,IAAItC,QAAQ,EAAE;IACZ8H,SAAS,GAAGvU,aAAa,CAACuU,SAAS,EAAE9H,QAAQ,CAAC,IAAI8H,SAAS;EAC7D;EACA,OAAO;IACL7U,QAAQ,EAAE6U,SAAS;IACnBtH,MAAM;IACNwH,UAAU;IACR;IACA;IACA;IACA;IACA;IACA;IACA;IACA3G,OAAO,CAACsE,GAAG,CAAC,oBAAoB,CAAC,IAAItE,OAAO,CAACsE,GAAG,CAAC,YAAY,CAC9D;IACDlC,MAAM,EAAEpC,OAAO,CAACsE,GAAG,CAAC,yBAAyB,CAAC;IAC9CxS,OAAO,EAAEkO,OAAO,CAACsE,GAAG,CAAC,iBAAiB;EACxC,CAAC;AACH;AACA,SAAS+B,oBAAoBA,CAACxN,KAAK,EAAE+N,aAAa,EAAEN,aAAa,EAAE3I,UAAU,EAAE;EAC7E,IAAIqE,UAAU,GAAG,IAAI6E,eAAe,CAAC,CAAC;EACtC,IAAIC,SAAS,GAAGC,UAAU,CACxB,MAAM/E,UAAU,CAACgF,KAAK,CAAC,IAAIpQ,KAAK,CAAC,gBAAgB,CAAC,CAAC,EACnD,OAAO0P,aAAa,KAAK,QAAQ,GAAGA,aAAa,GAAG,IACtD,CAAC;EACDM,aAAa,CAACK,gBAAgB,CAAC,OAAO,EAAE,MAAMC,YAAY,CAACJ,SAAS,CAAC,CAAC;EACtE,OAAOhW,MAAM,CAAC+H,KAAK,EAAE;IACnBoH,MAAM,EAAE+B,UAAU,CAAC/B,MAAM;IACzBkH,OAAO,EAAE,CACN9S,KAAK,IAAK;MACT,IAAIA,KAAK,YAAYuC,KAAK,EAAE;QAC1B,IAAI;UAAEuD,IAAI;UAAEgE,OAAO;UAAEN;QAAM,CAAC,GAAGF,UAAU,KAAK,YAAY,CAAC,mBAAmBD,aAAa,CAACrJ,KAAK,EAAEsJ,UAAU,CAAC,GAAGtJ,KAAK;QACtH,OAAO,CAAC,gBAAgB,EAAE8F,IAAI,EAAEgE,OAAO,EAAEN,KAAK,CAAC;MACjD;MACA,IAAIxJ,KAAK,YAAY9E,iBAAiB,EAAE;QACtC,IAAI;UAAE8S,IAAI,EAAE+E,KAAK;UAAEjI,MAAM;UAAEkI;QAAW,CAAC,GAAGhT,KAAK;QAC/C,OAAO,CAAC,eAAe,EAAE+S,KAAK,EAAEjI,MAAM,EAAEkI,UAAU,CAAC;MACrD;MACA,IAAIhT,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIrE,yBAAyB,IAAIqE,KAAK,EAAE;QAC5E,OAAO,CAAC,qBAAqB,EAAEA,KAAK,CAACrE,yBAAyB,CAAC,CAAC;MAClE;IACF,CAAC,CACF;IACDsX,WAAW,EAAE,CACVjT,KAAK,IAAK;MACT,IAAI,CAACA,KAAK,EAAE;MACZ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC/B,OAAO,CACL,0BAA0B,EAC1B8I,MAAM,CAACoK,WAAW,CAACpK,MAAM,CAACa,OAAO,CAAC3J,KAAK,CAAC,CAAC,CAC1C;IACH,CAAC,EACD,MAAM,CAAC,qBAAqB,CAAC;EAEjC,CAAC,CAAC;AACJ;;AAEA;AACA,SAASmT,MAAMA,CAAC7E,KAAK,EAAEtM,IAAI,EAAE;EAC3B,IAAIlD,MAAM,GAAGmO,YAAY,CAACqB,KAAK,CAACxP,MAAM,CAAC;EACvC,IAAIsU,UAAU,GAAGjG,6BAA6B,CAACmB,KAAK,CAACxP,MAAM,EAAEwP,KAAK,CAACvP,MAAM,CAAC;EAC1E,IAAIuK,UAAU,GAAGF,YAAY,CAACpH,IAAI,CAAC,GAAGA,IAAI,GAAG,YAAY,CAAC;EAC1D,IAAIoO,aAAa,GAAG9T,mBAAmB,CAAC8W,UAAU,EAAE;IAClD9I,QAAQ,EAAEgE,KAAK,CAAChE;EAClB,CAAC,CAAC;EACF,IAAI+I,YAAY,GAAG/E,KAAK,CAAC1M,KAAK,CAACE,MAAM,CAACyO,WAAW,KAAK,CAAC5K,KAAK,EAAE;IAAE+E;EAAQ,CAAC,KAAK;IAC5E,IAAIpB,UAAU,KAAK,MAAM,CAAC,cAAc,CAACoB,OAAO,CAACkB,MAAM,CAAC0H,OAAO,EAAE;MAC/DrH,OAAO,CAACtG,KAAK;MACX;MACAvI,oBAAoB,CAACuI,KAAK,CAAC,IAAIA,KAAK,CAACA,KAAK,GAAGA,KAAK,CAACA,KAAK,GAAGA,KAC7D,CAAC;IACH;EACF,CAAC,CAAC;EACF,OAAO;IACL7G,MAAM;IACNsU,UAAU;IACV9J,UAAU;IACV8G,aAAa;IACbiD;EACF,CAAC;AACH;AACA,IAAIE,oBAAoB,GAAGA,CAACjF,KAAK,EAAEtM,IAAI,KAAK;EAC1C,IAAIwR,MAAM;EACV,IAAI1U,MAAM;EACV,IAAIwK,UAAU;EACd,IAAI8G,aAAa;EACjB,IAAIiD,YAAY;EAChB,OAAO,eAAeI,cAAcA,CAAC/I,OAAO,EAAEgJ,cAAc,EAAE;IAC5DF,MAAM,GAAG,OAAOlF,KAAK,KAAK,UAAU,GAAG,MAAMA,KAAK,CAAC,CAAC,GAAGA,KAAK;IAC5D,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;MAC/B,IAAIqF,OAAO,GAAGR,MAAM,CAACK,MAAM,EAAExR,IAAI,CAAC;MAClClD,MAAM,GAAG6U,OAAO,CAAC7U,MAAM;MACvBwK,UAAU,GAAGqK,OAAO,CAACrK,UAAU;MAC/B8G,aAAa,GAAGuD,OAAO,CAACvD,aAAa;MACrCiD,YAAY,GAAGM,OAAO,CAACN,YAAY;IACrC,CAAC,MAAM,IAAI,CAACvU,MAAM,IAAI,CAACwK,UAAU,IAAI,CAAC8G,aAAa,IAAI,CAACiD,YAAY,EAAE;MACpE,IAAIM,OAAO,GAAGR,MAAM,CAACK,MAAM,EAAExR,IAAI,CAAC;MAClClD,MAAM,GAAG6U,OAAO,CAAC7U,MAAM;MACvBwK,UAAU,GAAGqK,OAAO,CAACrK,UAAU;MAC/B8G,aAAa,GAAGuD,OAAO,CAACvD,aAAa;MACrCiD,YAAY,GAAGM,OAAO,CAACN,YAAY;IACrC;IACA,IAAI9I,MAAM,GAAG,CAAC,CAAC;IACf,IAAI+F,WAAW;IACf,IAAIC,WAAW,GAAI5K,KAAK,IAAK;MAC3B,IAAI3D,IAAI,KAAK,aAAa,CAAC,mBAAmB;QAC5CsK,iBAAiB,CAAC,CAAC,EAAEsH,mBAAmB,GAAGjO,KAAK,CAAC;MACnD;MACA0N,YAAY,CAAC1N,KAAK,EAAE;QAClBrH,OAAO,EAAEgS,WAAW;QACpB/F,MAAM;QACNG;MACF,CAAC,CAAC;IACJ,CAAC;IACD,IAAI8I,MAAM,CAACzU,MAAM,CAAC4C,aAAa,EAAE;MAC/B,IAAI+R,cAAc,IAAI,EAAEA,cAAc,YAAYlY,qBAAqB,CAAC,EAAE;QACxE,IAAImK,KAAK,GAAG,IAAIpD,KAAK,CACnB,6KACF,CAAC;QACDgO,WAAW,CAAC5K,KAAK,CAAC;QAClB,OAAOkO,6BAA6B,CAAClO,KAAK,EAAE2D,UAAU,CAAC;MACzD;MACAgH,WAAW,GAAGoD,cAAc,IAAI,IAAIlY,qBAAqB,CAAC,CAAC;IAC7D,CAAC,MAAM;MACL8U,WAAW,GAAGoD,cAAc,IAAI,CAAC,CAAC;IACpC;IACA,IAAInV,GAAG,GAAG,IAAIE,GAAG,CAACiM,OAAO,CAACnM,GAAG,CAAC;IAC9B,IAAIuV,kBAAkB,GAAGN,MAAM,CAAClJ,QAAQ,IAAI,GAAG;IAC/C,IAAIyJ,cAAc,GAAGxV,GAAG,CAAC8L,QAAQ;IACjC,IAAIxM,aAAa,CAACkW,cAAc,EAAED,kBAAkB,CAAC,KAAK,aAAa,EAAE;MACvEC,cAAc,GAAGD,kBAAkB;IACrC,CAAC,MAAM,IAAIC,cAAc,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC3CD,cAAc,GAAGA,cAAc,CAACtW,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;IACxD;IACA,IAAII,aAAa,CAACkW,cAAc,EAAED,kBAAkB,CAAC,KAAK,GAAG,IAAIC,cAAc,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC7FD,cAAc,GAAGA,cAAc,CAACzO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9C;IACA,IAAItG,SAAS,GAAGuN,kBAAkB,CAAC7B,OAAO,EAAE,yBAAyB,CAAC,KAAK,KAAK;IAChF,IAAI,CAAC8I,MAAM,CAACvT,GAAG,EAAE;MACf,IAAIgU,WAAW,GAAG3G,SAAS,CAACyG,cAAc,CAAC;MAC3C,IAAID,kBAAkB,KAAK,GAAG,EAAE;QAC9B,IAAII,YAAY,GAAGrW,aAAa,CAACoW,WAAW,EAAEH,kBAAkB,CAAC;QACjE,IAAII,YAAY,IAAI,IAAI,EAAE;UACxBb,YAAY,CACV,IAAInY,iBAAiB,CACnB,GAAG,EACH,WAAW,EACX,+BAA+B+Y,WAAW,yDAAyDH,kBAAkB,IACvH,CAAC,EACD;YACExV,OAAO,EAAEgS,WAAW;YACpB/F,MAAM;YACNG;UACF,CACF,CAAC;UACD,OAAO,IAAIK,QAAQ,CAAC,WAAW,EAAE;YAC/BD,MAAM,EAAE,GAAG;YACXkI,UAAU,EAAE;UACd,CAAC,CAAC;QACJ;QACAiB,WAAW,GAAGC,YAAY;MAC5B;MACA,IAAIV,MAAM,CAACW,SAAS,CAAClO,MAAM,KAAK,CAAC,EAAE;QACjCjH,SAAS,GAAG,IAAI;MAClB,CAAC,MAAM,IAAI,CAACwU,MAAM,CAACW,SAAS,CAACC,QAAQ,CAACH,WAAW,CAAC,IAAI,CAACT,MAAM,CAACW,SAAS,CAACC,QAAQ,CAACH,WAAW,GAAG,GAAG,CAAC,EAAE;QACnG,IAAI1V,GAAG,CAAC8L,QAAQ,CAAC2J,QAAQ,CAAC,OAAO,CAAC,EAAE;UAClCX,YAAY,CACV,IAAInY,iBAAiB,CACnB,GAAG,EACH,WAAW,EACX,8BAA8B+Y,WAAW,oIAC3C,CAAC,EACD;YACE3V,OAAO,EAAEgS,WAAW;YACpB/F,MAAM;YACNG;UACF,CACF,CAAC;UACD,OAAO,IAAIK,QAAQ,CAAC,WAAW,EAAE;YAC/BD,MAAM,EAAE,GAAG;YACXkI,UAAU,EAAE;UACd,CAAC,CAAC;QACJ,CAAC,MAAM;UACLhU,SAAS,GAAG,IAAI;QAClB;MACF;IACF;IACA,IAAIqV,WAAW,GAAG1X,eAAe,CAC/B6W,MAAM,CAACtT,cAAc,CAAC+B,YAAY,EAClC6R,kBACF,CAAC;IACD,IAAIvV,GAAG,CAAC8L,QAAQ,KAAKgK,WAAW,EAAE;MAChC,IAAI;QACF,IAAIC,GAAG,GAAG,MAAMC,qBAAqB,CAACf,MAAM,EAAE1U,MAAM,EAAEP,GAAG,CAAC;QAC1D,OAAO+V,GAAG;MACZ,CAAC,CAAC,OAAOzH,CAAC,EAAE;QACV0D,WAAW,CAAC1D,CAAC,CAAC;QACd,OAAO,IAAI9B,QAAQ,CAAC,sBAAsB,EAAE;UAAED,MAAM,EAAE;QAAI,CAAC,CAAC;MAC9D;IACF;IACA,IAAI1L,OAAO,GAAGgL,iBAAiB,CAACtL,MAAM,EAAEiV,cAAc,EAAEP,MAAM,CAAClJ,QAAQ,CAAC;IACxE,IAAIlL,OAAO,IAAIA,OAAO,CAAC6G,MAAM,GAAG,CAAC,EAAE;MACjC6C,MAAM,CAACe,MAAM,CAACU,MAAM,EAAEnL,OAAO,CAAC,CAAC,CAAC,CAACmL,MAAM,CAAC;IAC1C;IACA,IAAIiK,QAAQ;IACZ,IAAIjW,GAAG,CAAC8L,QAAQ,CAAC2J,QAAQ,CAAC,OAAO,CAAC,EAAE;MAClC,IAAI3D,UAAU,GAAG,IAAI5R,GAAG,CAACiM,OAAO,CAACnM,GAAG,CAAC;MACrC8R,UAAU,CAAChG,QAAQ,GAAG0J,cAAc;MACpC,IAAIU,kBAAkB,GAAGrK,iBAAiB,CACxCtL,MAAM,EACNuR,UAAU,CAAChG,QAAQ,EACnBmJ,MAAM,CAAClJ,QACT,CAAC;MACDkK,QAAQ,GAAG,MAAME,wBAAwB,CACvCpL,UAAU,EACVkK,MAAM,EACNpD,aAAa,EACb1F,OAAO,EACP2F,UAAU,EACVC,WAAW,EACXC,WACF,CAAC;MACD,IAAItT,kBAAkB,CAACuX,QAAQ,CAAC,EAAE;QAChCA,QAAQ,GAAGtC,mCAAmC,CAC5CsC,QAAQ,EACR9J,OAAO,EACP8I,MAAM,EACNlK,UACF,CAAC;MACH;MACA,IAAIkK,MAAM,CAAC5R,KAAK,CAACE,MAAM,CAAC6S,iBAAiB,EAAE;QACzCH,QAAQ,GAAG,MAAMhB,MAAM,CAAC5R,KAAK,CAACE,MAAM,CAAC6S,iBAAiB,CAACH,QAAQ,EAAE;UAC/DlW,OAAO,EAAEgS,WAAW;UACpB/F,MAAM,EAAEkK,kBAAkB,GAAGA,kBAAkB,CAAC,CAAC,CAAC,CAAClK,MAAM,GAAG,CAAC,CAAC;UAC9DG;QACF,CAAC,CAAC;QACF,IAAIzN,kBAAkB,CAACuX,QAAQ,CAAC,EAAE;UAChCA,QAAQ,GAAGtC,mCAAmC,CAC5CsC,QAAQ,EACR9J,OAAO,EACP8I,MAAM,EACNlK,UACF,CAAC;QACH;MACF;IACF,CAAC,MAAM,IAAI,CAACtK,SAAS,IAAII,OAAO,IAAIA,OAAO,CAACA,OAAO,CAAC6G,MAAM,GAAG,CAAC,CAAC,CAAC3G,KAAK,CAACwC,MAAM,CAACgC,OAAO,IAAI,IAAI,IAAI1E,OAAO,CAACA,OAAO,CAAC6G,MAAM,GAAG,CAAC,CAAC,CAAC3G,KAAK,CAACwC,MAAM,CAACc,aAAa,IAAI,IAAI,EAAE;MAC9J4R,QAAQ,GAAG,MAAMI,qBAAqB,CACpCtL,UAAU,EACVkK,MAAM,EACNpD,aAAa,EACbhR,OAAO,CAACkG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAChG,KAAK,CAACC,EAAE,EAC7BmL,OAAO,EACP4F,WAAW,EACXC,WACF,CAAC;IACH,CAAC,MAAM;MACL,IAAI;QAAElG;MAAS,CAAC,GAAG9L,GAAG;MACtB,IAAIK,WAAW,GAAG,KAAK,CAAC;MACxB,IAAI4U,MAAM,CAACqB,uBAAuB,EAAE;QAClCjW,WAAW,GAAG,MAAM4U,MAAM,CAACqB,uBAAuB,CAAC;UAAExK;QAAS,CAAC,CAAC;MAClE,CAAC,MAAM,IAAIrI,IAAI,KAAK,aAAa,CAAC,qBAAqBsK,iBAAiB,CAAC,CAAC,EAAEwI,cAAc,EAAE;QAC1FlW,WAAW,GAAG,MAAM0N,iBAAiB,CAAC,CAAC,EAAEwI,cAAc,GAAGzK,QAAQ,CAAC;MACrE;MACAmK,QAAQ,GAAG,MAAMO,qBAAqB,CACpCzL,UAAU,EACVkK,MAAM,EACNpD,aAAa,EACb1F,OAAO,EACP4F,WAAW,EACXC,WAAW,EACXvR,SAAS,EACTJ,WACF,CAAC;IACH;IACA,IAAI8L,OAAO,CAACe,MAAM,KAAK,MAAM,EAAE;MAC7B,OAAO,IAAIV,QAAQ,CAAC,IAAI,EAAE;QACxBY,OAAO,EAAE6I,QAAQ,CAAC7I,OAAO;QACzBb,MAAM,EAAE0J,QAAQ,CAAC1J,MAAM;QACvBkI,UAAU,EAAEwB,QAAQ,CAACxB;MACvB,CAAC,CAAC;IACJ;IACA,OAAOwB,QAAQ;EACjB,CAAC;AACH,CAAC;AACD,eAAeD,qBAAqBA,CAACjG,KAAK,EAAExP,MAAM,EAAEP,GAAG,EAAE;EACvD,IAAI+P,KAAK,CAAC0G,MAAM,CAACjT,OAAO,KAAKxD,GAAG,CAAC0M,YAAY,CAAC2B,GAAG,CAAC,SAAS,CAAC,EAAE;IAC5D,OAAO,IAAI7B,QAAQ,CAAC,IAAI,EAAE;MACxBD,MAAM,EAAE,GAAG;MACXa,OAAO,EAAE;QACP,yBAAyB,EAAE;MAC7B;IACF,CAAC,CAAC;EACJ;EACA,IAAIsJ,OAAO,GAAG,CAAC,CAAC;EAChB,IAAI1W,GAAG,CAAC0M,YAAY,CAACgF,GAAG,CAAC,OAAO,CAAC,EAAE;IACjC,IAAIiF,KAAK,GAAG,eAAgB,IAAInF,GAAG,CAAC,CAAC;IACrC,IAAIoF,SAAS,GAAG5W,GAAG,CAAC0M,YAAY,CAAC2B,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;IACnD,IAAIwI,cAAc,GAAGD,SAAS,CAAC1D,KAAK,CAAC,GAAG,CAAC,CAACI,MAAM,CAACwD,OAAO,CAAC;IACzDD,cAAc,CAACpI,OAAO,CAAEvK,IAAI,IAAK;MAC/B,IAAI,CAACA,IAAI,CAAC6S,UAAU,CAAC,GAAG,CAAC,EAAE;QACzB7S,IAAI,GAAG,IAAIA,IAAI,EAAE;MACnB;MACA,IAAI8S,QAAQ,GAAG9S,IAAI,CAACgP,KAAK,CAAC,GAAG,CAAC,CAACnM,KAAK,CAAC,CAAC,CAAC;MACvCiQ,QAAQ,CAACvI,OAAO,CAAC,CAACwI,CAAC,EAAEtP,CAAC,KAAK;QACzB,IAAIuP,WAAW,GAAGF,QAAQ,CAACjQ,KAAK,CAAC,CAAC,EAAEY,CAAC,GAAG,CAAC,CAAC,CAACwP,IAAI,CAAC,GAAG,CAAC;QACpDR,KAAK,CAACS,GAAG,CAAC,IAAIF,WAAW,EAAE,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,KAAK,IAAIhT,IAAI,IAAIyS,KAAK,EAAE;MACtB,IAAI9V,OAAO,GAAGgL,iBAAiB,CAACtL,MAAM,EAAE2D,IAAI,EAAE6L,KAAK,CAAChE,QAAQ,CAAC;MAC7D,IAAIlL,OAAO,EAAE;QACX,KAAK,IAAID,KAAK,IAAIC,OAAO,EAAE;UACzB,IAAIC,OAAO,GAAGF,KAAK,CAACG,KAAK,CAACC,EAAE;UAC5B,IAAID,KAAK,GAAGgP,KAAK,CAAC0G,MAAM,CAAClW,MAAM,CAACO,OAAO,CAAC;UACxC,IAAIC,KAAK,EAAE;YACT2V,OAAO,CAAC5V,OAAO,CAAC,GAAGC,KAAK;UAC1B;QACF;MACF;IACF;IACA,OAAOyL,QAAQ,CAAC6K,IAAI,CAACX,OAAO,EAAE;MAC5BtJ,OAAO,EAAE;QACP,eAAe,EAAE;MACnB;IACF,CAAC,CAAC;EACJ;EACA,OAAO,IAAIZ,QAAQ,CAAC,iBAAiB,EAAE;IAAED,MAAM,EAAE;EAAI,CAAC,CAAC;AACzD;AACA,eAAe4J,wBAAwBA,CAACpL,UAAU,EAAEgF,KAAK,EAAE8B,aAAa,EAAE1F,OAAO,EAAE2F,UAAU,EAAEC,WAAW,EAAEC,WAAW,EAAE;EACvH,IAAIiE,QAAQ,GAAG9J,OAAO,CAACe,MAAM,KAAK,KAAK,GAAG,MAAM0E,iBAAiB,CAC/D7B,KAAK,EACLhF,UAAU,EACV8G,aAAa,EACb1F,OAAO,EACP2F,UAAU,EACVC,WAAW,EACXC,WACF,CAAC,GAAG,MAAMe,kBAAkB,CAC1BhD,KAAK,EACLhF,UAAU,EACV8G,aAAa,EACb1F,OAAO,EACP2F,UAAU,EACVC,WAAW,EACXC,WACF,CAAC;EACD,OAAOiE,QAAQ;AACjB;AACA,eAAeO,qBAAqBA,CAACzL,UAAU,EAAEgF,KAAK,EAAE8B,aAAa,EAAE1F,OAAO,EAAE4F,WAAW,EAAEC,WAAW,EAAEvR,SAAS,EAAEJ,WAAW,EAAE;EAChI,IAAI;IACF,IAAIwJ,MAAM,GAAG,MAAMgI,aAAa,CAACK,KAAK,CAAC/F,OAAO,EAAE;MAC9CgG,cAAc,EAAEJ,WAAW;MAC3BO,0BAA0B,EAAEvC,KAAK,CAACvP,MAAM,CAAC4C,aAAa,GAAG,MAAO8O,KAAK,IAAK;QACxE,IAAI;UACF,IAAIK,WAAW,GAAG,MAAML,KAAK,CAAC/F,OAAO,CAAC;UACtC,IAAI,CAACvN,UAAU,CAAC2T,WAAW,CAAC,EAAE;YAC5BA,WAAW,GAAG,MAAM+E,UAAU,CAAC/E,WAAW,EAAE9R,SAAS,CAAC;UACxD;UACA,OAAO8R,WAAW;QACpB,CAAC,CAAC,OAAOnL,KAAK,EAAE;UACd4K,WAAW,CAAC5K,KAAK,CAAC;UAClB,OAAO,IAAIoF,QAAQ,CAAC,IAAI,EAAE;YAAED,MAAM,EAAE;UAAI,CAAC,CAAC;QAC5C;MACF,CAAC,GAAG,KAAK;IACX,CAAC,CAAC;IACF,IAAI,CAAC3N,UAAU,CAACiL,MAAM,CAAC,EAAE;MACvBA,MAAM,GAAG,MAAMyN,UAAU,CAACzN,MAAM,EAAEpJ,SAAS,CAAC;IAC9C;IACA,OAAOoJ,MAAM;EACf,CAAC,CAAC,OAAOzC,KAAK,EAAE;IACd4K,WAAW,CAAC5K,KAAK,CAAC;IAClB,OAAO,IAAIoF,QAAQ,CAAC,IAAI,EAAE;MAAED,MAAM,EAAE;IAAI,CAAC,CAAC;EAC5C;EACA,eAAe+K,UAAUA,CAACvX,OAAO,EAAEwX,UAAU,EAAE;IAC7C,IAAInK,OAAO,GAAG0C,kBAAkB,CAAC/P,OAAO,EAAEgQ,KAAK,CAAC;IAChD,IAAI4B,2BAA2B,CAACD,GAAG,CAAC3R,OAAO,CAAC6S,UAAU,CAAC,EAAE;MACvD,OAAO,IAAIpG,QAAQ,CAAC,IAAI,EAAE;QAAED,MAAM,EAAExM,OAAO,CAAC6S,UAAU;QAAExF;MAAQ,CAAC,CAAC;IACpE;IACA,IAAIrN,OAAO,CAACoL,MAAM,EAAE;MAClBZ,MAAM,CAACiE,MAAM,CAACzO,OAAO,CAACoL,MAAM,CAAC,CAACsD,OAAO,CAAEoE,GAAG,IAAK;QAC7C,IAAI,CAAChU,oBAAoB,CAACgU,GAAG,CAAC,IAAIA,GAAG,CAACzL,KAAK,EAAE;UAC3C4K,WAAW,CAACa,GAAG,CAAC;QAClB;MACF,CAAC,CAAC;MACF9S,OAAO,CAACoL,MAAM,GAAGD,cAAc,CAACnL,OAAO,CAACoL,MAAM,EAAEJ,UAAU,CAAC;IAC7D;IACA,IAAIhJ,KAAK,GAAG;MACVpB,UAAU,EAAEZ,OAAO,CAACY,UAAU;MAC9B6P,UAAU,EAAEzQ,OAAO,CAACyQ,UAAU;MAC9BrF,MAAM,EAAEK,eAAe,CAACzL,OAAO,CAACoL,MAAM,EAAEJ,UAAU;IACpD,CAAC;IACD,IAAIyM,iBAAiB,GAAG;MACtBzL,QAAQ,EAAEgE,KAAK,CAAChE,QAAQ;MACxBvL,MAAM,EAAEuP,KAAK,CAACvP,MAAM;MACpBmB,cAAc,EAAEoO,KAAK,CAACpO,cAAc;MACpCD,GAAG,EAAEqO,KAAK,CAACrO,GAAG;MACdjB,SAAS,EAAE8W;IACb,CAAC;IACD,IAAIE,YAAY,GAAG;MACjBtX,QAAQ,EAAE4P,KAAK,CAAC0G,MAAM;MACtBrW,YAAY,EAAEkK,uBAAuB,CAACyF,KAAK,CAACxP,MAAM,CAAC;MACnDG,oBAAoB,EAAEX,OAAO;MAC7BM,WAAW;MACXC,mBAAmB,EAAEqP,yBAAyB,CAAC;QAC7C,GAAG6H,iBAAiB;QACpBnX;MACF,CAAC,CAAC;MACF4B,mBAAmB,EAAEwR,oBAAoB,CACvC1R,KAAK,EACLoK,OAAO,CAACkB,MAAM,EACd0C,KAAK,CAAC1M,KAAK,CAACE,MAAM,CAACmQ,aAAa,EAChC3I,UACF,CAAC;MACDlJ,UAAU,EAAE,CAAC,CAAC;MACdrB,MAAM,EAAEuP,KAAK,CAACvP,MAAM;MACpBkB,GAAG,EAAEqO,KAAK,CAACrO,GAAG;MACdC,cAAc,EAAEoO,KAAK,CAACpO,cAAc;MACpClB,SAAS,EAAE8W,UAAU;MACrB3V,cAAc,EAAGiR,GAAG,IAAKjR,cAAc,CAACiR,GAAG,EAAE9H,UAAU;IACzD,CAAC;IACD,IAAI2M,6BAA6B,GAAG3H,KAAK,CAAC1M,KAAK,CAACE,MAAM,CAACgC,OAAO;IAC9D,IAAI;MACF,OAAO,MAAMmS,6BAA6B,CACxCvL,OAAO,EACPpM,OAAO,CAAC6S,UAAU,EAClBxF,OAAO,EACPqK,YAAY,EACZ1F,WACF,CAAC;IACH,CAAC,CAAC,OAAO3K,KAAK,EAAE;MACd4K,WAAW,CAAC5K,KAAK,CAAC;MAClB,IAAIuQ,oBAAoB,GAAGvQ,KAAK;MAChC,IAAIxI,UAAU,CAACwI,KAAK,CAAC,EAAE;QACrB,IAAI;UACF,IAAInB,KAAK,GAAG,MAAM2R,cAAc,CAACxQ,KAAK,CAAC;UACvCuQ,oBAAoB,GAAG,IAAIhb,iBAAiB,CAC1CyK,KAAK,CAACmF,MAAM,EACZnF,KAAK,CAACqN,UAAU,EAChBxO,KACF,CAAC;QACH,CAAC,CAAC,OAAOqI,CAAC,EAAE,CACZ;MACF;MACAvO,OAAO,GAAGzB,yBAAyB,CACjCuT,aAAa,CAACgD,UAAU,EACxB9U,OAAO,EACP4X,oBACF,CAAC;MACD,IAAI5X,OAAO,CAACoL,MAAM,EAAE;QAClBpL,OAAO,CAACoL,MAAM,GAAGD,cAAc,CAACnL,OAAO,CAACoL,MAAM,EAAEJ,UAAU,CAAC;MAC7D;MACA,IAAI8M,MAAM,GAAG;QACXlX,UAAU,EAAEZ,OAAO,CAACY,UAAU;QAC9B6P,UAAU,EAAEzQ,OAAO,CAACyQ,UAAU;QAC9BrF,MAAM,EAAEK,eAAe,CAACzL,OAAO,CAACoL,MAAM,EAAEJ,UAAU;MACpD,CAAC;MACD0M,YAAY,GAAG;QACb,GAAGA,YAAY;QACf/W,oBAAoB,EAAEX,OAAO;QAC7BO,mBAAmB,EAAEqP,yBAAyB,CAAC6H,iBAAiB,CAAC;QACjEvV,mBAAmB,EAAEwR,oBAAoB,CACvCoE,MAAM,EACN1L,OAAO,CAACkB,MAAM,EACd0C,KAAK,CAAC1M,KAAK,CAACE,MAAM,CAACmQ,aAAa,EAChC3I,UACF,CAAC;QACDlJ,UAAU,EAAE,CAAC;MACf,CAAC;MACD,IAAI;QACF,OAAO,MAAM6V,6BAA6B,CACxCvL,OAAO,EACPpM,OAAO,CAAC6S,UAAU,EAClBxF,OAAO,EACPqK,YAAY,EACZ1F,WACF,CAAC;MACH,CAAC,CAAC,OAAO+F,MAAM,EAAE;QACf9F,WAAW,CAAC8F,MAAM,CAAC;QACnB,OAAOxC,6BAA6B,CAACwC,MAAM,EAAE/M,UAAU,CAAC;MAC1D;IACF;EACF;AACF;AACA,eAAesL,qBAAqBA,CAACtL,UAAU,EAAEgF,KAAK,EAAE8B,aAAa,EAAE/Q,OAAO,EAAEqL,OAAO,EAAE4F,WAAW,EAAEC,WAAW,EAAE;EACjH,IAAI;IACF,IAAInI,MAAM,GAAG,MAAMgI,aAAa,CAACkG,UAAU,CAAC5L,OAAO,EAAE;MACnDrL,OAAO;MACPqR,cAAc,EAAEJ,WAAW;MAC3BO,0BAA0B,EAAEvC,KAAK,CAACvP,MAAM,CAAC4C,aAAa,GAAG,MAAO2U,UAAU,IAAK;QAC7E,IAAI;UACF,IAAIxF,WAAW,GAAG,MAAMwF,UAAU,CAAC5L,OAAO,CAAC;UAC3C,OAAO6L,sBAAsB,CAACzF,WAAW,CAAC;QAC5C,CAAC,CAAC,OAAOnL,KAAK,EAAE;UACd,OAAO6Q,qBAAqB,CAAC7Q,KAAK,CAAC;QACrC;MACF,CAAC,GAAG,KAAK;IACX,CAAC,CAAC;IACF,OAAO4Q,sBAAsB,CAACnO,MAAM,CAAC;EACvC,CAAC,CAAC,OAAOzC,KAAK,EAAE;IACd,OAAO6Q,qBAAqB,CAAC7Q,KAAK,CAAC;EACrC;EACA,SAAS4Q,sBAAsBA,CAACnO,MAAM,EAAE;IACtC,IAAIjL,UAAU,CAACiL,MAAM,CAAC,EAAE;MACtB,OAAOA,MAAM;IACf;IACA,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;MAC9B,OAAO,IAAI2C,QAAQ,CAAC3C,MAAM,CAAC;IAC7B;IACA,OAAO2C,QAAQ,CAAC6K,IAAI,CAACxN,MAAM,CAAC;EAC9B;EACA,SAASoO,qBAAqBA,CAAC7Q,KAAK,EAAE;IACpC,IAAIxI,UAAU,CAACwI,KAAK,CAAC,EAAE;MACrB,OAAOA,KAAK;IACd;IACA,IAAIvI,oBAAoB,CAACuI,KAAK,CAAC,EAAE;MAC/B4K,WAAW,CAAC5K,KAAK,CAAC;MAClB,OAAO8Q,mBAAmB,CAAC9Q,KAAK,EAAE2D,UAAU,CAAC;IAC/C;IACA,IAAI3D,KAAK,YAAYpD,KAAK,IAAIoD,KAAK,CAACmE,OAAO,KAAK,qCAAqC,EAAE;MACrF,IAAI4M,QAAQ,GAAG,IAAInU,KAAK,CACtB,gEACF,CAAC;MACDgO,WAAW,CAACmG,QAAQ,CAAC;MACrB,OAAO7C,6BAA6B,CAAC6C,QAAQ,EAAEpN,UAAU,CAAC;IAC5D;IACAiH,WAAW,CAAC5K,KAAK,CAAC;IAClB,OAAOkO,6BAA6B,CAAClO,KAAK,EAAE2D,UAAU,CAAC;EACzD;AACF;AACA,SAASmN,mBAAmBA,CAACE,aAAa,EAAErN,UAAU,EAAE;EACtD,OAAOyB,QAAQ,CAAC6K,IAAI,CAClBzV,cAAc;EACZ;EACAwW,aAAa,CAAChR,KAAK,IAAI,IAAIpD,KAAK,CAAC,yBAAyB,CAAC,EAC3D+G,UACF,CAAC,EACD;IACEwB,MAAM,EAAE6L,aAAa,CAAC7L,MAAM;IAC5BkI,UAAU,EAAE2D,aAAa,CAAC3D;EAC5B,CACF,CAAC;AACH;AACA,SAASa,6BAA6BA,CAAClO,KAAK,EAAE2D,UAAU,EAAE;EACxD,IAAIQ,OAAO,GAAG,yBAAyB;EACvC,IAAIR,UAAU,KAAK,YAAY,CAAC,kBAAkB;IAChDQ,OAAO,IAAI;AACf;AACA,EAAE9E,MAAM,CAACW,KAAK,CAAC,EAAE;EACf;EACA,OAAO,IAAIoF,QAAQ,CAACjB,OAAO,EAAE;IAC3BgB,MAAM,EAAE,GAAG;IACXa,OAAO,EAAE;MACP,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;AACJ;AACA,SAASwK,cAAcA,CAAC3B,QAAQ,EAAE;EAChC,IAAIoC,WAAW,GAAGpC,QAAQ,CAAC7I,OAAO,CAACiB,GAAG,CAAC,cAAc,CAAC;EACtD,OAAOgK,WAAW,IAAI,uBAAuB,CAACC,IAAI,CAACD,WAAW,CAAC,GAAGpC,QAAQ,CAAC9I,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG8I,QAAQ,CAACoB,IAAI,CAAC,CAAC,GAAGpB,QAAQ,CAACsC,IAAI,CAAC,CAAC;AACpI;;AAEA;AACA,SAASC,KAAKA,CAACjR,IAAI,EAAE;EACnB,OAAO,WAAWA,IAAI,IAAI;AAC5B;AACA,IAAIkR,aAAa,GAAGA,CAACC,WAAW,GAAG,CAAC,CAAC,EAAE1X,EAAE,GAAG,EAAE,KAAK;EACjD,IAAI+C,GAAG,GAAG,IAAI4U,GAAG,CAACpO,MAAM,CAACa,OAAO,CAACsN,WAAW,CAAC,CAAC;EAC9C,OAAO;IACL,IAAI1X,EAAEA,CAAA,EAAG;MACP,OAAOA,EAAE;IACX,CAAC;IACD,IAAIyO,IAAIA,CAAA,EAAG;MACT,OAAOlF,MAAM,CAACoK,WAAW,CAAC5Q,GAAG,CAAC;IAChC,CAAC;IACD2N,GAAGA,CAACnK,IAAI,EAAE;MACR,OAAOxD,GAAG,CAAC2N,GAAG,CAACnK,IAAI,CAAC,IAAIxD,GAAG,CAAC2N,GAAG,CAAC8G,KAAK,CAACjR,IAAI,CAAC,CAAC;IAC9C,CAAC;IACD8G,GAAGA,CAAC9G,IAAI,EAAE;MACR,IAAIxD,GAAG,CAAC2N,GAAG,CAACnK,IAAI,CAAC,EAAE,OAAOxD,GAAG,CAACsK,GAAG,CAAC9G,IAAI,CAAC;MACvC,IAAIqR,SAAS,GAAGJ,KAAK,CAACjR,IAAI,CAAC;MAC3B,IAAIxD,GAAG,CAAC2N,GAAG,CAACkH,SAAS,CAAC,EAAE;QACtB,IAAInX,KAAK,GAAGsC,GAAG,CAACsK,GAAG,CAACuK,SAAS,CAAC;QAC9B7U,GAAG,CAAC6I,MAAM,CAACgM,SAAS,CAAC;QACrB,OAAOnX,KAAK;MACd;MACA,OAAO,KAAK,CAAC;IACf,CAAC;IACD+R,GAAGA,CAACjM,IAAI,EAAE9F,KAAK,EAAE;MACfsC,GAAG,CAACyP,GAAG,CAACjM,IAAI,EAAE9F,KAAK,CAAC;IACtB,CAAC;IACD+W,KAAKA,CAACjR,IAAI,EAAE9F,KAAK,EAAE;MACjBsC,GAAG,CAACyP,GAAG,CAACgF,KAAK,CAACjR,IAAI,CAAC,EAAE9F,KAAK,CAAC;IAC7B,CAAC;IACDoX,KAAKA,CAACtR,IAAI,EAAE;MACVxD,GAAG,CAAC6I,MAAM,CAACrF,IAAI,CAAC;IAClB;EACF,CAAC;AACH,CAAC;AACD,IAAIuR,SAAS,GAAI9P,MAAM,IAAK;EAC1B,OAAOA,MAAM,IAAI,IAAI,IAAI,OAAOA,MAAM,CAAChI,EAAE,KAAK,QAAQ,IAAI,OAAOgI,MAAM,CAACyG,IAAI,KAAK,WAAW,IAAI,OAAOzG,MAAM,CAAC0I,GAAG,KAAK,UAAU,IAAI,OAAO1I,MAAM,CAACqF,GAAG,KAAK,UAAU,IAAI,OAAOrF,MAAM,CAACwK,GAAG,KAAK,UAAU,IAAI,OAAOxK,MAAM,CAACwP,KAAK,KAAK,UAAU,IAAI,OAAOxP,MAAM,CAAC6P,KAAK,KAAK,UAAU;AACtR,CAAC;AACD,SAASE,oBAAoBA,CAAC;EAC5BlS,MAAM,EAAEmS,SAAS;EACjBC,UAAU;EACVC,QAAQ;EACRC,UAAU;EACVC;AACF,CAAC,EAAE;EACD,IAAIvS,MAAM,GAAGkC,QAAQ,CAACiQ,SAAS,CAAC,GAAGA,SAAS,GAAGnR,YAAY,CAACmR,SAAS,EAAEzR,IAAI,IAAI,WAAW,EAAEyR,SAAS,CAAC;EACtGK,iCAAiC,CAACxS,MAAM,CAAC;EACzC,OAAO;IACL,MAAMyS,UAAUA,CAAC9Q,YAAY,EAAER,OAAO,EAAE;MACtC,IAAIhH,EAAE,GAAGwH,YAAY,KAAI,MAAM3B,MAAM,CAAClB,KAAK,CAAC6C,YAAY,EAAER,OAAO,CAAC;MAClE,IAAI/B,KAAK,GAAGjF,EAAE,KAAI,MAAMkY,QAAQ,CAAClY,EAAE,CAAC;MACpC,OAAOyX,aAAa,CAACxS,KAAK,IAAI,CAAC,CAAC,EAAEjF,EAAE,IAAI,EAAE,CAAC;IAC7C,CAAC;IACD,MAAMuY,aAAaA,CAACC,OAAO,EAAExR,OAAO,EAAE;MACpC,IAAI;QAAEhH,EAAE;QAAEyO,IAAI,EAAExJ;MAAM,CAAC,GAAGuT,OAAO;MACjC,IAAIrR,OAAO,GAAGH,OAAO,EAAEK,MAAM,IAAI,IAAI,GAAG,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGP,OAAO,CAACK,MAAM,GAAG,GAAG,CAAC,GAAGL,OAAO,EAAEG,OAAO,IAAI,IAAI,GAAGH,OAAO,CAACG,OAAO,GAAGtB,MAAM,CAACsB,OAAO;MACjJ,IAAInH,EAAE,EAAE;QACN,MAAMmY,UAAU,CAACnY,EAAE,EAAEiF,KAAK,EAAEkC,OAAO,CAAC;MACtC,CAAC,MAAM;QACLnH,EAAE,GAAG,MAAMiY,UAAU,CAAChT,KAAK,EAAEkC,OAAO,CAAC;MACvC;MACA,OAAOtB,MAAM,CAACjB,SAAS,CAAC5E,EAAE,EAAEgH,OAAO,CAAC;IACtC,CAAC;IACD,MAAMyR,cAAcA,CAACD,OAAO,EAAExR,OAAO,EAAE;MACrC,MAAMoR,UAAU,CAACI,OAAO,CAACxY,EAAE,CAAC;MAC5B,OAAO6F,MAAM,CAACjB,SAAS,CAAC,EAAE,EAAE;QAC1B,GAAGoC,OAAO;QACVK,MAAM,EAAE,KAAK,CAAC;QACdF,OAAO,EAAE,eAAgB,IAAIG,IAAI,CAAC,CAAC;MACrC,CAAC,CAAC;IACJ;EACF,CAAC;AACH;AACA,SAAS+Q,iCAAiCA,CAACxS,MAAM,EAAE;EACjDpH,QAAQ,CACNoH,MAAM,CAACuB,QAAQ,EACf,QAAQvB,MAAM,CAACU,IAAI,6OACrB,CAAC;AACH;;AAEA;AACA,SAASmS,0BAA0BA,CAAC;EAAE7S,MAAM,EAAEmS;AAAU,CAAC,GAAG,CAAC,CAAC,EAAE;EAC9D,IAAInS,MAAM,GAAGkC,QAAQ,CAACiQ,SAAS,CAAC,GAAGA,SAAS,GAAGnR,YAAY,CAACmR,SAAS,EAAEzR,IAAI,IAAI,WAAW,EAAEyR,SAAS,CAAC;EACtGK,iCAAiC,CAACxS,MAAM,CAAC;EACzC,OAAO;IACL,MAAMyS,UAAUA,CAAC9Q,YAAY,EAAER,OAAO,EAAE;MACtC,OAAOyQ,aAAa,CAClBjQ,YAAY,KAAI,MAAM3B,MAAM,CAAClB,KAAK,CAAC6C,YAAY,EAAER,OAAO,CAAC,KAAI,CAAC,CAChE,CAAC;IACH,CAAC;IACD,MAAMuR,aAAaA,CAACC,OAAO,EAAExR,OAAO,EAAE;MACpC,IAAI2R,gBAAgB,GAAG,MAAM9S,MAAM,CAACjB,SAAS,CAAC4T,OAAO,CAAC/J,IAAI,EAAEzH,OAAO,CAAC;MACpE,IAAI2R,gBAAgB,CAACjS,MAAM,GAAG,IAAI,EAAE;QAClC,MAAM,IAAI1D,KAAK,CACb,qDAAqD,GAAG2V,gBAAgB,CAACjS,MAC3E,CAAC;MACH;MACA,OAAOiS,gBAAgB;IACzB,CAAC;IACD,MAAMF,cAAcA,CAACG,QAAQ,EAAE5R,OAAO,EAAE;MACtC,OAAOnB,MAAM,CAACjB,SAAS,CAAC,EAAE,EAAE;QAC1B,GAAGoC,OAAO;QACVK,MAAM,EAAE,KAAK,CAAC;QACdF,OAAO,EAAE,eAAgB,IAAIG,IAAI,CAAC,CAAC;MACrC,CAAC,CAAC;IACJ;EACF,CAAC;AACH;;AAEA;AACA,SAASuR,0BAA0BA,CAAC;EAAEhT;AAAO,CAAC,GAAG,CAAC,CAAC,EAAE;EACnD,IAAI9C,GAAG,GAAG,eAAgB,IAAI4U,GAAG,CAAC,CAAC;EACnC,OAAOI,oBAAoB,CAAC;IAC1BlS,MAAM;IACN,MAAMoS,UAAUA,CAAChT,KAAK,EAAEkC,OAAO,EAAE;MAC/B,IAAInH,EAAE,GAAG8Y,IAAI,CAACC,MAAM,CAAC,CAAC,CAACnQ,QAAQ,CAAC,EAAE,CAAC,CAACoQ,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;MACpDjW,GAAG,CAACyP,GAAG,CAACxS,EAAE,EAAE;QAAEyO,IAAI,EAAExJ,KAAK;QAAEkC;MAAQ,CAAC,CAAC;MACrC,OAAOnH,EAAE;IACX,CAAC;IACD,MAAMkY,QAAQA,CAAClY,EAAE,EAAE;MACjB,IAAI+C,GAAG,CAAC2N,GAAG,CAAC1Q,EAAE,CAAC,EAAE;QACf,IAAI;UAAEyO,IAAI,EAAExJ,KAAK;UAAEkC;QAAQ,CAAC,GAAGpE,GAAG,CAACsK,GAAG,CAACrN,EAAE,CAAC;QAC1C,IAAI,CAACmH,OAAO,IAAIA,OAAO,GAAG,eAAgB,IAAIG,IAAI,CAAC,CAAC,EAAE;UACpD,OAAOrC,KAAK;QACd;QACA,IAAIkC,OAAO,EAAEpE,GAAG,CAAC6I,MAAM,CAAC5L,EAAE,CAAC;MAC7B;MACA,OAAO,IAAI;IACb,CAAC;IACD,MAAMmY,UAAUA,CAACnY,EAAE,EAAEiF,KAAK,EAAEkC,OAAO,EAAE;MACnCpE,GAAG,CAACyP,GAAG,CAACxS,EAAE,EAAE;QAAEyO,IAAI,EAAExJ,KAAK;QAAEkC;MAAQ,CAAC,CAAC;IACvC,CAAC;IACD,MAAMiR,UAAUA,CAACpY,EAAE,EAAE;MACnB+C,GAAG,CAAC6I,MAAM,CAAC5L,EAAE,CAAC;IAChB;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,SAASwM,IAAIA,CAACtJ,IAAI,EAAE,GAAGK,IAAI,EAAE;EAC3B,IAAIyH,MAAM,GAAGzH,IAAI,CAAC,CAAC,CAAC;EACpB,IAAIsF,MAAM,GAAG3F,IAAI,CAAChF,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAACA,OAAO,CAC9C,mBAAmB;EACnB;EACA,CAAC+X,CAAC,EAAEgD,KAAK,EAAEC,YAAY,KAAK;IAC1B,MAAMC,UAAU,GAAGD,YAAY,KAAK,KAAK,CAAC;IAC1C,MAAMzY,KAAK,GAAGuK,MAAM,GAAGA,MAAM,CAACiO,KAAK,CAAC,GAAG,KAAK,CAAC;IAC7C,IAAIE,UAAU,IAAI1Y,KAAK,KAAK,KAAK,CAAC,EAAE;MAClC,MAAM,IAAIuC,KAAK,CACb,SAASE,IAAI,qBAAqB+V,KAAK,2BACzC,CAAC;IACH;IACA,OAAOxY,KAAK,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,GAAGA,KAAK;EAC5C,CACF,CAAC;EACD,IAAIyC,IAAI,CAACuR,QAAQ,CAAC,GAAG,CAAC,EAAE;IACtB,MAAMhU,KAAK,GAAGuK,MAAM,GAAGA,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;IAC3C,IAAIvK,KAAK,KAAK,KAAK,CAAC,EAAE;MACpBoI,MAAM,IAAI,GAAG,GAAGpI,KAAK;IACvB;EACF;EACA,OAAOoI,MAAM,IAAI,GAAG;AACtB;;AAEA;AACA,OAAO,KAAKuQ,MAAM,MAAM,OAAO;AAC/B,OAAO,KAAKC,QAAQ,MAAM,WAAW;;AAErC;AACA,SAASC,gBAAgBA,CAAC;EACxBvY,KAAK;EACLxB,MAAM;EACNga,YAAY;EACZzY,QAAQ,EAAE0Y,SAAS;EACnBzO,QAAQ;EACRtL;AACF,CAAC,EAAE;EACD,IAAIqC,aAAa,GAAG;IAClB,GAAGf,KAAK;IACRpB,UAAU,EAAE;MAAE,GAAGoB,KAAK,CAACpB;IAAW;EACpC,CAAC;EACD,IAAI8Z,cAAc,GAAG3b,WAAW,CAACyB,MAAM,EAAEia,SAAS,EAAEzO,QAAQ,CAAC;EAC7D,IAAI0O,cAAc,EAAE;IAClB,KAAK,IAAI7Z,KAAK,IAAI6Z,cAAc,EAAE;MAChC,IAAI3Z,OAAO,GAAGF,KAAK,CAACG,KAAK,CAACC,EAAE;MAC5B,IAAI0Z,SAAS,GAAGH,YAAY,CAACzZ,OAAO,CAAC;MACrC,IAAI1B,wBAAwB,CAC1B0B,OAAO,EACP4Z,SAAS,CAACxZ,YAAY,EACtBwZ,SAAS,CAACvZ,SAAS,EACnBV,SACF,CAAC,KAAKia,SAAS,CAACC,kBAAkB,IAAI,CAACD,SAAS,CAACvZ,SAAS,CAAC,EAAE;QAC3D,OAAO2B,aAAa,CAACnC,UAAU,CAACG,OAAO,CAAC;MAC1C,CAAC,MAAM,IAAI,CAAC4Z,SAAS,CAACvZ,SAAS,EAAE;QAC/B2B,aAAa,CAACnC,UAAU,CAACG,OAAO,CAAC,GAAG,IAAI;MAC1C;IACF;EACF;EACA,OAAOgC,aAAa;AACtB;;AAEA;AACA,OAAO8X,MAAM,MAAM,OAAO;AAC1B,IAAIC,4BAA4B,GAAG,cAAcD,MAAM,CAACxW,SAAS,CAAC;EAChE0W,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAAChZ,KAAK,GAAG;MAAEqF,KAAK,EAAE,IAAI;MAAEtF,QAAQ,EAAEiZ,KAAK,CAACjZ;IAAS,CAAC;EACxD;EACA,OAAOkZ,wBAAwBA,CAAC5T,KAAK,EAAE;IACrC,OAAO;MAAEA;IAAM,CAAC;EAClB;EACA,OAAO6T,wBAAwBA,CAACF,KAAK,EAAEhZ,KAAK,EAAE;IAC5C,IAAIA,KAAK,CAACD,QAAQ,KAAKiZ,KAAK,CAACjZ,QAAQ,EAAE;MACrC,OAAO;QAAEsF,KAAK,EAAE,IAAI;QAAEtF,QAAQ,EAAEiZ,KAAK,CAACjZ;MAAS,CAAC;IAClD;IACA,OAAO;MAAEsF,KAAK,EAAErF,KAAK,CAACqF,KAAK;MAAEtF,QAAQ,EAAEC,KAAK,CAACD;IAAS,CAAC;EACzD;EACAoZ,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACnZ,KAAK,CAACqF,KAAK,EAAE;MACpB,OAAO,eAAgBwT,MAAM,CAACtZ,aAAa,CACzC6Z,+BAA+B,EAC/B;QACE/T,KAAK,EAAE,IAAI,CAACrF,KAAK,CAACqF,KAAK;QACvBgU,cAAc,EAAE;MAClB,CACF,CAAC;IACH,CAAC,MAAM;MACL,OAAO,IAAI,CAACL,KAAK,CAACrV,QAAQ;IAC5B;EACF;AACF,CAAC;AACD,SAAS2V,YAAYA,CAAC;EACpBD,cAAc;EACdE,KAAK;EACL5V;AACF,CAAC,EAAE;EACD,IAAI,CAAC0V,cAAc,EAAE;IACnB,OAAO1V,QAAQ;EACjB;EACA,OAAO,eAAgBkV,MAAM,CAACtZ,aAAa,CAAC,MAAM,EAAE;IAAEia,IAAI,EAAE;EAAK,CAAC,EAAE,eAAgBX,MAAM,CAACtZ,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,eAAgBsZ,MAAM,CAACtZ,aAAa,CAAC,MAAM,EAAE;IAAEka,OAAO,EAAE;EAAQ,CAAC,CAAC,EAAE,eAAgBZ,MAAM,CAACtZ,aAAa,CAC7N,MAAM,EACN;IACEiG,IAAI,EAAE,UAAU;IAChBkU,OAAO,EAAE;EACX,CACF,CAAC,EAAE,eAAgBb,MAAM,CAACtZ,aAAa,CAAC,OAAO,EAAE,IAAI,EAAEga,KAAK,CAAC,CAAC,EAAE,eAAgBV,MAAM,CAACtZ,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,eAAgBsZ,MAAM,CAACtZ,aAAa,CAAC,MAAM,EAAE;IAAEoa,KAAK,EAAE;MAAEC,UAAU,EAAE,uBAAuB;MAAEC,OAAO,EAAE;IAAO;EAAE,CAAC,EAAElW,QAAQ,CAAC,CAAC,CAAC;AAClP;AACA,SAASyV,+BAA+BA,CAAC;EACvC/T,KAAK;EACLgU;AACF,CAAC,EAAE;EACD1N,OAAO,CAACtG,KAAK,CAACA,KAAK,CAAC;EACpB,IAAIyU,YAAY,GAAG,eAAgBjB,MAAM,CAACtZ,aAAa,CACrD,QAAQ,EACR;IACEwa,uBAAuB,EAAE;MACvBC,MAAM,EAAE;AAChB;AACA;AACA;AACA;IACM;EACF,CACF,CAAC;EACD,IAAIld,oBAAoB,CAACuI,KAAK,CAAC,EAAE;IAC/B,OAAO,eAAgBwT,MAAM,CAACtZ,aAAa,CACzC+Z,YAAY,EACZ;MACED,cAAc;MACdE,KAAK,EAAE;IACT,CAAC,EACD,eAAgBV,MAAM,CAACtZ,aAAa,CAAC,IAAI,EAAE;MAAEoa,KAAK,EAAE;QAAEM,QAAQ,EAAE;MAAO;IAAE,CAAC,EAAE5U,KAAK,CAACmF,MAAM,EAAE,GAAG,EAAEnF,KAAK,CAACqN,UAAU,CAAC,EAChH/X,mBAAmB,GAAGmf,YAAY,GAAG,IACvC,CAAC;EACH;EACA,IAAII,aAAa;EACjB,IAAI7U,KAAK,YAAYpD,KAAK,EAAE;IAC1BiY,aAAa,GAAG7U,KAAK;EACvB,CAAC,MAAM;IACL,IAAI8U,WAAW,GAAG9U,KAAK,IAAI,IAAI,GAAG,eAAe,GAAG,OAAOA,KAAK,KAAK,QAAQ,IAAI,UAAU,IAAIA,KAAK,GAAGA,KAAK,CAACwC,QAAQ,CAAC,CAAC,GAAGL,IAAI,CAACC,SAAS,CAACpC,KAAK,CAAC;IAC/I6U,aAAa,GAAG,IAAIjY,KAAK,CAACkY,WAAW,CAAC;EACxC;EACA,OAAO,eAAgBtB,MAAM,CAACtZ,aAAa,CAAC+Z,YAAY,EAAE;IAAED,cAAc;IAAEE,KAAK,EAAE;EAAqB,CAAC,EAAE,eAAgBV,MAAM,CAACtZ,aAAa,CAAC,IAAI,EAAE;IAAEoa,KAAK,EAAE;MAAEM,QAAQ,EAAE;IAAO;EAAE,CAAC,EAAE,mBAAmB,CAAC,EAAE,eAAgBpB,MAAM,CAACtZ,aAAa,CAC/O,KAAK,EACL;IACEoa,KAAK,EAAE;MACLE,OAAO,EAAE,MAAM;MACfO,UAAU,EAAE,yBAAyB;MACrCC,KAAK,EAAE,KAAK;MACZC,QAAQ,EAAE;IACZ;EACF,CAAC,EACDJ,aAAa,CAAChR,KAChB,CAAC,EAAE4Q,YAAY,CAAC;AAClB;AACA,SAASS,2BAA2BA,CAAC;EACnCC;AACF,CAAC,EAAE;EACD,IAAInV,KAAK,GAAG5H,aAAa,CAAC,CAAC;EAC3B,IAAI+c,aAAa,KAAK,KAAK,CAAC,EAAE;IAC5B,MAAM,IAAIvY,KAAK,CAAC,8BAA8B,CAAC;EACjD;EACA,OAAO,eAAgB4W,MAAM,CAACtZ,aAAa,CACzC6Z,+BAA+B,EAC/B;IACEC,cAAc,EAAE,CAACmB,aAAa;IAC9BnV;EACF,CACF,CAAC;AACH;;AAEA;AACA,SAASoV,qBAAqBA,CAACC,OAAO,EAAE;EACtC,MAAMrc,YAAY,GAAG,CAAC,CAAC;EACvB,KAAK,MAAMQ,KAAK,IAAI6b,OAAO,CAAC5b,OAAO,EAAE;IACnC6b,uBAAuB,CAACtc,YAAY,EAAEQ,KAAK,CAAC;EAC9C;EACA,OAAOR,YAAY;AACrB;AACA,SAASsc,uBAAuBA,CAACtc,YAAY,EAAES,OAAO,EAAE;EACtDA,OAAO,GAAG8b,KAAK,CAACC,OAAO,CAAC/b,OAAO,CAAC,GAAGA,OAAO,GAAG,CAACA,OAAO,CAAC;EACtD,KAAK,MAAMD,KAAK,IAAIC,OAAO,EAAE;IAC3BT,YAAY,CAACQ,KAAK,CAACI,EAAE,CAAC,GAAG;MACvBwE,KAAK,EAAE5E,KAAK,CAAC4E,KAAK;MAClBC,IAAI,EAAE7E,KAAK,CAAC6E,IAAI;MAChBF,OAAO,EAAEsX;IACX,CAAC;EACH;AACF;AACA,IAAIA,aAAa,GAAGA,CAAA,KAAM,IAAI;;AAE9B;AACA,SAASC,gBAAgBA,CAAC;EACxBC,wBAAwB;EACxBC,2BAA2B;EAC3BC,WAAW;EACXC,KAAK,EAAEC,mBAAmB,GAAGD;AAC/B,CAAC,EAAE;EACD,MAAME,SAAS,GAAGC,MAAM;EACxB,IAAIC,cAAc,GAAG,CAAC;EACtB,OAAO,OAAOtc,EAAE,EAAEuD,IAAI,KAAK;IACzB,IAAIgZ,QAAQ,GAAGH,SAAS,CAACI,gBAAgB,GAAG,CAACJ,SAAS,CAACI,gBAAgB,KAAKJ,SAAS,CAACI,gBAAgB,GAAG,CAAC,CAAC,IAAI,CAAC;IAChH,MAAMC,mBAAmB,GAAGT,2BAA2B,CAAC,CAAC;IACzD,MAAMU,cAAc,GAAGP,mBAAmB,CACxC,IAAI5P,OAAO,CAACzL,QAAQ,CAAC0L,IAAI,EAAE;MACzBL,IAAI,EAAE,MAAM8P,WAAW,CAAC1Y,IAAI,EAAE;QAAEkZ;MAAoB,CAAC,CAAC;MACtDvQ,MAAM,EAAE,MAAM;MACdE,OAAO,EAAE;QACPuQ,MAAM,EAAE,kBAAkB;QAC1B,eAAe,EAAE3c;MACnB;IACF,CAAC,CACH,CAAC,CAAC4c,IAAI,CAAE3H,QAAQ,IAAK;MACnB,IAAI,CAACA,QAAQ,CAAC9I,IAAI,EAAE;QAClB,MAAM,IAAInJ,KAAK,CAAC,kBAAkB,CAAC;MACrC;MACA,OAAO+Y,wBAAwB,CAAC9G,QAAQ,CAAC9I,IAAI,EAAE;QAC7CsQ;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IACFL,SAAS,CAACS,uBAAuB,CAACC,oBAAoB,CACpDC,OAAO,CAACC,OAAO,CAACN,cAAc,CAAC,CAACE,IAAI,CAAC,MAAOnB,OAAO,IAAK;MACtD,IAAIA,OAAO,CAACwB,IAAI,KAAK,UAAU,EAAE;QAC/B,IAAIxB,OAAO,CAACjN,MAAM,EAAE;UAClB6N,MAAM,CAACvb,QAAQ,CAAC0L,IAAI,GAAGiP,OAAO,CAAC3a,QAAQ;UACvC,OAAO,MAAM,CACb,CAAC;QACH;QACA,OAAO,MAAM;UACXsb,SAAS,CAACS,uBAAuB,CAACK,QAAQ,CAACzB,OAAO,CAAC3a,QAAQ,EAAE;YAC3D5C,OAAO,EAAEud,OAAO,CAACvd;UACnB,CAAC,CAAC;QACJ,CAAC;MACH;MACA,IAAIud,OAAO,CAACwB,IAAI,KAAK,QAAQ,EAAE;QAC7B,MAAM,IAAIja,KAAK,CAAC,yBAAyB,CAAC;MAC5C;MACA,MAAMma,QAAQ,GAAG,MAAM1B,OAAO,CAAC0B,QAAQ;MACvC,IAAIA,QAAQ,IAAIb,cAAc,GAAGC,QAAQ,IAAIH,SAAS,CAACI,gBAAgB,IAAID,QAAQ,EAAE;QACnF,IAAIY,QAAQ,CAACF,IAAI,KAAK,UAAU,EAAE;UAChC,IAAIE,QAAQ,CAAC3O,MAAM,EAAE;YACnB6N,MAAM,CAACvb,QAAQ,CAAC0L,IAAI,GAAG2Q,QAAQ,CAACrc,QAAQ;YACxC;UACF;UACA,OAAO,MAAM;YACXsb,SAAS,CAACS,uBAAuB,CAACK,QAAQ,CAACC,QAAQ,CAACrc,QAAQ,EAAE;cAC5D5C,OAAO,EAAEif,QAAQ,CAACjf;YACpB,CAAC,CAAC;UACJ,CAAC;QACH;QACA,OAAO,MAAM;UACX,IAAIkf,SAAS;UACb,KAAK,MAAMxd,KAAK,IAAIud,QAAQ,CAACtd,OAAO,EAAE;YACpCuc,SAAS,CAACS,uBAAuB,CAACQ,WAAW,CAC3CD,SAAS,EAAEpd,EAAE,IAAI,IAAI,EACrB,CAACsd,6BAA6B,CAAC1d,KAAK,CAAC,CAAC,EACtC,IACF,CAAC;YACDwd,SAAS,GAAGxd,KAAK;UACnB;UACAyc,MAAM,CAACQ,uBAAuB,CAACU,8CAA8C,CAC3E;YACE5d,UAAU,EAAE4J,MAAM,CAACe,MAAM,CACvB,CAAC,CAAC,EACF8R,SAAS,CAACS,uBAAuB,CAAC9b,KAAK,CAACpB,UAAU,EAClDwd,QAAQ,CAACxd,UACX,CAAC;YACDwK,MAAM,EAAEgT,QAAQ,CAAChT,MAAM,GAAGZ,MAAM,CAACe,MAAM,CACrC,CAAC,CAAC,EACF8R,SAAS,CAACS,uBAAuB,CAAC9b,KAAK,CAACoJ,MAAM,EAC9CgT,QAAQ,CAAChT,MACX,CAAC,GAAG;UACN,CACF,CAAC;QACH,CAAC;MACH;MACA,OAAO,MAAM,CACb,CAAC;IACH,CAAC,CAAC,CAACqT,KAAK,CAAC,MAAM,CACf,CAAC,CACH,CAAC;IACD,OAAOd,cAAc,CAACE,IAAI,CAAEnB,OAAO,IAAK;MACtC,IAAIA,OAAO,CAACwB,IAAI,KAAK,QAAQ,IAAIxB,OAAO,CAACwB,IAAI,KAAK,UAAU,EAAE;QAC5D,MAAM,IAAIja,KAAK,CAAC,yBAAyB,CAAC;MAC5C;MACA,OAAOyY,OAAO,CAACgC,YAAY;IAC7B,CAAC,CAAC;EACJ,CAAC;AACH;AACA,SAASC,uBAAuBA,CAAC;EAC/BvB,mBAAmB;EACnBJ,wBAAwB;EACxB4B,UAAU;EACVlC;AACF,CAAC,EAAE;EACD,MAAMW,SAAS,GAAGC,MAAM;EACxB,IAAID,SAAS,CAACS,uBAAuB,IAAIT,SAAS,CAACwB,yBAAyB,EAC1E,OAAO;IACLvd,MAAM,EAAE+b,SAAS,CAACS,uBAAuB;IACzCzd,YAAY,EAAEgd,SAAS,CAACwB;EAC1B,CAAC;EACH,IAAInC,OAAO,CAACwB,IAAI,KAAK,QAAQ,EAAE,MAAM,IAAIja,KAAK,CAAC,sBAAsB,CAAC;EACtEoZ,SAAS,CAACwB,yBAAyB,GAAGxB,SAAS,CAACwB,yBAAyB,IAAI,CAAC,CAAC;EAC/ElC,uBAAuB,CAACU,SAAS,CAACwB,yBAAyB,EAAEnC,OAAO,CAAC5b,OAAO,CAAC;EAC7E,IAAI6V,OAAO,GAAG,eAAgB,IAAIiC,GAAG,CAAC,CAAC;EACvC8D,OAAO,CAAC/F,OAAO,EAAEjI,OAAO,CAAEoQ,KAAK,IAAK;IAClCtgB,SAAS,CAACsgB,KAAK,CAAC/a,QAAQ,EAAE,wBAAwB,CAAC;IACnD,IAAI,CAAC4S,OAAO,CAAChF,GAAG,CAACmN,KAAK,CAAC/a,QAAQ,CAAC,EAAE;MAChC4S,OAAO,CAAClD,GAAG,CAACqL,KAAK,CAAC/a,QAAQ,EAAE,EAAE,CAAC;IACjC;IACA4S,OAAO,CAACrI,GAAG,CAACwQ,KAAK,CAAC/a,QAAQ,CAAC,EAAEiJ,IAAI,CAAC8R,KAAK,CAAC;EAC1C,CAAC,CAAC;EACF,IAAIte,MAAM,GAAGkc,OAAO,CAAC5b,OAAO,CAACie,WAAW,CAAC,CAACC,QAAQ,EAAEne,KAAK,KAAK;IAC5D,MAAMG,KAAK,GAAGud,6BAA6B,CACzC1d,KAAK,EACL6b,OACF,CAAC;IACD,IAAIsC,QAAQ,CAACrX,MAAM,GAAG,CAAC,EAAE;MACvB3G,KAAK,CAAC2E,QAAQ,GAAGqZ,QAAQ;MACzB,IAAIC,eAAe,GAAGtI,OAAO,CAACrI,GAAG,CAACzN,KAAK,CAACI,EAAE,CAAC;MAC3C,IAAIge,eAAe,EAAE;QACnBje,KAAK,CAAC2E,QAAQ,CAACqH,IAAI,CACjB,GAAGiS,eAAe,CAACjb,GAAG,CAAEF,CAAC,IAAKya,6BAA6B,CAACza,CAAC,CAAC,CAChE,CAAC;MACH;IACF;IACA,OAAO,CAAC9C,KAAK,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EACNqc,SAAS,CAACS,uBAAuB,GAAGhgB,YAAY,CAAC;IAC/C0C,MAAM;IACNoe,UAAU;IACV5S,QAAQ,EAAE0Q,OAAO,CAAC1Q,QAAQ;IAC1BkT,OAAO,EAAExhB,oBAAoB,CAAC,CAAC;IAC/BqF,aAAa,EAAEwX,gBAAgB,CAAC;MAC9BvY,KAAK,EAAE;QACLpB,UAAU,EAAE8b,OAAO,CAAC9b,UAAU;QAC9B6P,UAAU,EAAEiM,OAAO,CAACjM,UAAU;QAC9BrF,MAAM,EAAEsR,OAAO,CAACtR;MAClB,CAAC;MACD5K,MAAM;MACNga,YAAY,EAAGzZ,OAAO,IAAK;QACzB,IAAIF,KAAK,GAAG6b,OAAO,CAAC5b,OAAO,CAACqe,IAAI,CAAEjP,CAAC,IAAKA,CAAC,CAACjP,EAAE,KAAKF,OAAO,CAAC;QACzDvC,SAAS,CAACqC,KAAK,EAAE,4BAA4B,CAAC;QAC9C,OAAO;UACLM,YAAY,EAAEN,KAAK,CAACM,YAAY;UAChCC,SAAS,EAAEP,KAAK,CAACO,SAAS;UAC1BwZ,kBAAkB,EAAE/Z,KAAK,CAACue,sBAAsB,IAAI;QACtD,CAAC;MACH,CAAC;MACDrd,QAAQ,EAAE2a,OAAO,CAAC3a,QAAQ;MAC1BiK,QAAQ,EAAE0Q,OAAO,CAAC1Q,QAAQ;MAC1BtL,SAAS,EAAE;IACb,CAAC,CAAC;IACF,MAAM2e,uBAAuBA,CAAC;MAAElb,IAAI;MAAEmJ;IAAO,CAAC,EAAE;MAC9C,IAAIgS,eAAe,CAAC3N,GAAG,CAACxN,IAAI,CAAC,EAAE;QAC7B;MACF;MACA,MAAMob,4BAA4B,CAChC,CAACpb,IAAI,CAAC,EACN6Y,wBAAwB,EACxBI,mBAAmB,EACnB9P,MACF,CAAC;IACH,CAAC;IACD;IACAkS,YAAY,EAAEC,6BAA6B,CACzC,MAAMpC,SAAS,CAACS,uBAAuB,EACvC,IAAI,EACJpB,OAAO,CAAC1Q,QAAQ,EAChBgR,wBAAwB,EACxBI,mBACF;EACF,CAAC,CAAC;EACF,IAAIC,SAAS,CAACS,uBAAuB,CAAC9b,KAAK,CAAC0d,WAAW,EAAE;IACvDrC,SAAS,CAACsC,mBAAmB,GAAG,IAAI;IACpCtC,SAAS,CAACS,uBAAuB,CAAC8B,UAAU,CAAC,CAAC;EAChD,CAAC,MAAM;IACLvC,SAAS,CAACsC,mBAAmB,GAAG,KAAK;EACvC;EACA,IAAIE,cAAc,GAAG,KAAK,CAAC;EAC3BxC,SAAS,CAACS,uBAAuB,CAACgC,SAAS,CAAC,CAAC;IAAElf,UAAU;IAAE6P;EAAW,CAAC,KAAK;IAC1E,IAAIoP,cAAc,KAAKjf,UAAU,EAAE;MACjCyc,SAAS,CAACI,gBAAgB,GAAG,CAACJ,SAAS,CAACI,gBAAgB,KAAKJ,SAAS,CAACI,gBAAgB,GAAG,CAAC,CAAC,IAAI,CAAC;IACnG;EACF,CAAC,CAAC;EACFJ,SAAS,CAACS,uBAAuB,CAACiC,mBAAmB,GAAIC,oBAAoB,IAAK;IAChF,MAAMC,SAAS,GAAG3C,MAAM,CAACQ,uBAAuB,CAACtd,MAAM;IACvD,MAAM0f,SAAS,GAAG,EAAE;IACpB,SAASC,UAAUA,CAACC,OAAO,EAAErc,QAAQ,EAAE;MACrC,OAAOqc,OAAO,CAACpc,GAAG,CAAEhD,KAAK,IAAK;QAC5B,MAAMqf,WAAW,GAAGL,oBAAoB,CAAC1R,GAAG,CAACtN,KAAK,CAACC,EAAE,CAAC;QACtD,IAAIof,WAAW,EAAE;UACf,MAAM;YACJC,WAAW;YACXvb,SAAS;YACTwb,YAAY;YACZpb,gBAAgB;YAChB/D;UACF,CAAC,GAAGif,WAAW;UACf,MAAMnc,QAAQ,GAAGqa,6BAA6B,CAAC;YAC7CiC,YAAY,EAAEF,WAAW,CAACE,YAAY;YACtCrf,YAAY,EAAEmf,WAAW,CAACnf,YAAY;YACtCsf,OAAO,EAAEzf,KAAK,CAACyf,OAAO;YACtBC,YAAY,EAAE1f,KAAK,CAAC0f,YAAY;YAChC9b,MAAM,EAAE5D,KAAK,CAAC4D,MAAM;YACpBG,SAAS;YACTwb,YAAY;YACZpb,gBAAgB;YAChB/D,SAAS;YACTge,sBAAsB,EAAEpe,KAAK,CAACoe,sBAAsB;YACpDne,EAAE,EAAED,KAAK,CAACC,EAAE;YACZmD,KAAK,EAAEpD,KAAK,CAACoD,KAAK;YAClBqB,KAAK,EAAE6a,WAAW,CAAC7a,KAAK;YACxBC,IAAI,EAAE4a,WAAW,CAAC5a,IAAI;YACtB3B,QAAQ;YACRI,IAAI,EAAEnD,KAAK,CAACmD,IAAI;YAChBU,gBAAgB,EAAEyb,WAAW,CAACzb;UAChC,CAAC,CAAC;UACF,IAAI7D,KAAK,CAAC2E,QAAQ,EAAE;YAClBzB,QAAQ,CAACyB,QAAQ,GAAGwa,UAAU,CAACnf,KAAK,CAAC2E,QAAQ,EAAE3E,KAAK,CAACC,EAAE,CAAC;UAC1D;UACA,OAAOiD,QAAQ;QACjB;QACA,MAAMyc,YAAY,GAAG;UAAE,GAAG3f;QAAM,CAAC;QACjC,IAAIA,KAAK,CAAC2E,QAAQ,EAAE;UAClBgb,YAAY,CAAChb,QAAQ,GAAGwa,UAAU,CAACnf,KAAK,CAAC2E,QAAQ,EAAE3E,KAAK,CAACC,EAAE,CAAC;QAC9D;QACA,OAAO0f,YAAY;MACrB,CAAC,CAAC;IACJ;IACAT,SAAS,CAAClT,IAAI,CACZ,GAAGmT,UAAU,CAACF,SAAS,EAAE,KAAK,CAAC,CACjC,CAAC;IACD3C,MAAM,CAACQ,uBAAuB,CAAC8C,kBAAkB,CAACV,SAAS,CAAC;EAC9D,CAAC;EACD,OAAO;IACL5e,MAAM,EAAE+b,SAAS,CAACS,uBAAuB;IACzCzd,YAAY,EAAEgd,SAAS,CAACwB;EAC1B,CAAC;AACH;AACA,IAAIgC,qBAAqB,GAAGljB,aAAa,CAAC,CAAC;AAC3C,SAAS8hB,6BAA6BA,CAACqB,SAAS,EAAEnf,GAAG,EAAEqK,QAAQ,EAAEgR,wBAAwB,EAAEI,mBAAmB,EAAE;EAC9G,IAAIoC,YAAY,GAAGlhB,8BAA8B,CAC/CwiB,SAAS,EACRjgB,KAAK,IAAK;IACT,IAAIkgB,CAAC,GAAGlgB,KAAK;IACb,OAAO;MACLO,SAAS,EAAE2f,CAAC,CAAC/f,KAAK,CAACI,SAAS;MAC5B6D,eAAe,EAAE8b,CAAC,CAAC/f,KAAK,CAACiE,eAAe;MACxCsb,YAAY,EAAEQ,CAAC,CAAC/f,KAAK,CAACuf,YAAY;MAClCxb,SAAS,EAAEgc,CAAC,CAAC/f,KAAK,CAAC+D,SAAS;MAC5BC,eAAe,EAAE+b,CAAC,CAAC/f,KAAK,CAACgE,eAAe;MACxCgc,mBAAmB,EAAED,CAAC,CAAC/f,KAAK,CAACggB;IAC/B,CAAC;EACH,CAAC;EACD;EACAC,uBAAuB,CAACjE,wBAAwB,EAAEI,mBAAmB,CAAC,EACtEzb,GAAG,EACHqK,QAAQ;EACR;EACA;EACA;EACCnL,KAAK,IAAK;IACT,IAAIkgB,CAAC,GAAGlgB,KAAK;IACb,OAAOkgB,CAAC,CAAC/f,KAAK,CAACuf,YAAY,IAAI,CAACQ,CAAC,CAAC/f,KAAK,CAACyf,OAAO;EACjD,CACF,CAAC;EACD,OAAO,MAAOjc,IAAI,IAAKA,IAAI,CAAC0c,mBAAmB,CAAC,YAAY;IAC1D,IAAIlhB,OAAO,GAAGwE,IAAI,CAACxE,OAAO;IAC1BA,OAAO,CAACyT,GAAG,CAACoN,qBAAqB,EAAE,EAAE,CAAC;IACtC,IAAIxN,OAAO,GAAG,MAAMmM,YAAY,CAAChb,IAAI,CAAC;IACtC,MAAM2c,kBAAkB,GAAG,eAAgB,IAAIvI,GAAG,CAAC,CAAC;IACpD,KAAK,MAAM5X,KAAK,IAAIhB,OAAO,CAACsO,GAAG,CAACuS,qBAAqB,CAAC,EAAE;MACtD,IAAI,CAACM,kBAAkB,CAACxP,GAAG,CAAC3Q,KAAK,CAACC,EAAE,CAAC,EAAE;QACrCkgB,kBAAkB,CAAC1N,GAAG,CAACzS,KAAK,CAACC,EAAE,EAAE,EAAE,CAAC;MACtC;MACAkgB,kBAAkB,CAAC7S,GAAG,CAACtN,KAAK,CAACC,EAAE,CAAC,CAAC+L,IAAI,CAAChM,KAAK,CAAC;IAC9C;IACA,KAAK,MAAMH,KAAK,IAAI2D,IAAI,CAAC1D,OAAO,EAAE;MAChC,MAAMsgB,cAAc,GAAGD,kBAAkB,CAAC7S,GAAG,CAACzN,KAAK,CAACG,KAAK,CAACC,EAAE,CAAC;MAC7D,IAAImgB,cAAc,EAAE;QAClB,KAAK,MAAMC,QAAQ,IAAID,cAAc,EAAE;UACrC9D,MAAM,CAACQ,uBAAuB,CAACQ,WAAW,CACxC+C,QAAQ,CAACtd,QAAQ,IAAI,IAAI,EACzB,CAACwa,6BAA6B,CAAC8C,QAAQ,CAAC,CAAC,EACzC,IACF,CAAC;QACH;MACF;IACF;IACA,OAAOhO,OAAO;EAChB,CAAC,CAAC;AACJ;AACA,SAAS4N,uBAAuBA,CAACjE,wBAAwB,EAAEI,mBAAmB,EAAE;EAC9E,OAAO,OAAO5Y,IAAI,EAAEwH,QAAQ,EAAEsV,YAAY,KAAK;IAC7C,IAAI;MAAElV,OAAO;MAAEpM;IAAQ,CAAC,GAAGwE,IAAI;IAC/B,IAAIvE,GAAG,GAAGX,cAAc,CAAC8M,OAAO,CAACnM,GAAG,EAAE+L,QAAQ,EAAE,KAAK,CAAC;IACtD,IAAII,OAAO,CAACe,MAAM,KAAK,KAAK,EAAE;MAC5BlN,GAAG,GAAGT,eAAe,CAACS,GAAG,CAAC;MAC1B,IAAIqhB,YAAY,EAAE;QAChBrhB,GAAG,CAAC0M,YAAY,CAAC8G,GAAG,CAAC,SAAS,EAAE6N,YAAY,CAAClK,IAAI,CAAC,GAAG,CAAC,CAAC;MACzD;IACF;IACA,IAAIpB,GAAG,GAAG,MAAMoH,mBAAmB,CACjC,IAAI5P,OAAO,CAACvN,GAAG,EAAE,MAAMpC,iBAAiB,CAACuO,OAAO,CAAC,CACnD,CAAC;IACD,IAAI4J,GAAG,CAACxJ,MAAM,IAAI,GAAG,IAAI,CAACwJ,GAAG,CAAC3I,OAAO,CAACsE,GAAG,CAAC,kBAAkB,CAAC,EAAE;MAC7D,MAAM,IAAI/U,iBAAiB,CAACoZ,GAAG,CAACxJ,MAAM,EAAEwJ,GAAG,CAACtB,UAAU,EAAE,MAAMsB,GAAG,CAACwC,IAAI,CAAC,CAAC,CAAC;IAC3E;IACAha,SAAS,CAACwX,GAAG,CAAC5I,IAAI,EAAE,4BAA4B,CAAC;IACjD,IAAI;MACF,MAAMsP,OAAO,GAAG,MAAMM,wBAAwB,CAAChH,GAAG,CAAC5I,IAAI,EAAE;QACvDsQ,mBAAmB,EAAE,KAAK;MAC5B,CAAC,CAAC;MACF,IAAIhB,OAAO,CAACwB,IAAI,KAAK,UAAU,EAAE;QAC/B,OAAO;UACL1R,MAAM,EAAEwJ,GAAG,CAACxJ,MAAM;UAClBkD,IAAI,EAAE;YACJzQ,QAAQ,EAAE;cACRA,QAAQ,EAAEyd,OAAO,CAAC3a,QAAQ;cAC1B0N,MAAM,EAAEiN,OAAO,CAACjN,MAAM;cACtBtQ,OAAO,EAAEud,OAAO,CAACvd,OAAO;cACxB6U,UAAU,EAAE,KAAK;cACjBxH,MAAM,EAAEkQ,OAAO,CAAClQ;YAClB;UACF;QACF,CAAC;MACH;MACA,IAAIkQ,OAAO,CAACwB,IAAI,KAAK,QAAQ,EAAE;QAC7B,MAAM,IAAIja,KAAK,CAAC,yBAAyB,CAAC;MAC5C;MACAjE,OAAO,CAACsO,GAAG,CAACuS,qBAAqB,CAAC,CAAC7T,IAAI,CAAC,GAAG0P,OAAO,CAAC5b,OAAO,CAAC;MAC3D,IAAIuS,OAAO,GAAG;QAAE7S,MAAM,EAAE,CAAC;MAAE,CAAC;MAC5B,MAAM+gB,OAAO,GAAG7iB,gBAAgB,CAAC0N,OAAO,CAACe,MAAM,CAAC,GAAG,YAAY,GAAG,YAAY;MAC9E,KAAK,IAAI,CAACpM,OAAO,EAAEmF,KAAK,CAAC,IAAIsE,MAAM,CAACa,OAAO,CAACqR,OAAO,CAAC6E,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;QACnElO,OAAO,CAAC7S,MAAM,CAACO,OAAO,CAAC,GAAG;UAAE2O,IAAI,EAAExJ;QAAM,CAAC;MAC3C;MACA,IAAIwW,OAAO,CAACtR,MAAM,EAAE;QAClB,KAAK,IAAI,CAACrK,OAAO,EAAEsG,KAAK,CAAC,IAAImD,MAAM,CAACa,OAAO,CAACqR,OAAO,CAACtR,MAAM,CAAC,EAAE;UAC3DiI,OAAO,CAAC7S,MAAM,CAACO,OAAO,CAAC,GAAG;YAAEsG;UAAM,CAAC;QACrC;MACF;MACA,OAAO;QAAEmF,MAAM,EAAEwJ,GAAG,CAACxJ,MAAM;QAAEkD,IAAI,EAAE2D;MAAQ,CAAC;IAC9C,CAAC,CAAC,OAAO9E,CAAC,EAAE;MACV,MAAM,IAAItK,KAAK,CAAC,+BAA+B,CAAC;IAClD;EACF,CAAC;AACH;AACA,SAASud,iBAAiBA,CAAC;EACzBxE,wBAAwB;EACxBG,KAAK,EAAEC,mBAAmB,GAAGD,KAAK;EAClCT,OAAO;EACP9a,cAAc,GAAG,OAAO;EACxBgd;AACF,CAAC,EAAE;EACD,IAAIlC,OAAO,CAACwB,IAAI,KAAK,QAAQ,EAAE,MAAM,IAAIja,KAAK,CAAC,sBAAsB,CAAC;EACtE,IAAI;IAAE3C,MAAM;IAAEjB;EAAa,CAAC,GAAGga,MAAM,CAACoH,OAAO,CAC3C,MAAM9C,uBAAuB,CAAC;IAC5BjC,OAAO;IACPU,mBAAmB;IACnBwB,UAAU;IACV5B;EACF,CAAC,CAAC,EACF,CAACA,wBAAwB,EAAEN,OAAO,EAAEU,mBAAmB,EAAEwB,UAAU,CACrE,CAAC;EACDvE,MAAM,CAACqH,SAAS,CAAC,MAAM;IACrBtiB,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EACNib,MAAM,CAACsH,eAAe,CAAC,MAAM;IAC3B,MAAMtE,SAAS,GAAGC,MAAM;IACxB,IAAI,CAACD,SAAS,CAACsC,mBAAmB,EAAE;MAClCtC,SAAS,CAACsC,mBAAmB,GAAG,IAAI;MACpCtC,SAAS,CAACS,uBAAuB,CAAC8B,UAAU,CAAC,CAAC;IAChD;EACF,CAAC,EAAE,EAAE,CAAC;EACN,IAAI,CAACnF,SAAS,EAAEmH,WAAW,CAAC,GAAGvH,MAAM,CAACwH,QAAQ,CAACvgB,MAAM,CAACU,KAAK,CAACD,QAAQ,CAAC;EACrEsY,MAAM,CAACsH,eAAe,CACpB,MAAMrgB,MAAM,CAACwe,SAAS,CAAEgC,QAAQ,IAAK;IACnC,IAAIA,QAAQ,CAAC/f,QAAQ,KAAK0Y,SAAS,EAAE;MACnCmH,WAAW,CAACE,QAAQ,CAAC/f,QAAQ,CAAC;IAChC;EACF,CAAC,CAAC,EACF,CAACT,MAAM,EAAEmZ,SAAS,CACpB,CAAC;EACDJ,MAAM,CAACqH,SAAS,CAAC,MAAM;IACrB,IAAI9f,cAAc,KAAK,MAAM;IAAI;IACjC0b,MAAM,CAACyE,SAAS,EAAEC,UAAU,EAAEC,QAAQ,KAAK,IAAI,EAAE;MAC/C;IACF;IACA,SAASC,eAAeA,CAACC,EAAE,EAAE;MAC3B,IAAIhe,IAAI,GAAGge,EAAE,CAACC,OAAO,KAAK,MAAM,GAAGD,EAAE,CAACE,YAAY,CAAC,QAAQ,CAAC,GAAGF,EAAE,CAACE,YAAY,CAAC,MAAM,CAAC;MACtF,IAAI,CAACle,IAAI,EAAE;QACT;MACF;MACA,IAAI4H,QAAQ,GAAGoW,EAAE,CAACC,OAAO,KAAK,GAAG,GAAGD,EAAE,CAACpW,QAAQ,GAAG,IAAI5L,GAAG,CAACgE,IAAI,EAAEmZ,MAAM,CAACvb,QAAQ,CAACugB,MAAM,CAAC,CAACvW,QAAQ;MAChG,IAAI,CAACuT,eAAe,CAAC3N,GAAG,CAAC5F,QAAQ,CAAC,EAAE;QAClCwW,SAAS,CAAClL,GAAG,CAACtL,QAAQ,CAAC;MACzB;IACF;IACA,eAAeyW,YAAYA,CAAA,EAAG;MAC5BC,QAAQ,CAACC,gBAAgB,CAAC,uCAAuC,CAAC,CAAChU,OAAO,CAACwT,eAAe,CAAC;MAC3F,IAAItL,KAAK,GAAGgG,KAAK,CAAC+F,IAAI,CAACJ,SAAS,CAAC9X,IAAI,CAAC,CAAC,CAAC,CAAC8I,MAAM,CAAEpP,IAAI,IAAK;QACxD,IAAImb,eAAe,CAAC3N,GAAG,CAACxN,IAAI,CAAC,EAAE;UAC7Boe,SAAS,CAAC1V,MAAM,CAAC1I,IAAI,CAAC;UACtB,OAAO,KAAK;QACd;QACA,OAAO,IAAI;MACb,CAAC,CAAC;MACF,IAAIyS,KAAK,CAACjP,MAAM,KAAK,CAAC,EAAE;QACtB;MACF;MACA,IAAI;QACF,MAAM4X,4BAA4B,CAChC3I,KAAK,EACLoG,wBAAwB,EACxBI,mBACF,CAAC;MACH,CAAC,CAAC,OAAO7O,CAAC,EAAE;QACVZ,OAAO,CAACtG,KAAK,CAAC,kCAAkC,EAAEkH,CAAC,CAAC;MACtD;IACF;IACA,IAAIqU,qBAAqB,GAAGC,QAAQ,CAACL,YAAY,EAAE,GAAG,CAAC;IACvDA,YAAY,CAAC,CAAC;IACd,IAAIM,QAAQ,GAAG,IAAIC,gBAAgB,CAAC,MAAMH,qBAAqB,CAAC,CAAC,CAAC;IAClEE,QAAQ,CAACE,OAAO,CAACP,QAAQ,CAACQ,eAAe,EAAE;MACzCC,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE,IAAI;MACfC,UAAU,EAAE,IAAI;MAChBC,eAAe,EAAE,CAAC,eAAe,EAAE,MAAM,EAAE,QAAQ;IACrD,CAAC,CAAC;EACJ,CAAC,EAAE,CAACzhB,cAAc,EAAEob,wBAAwB,EAAEI,mBAAmB,CAAC,CAAC;EACnE,MAAMkG,gBAAgB,GAAG;IACvB7iB,MAAM,EAAE;MACN;MACA;MACA4C,aAAa,EAAE,KAAK;MACpBD,6BAA6B,EAAE;IACjC,CAAC;IACD1C,SAAS,EAAE,KAAK;IAChBiB,GAAG,EAAE,IAAI;IACTrB,WAAW,EAAE,EAAE;IACfF,QAAQ,EAAE;MACRI,MAAM,EAAE,CAAC,CAAC;MACViD,OAAO,EAAE,GAAG;MACZxD,GAAG,EAAE,EAAE;MACPqD,KAAK,EAAE;QACLE,MAAM,EAAE,EAAE;QACVD,OAAO,EAAE;MACX;IACF,CAAC;IACD3B,cAAc,EAAE;MAAE8B,IAAI,EAAE,MAAM;MAAEC,YAAY,EAAE;IAAc,CAAC;IAC7DtD;EACF,CAAC;EACD,OAAO,eAAgBga,MAAM,CAAC9Y,aAAa,CAACvE,gBAAgB,CAACyE,QAAQ,EAAE;IAAEC,KAAK,EAAE;EAAK,CAAC,EAAE,eAAgB2Y,MAAM,CAAC9Y,aAAa,CAACuZ,4BAA4B,EAAE;IAAE/Y,QAAQ,EAAE0Y;EAAU,CAAC,EAAE,eAAgBJ,MAAM,CAAC9Y,aAAa,CAAC1E,gBAAgB,CAAC4E,QAAQ,EAAE;IAAEC,KAAK,EAAE4hB;EAAiB,CAAC,EAAE,eAAgBjJ,MAAM,CAAC9Y,aAAa,CAAC/D,wCAAwC,EAAE;IAAE8D,MAAM;IAAEiiB,SAAS,EAAEjJ,QAAQ,CAACiJ;EAAU,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/Y;AACA,SAAShF,6BAA6BA,CAAC1d,KAAK,EAAE6b,OAAO,EAAE;EACrD,IAAI8G,cAAc,GAAG9G,OAAO,IAAI7b,KAAK,CAACI,EAAE,IAAIyb,OAAO,CAAC9b,UAAU;EAC9D,IAAI+X,WAAW,GAAG+D,OAAO,EAAE9b,UAAU,CAACC,KAAK,CAACI,EAAE,CAAC;EAC/C,IAAIwiB,eAAe,GAAG/G,OAAO,EAAEtR,MAAM,IAAIvK,KAAK,CAACI,EAAE,IAAIyb,OAAO,CAACtR,MAAM;EACnE,IAAIsY,YAAY,GAAGhH,OAAO,EAAEtR,MAAM,GAAGvK,KAAK,CAACI,EAAE,CAAC;EAC9C,IAAI0iB,kBAAkB,GAAG9iB,KAAK,CAACM,YAAY,EAAEc,OAAO,KAAK,IAAI,IAAI,CAACpB,KAAK,CAACO,SAAS;EAAI;EACrF;EACA;EACAP,KAAK,CAAC0f,YAAY,IAAI,CAAC1f,KAAK,CAAC4f,OAAO;EACpCjiB,SAAS,CAAC8e,MAAM,CAACuB,yBAAyB,CAAC;EAC3ClC,uBAAuB,CAACW,MAAM,CAACuB,yBAAyB,EAAEhe,KAAK,CAAC;EAChE,IAAI+iB,SAAS,GAAG;IACd3iB,EAAE,EAAEJ,KAAK,CAACI,EAAE;IACZwf,OAAO,EAAE5f,KAAK,CAAC4f,OAAO;IACtBC,YAAY,EAAE7f,KAAK,CAAC6f,YAAY;IAChC9b,MAAM,EAAE/D,KAAK,CAAC+D,MAAM;IACpBO,gBAAgB,EAAEtE,KAAK,CAACsE,gBAAgB;IACxCia,sBAAsB,EAAEve,KAAK,CAACue,sBAAsB;IACpDhb,KAAK,EAAEvD,KAAK,CAACuD,KAAK;IAClBK,MAAM,EAAE5D,KAAK,CAACM,YAAY,GAAG,OAAOqD,IAAI,EAAEqf,WAAW,KAAK;MACxD,IAAI;QACF,IAAI/Z,MAAM,GAAG,MAAMjJ,KAAK,CAACM,YAAY,CAAC;UACpC,GAAGqD,IAAI;UACPsf,YAAY,EAAEA,CAAA,KAAM;YAClBC,+BAA+B,CAC7B,QAAQ,EACRljB,KAAK,CAACI,EAAE,EACRJ,KAAK,CAACO,SACR,CAAC;YACD,IAAIuiB,kBAAkB,EAAE;cACtB,IAAIH,cAAc,EAAE;gBAClB,OAAO7K,WAAW;cACpB;cACA,IAAI8K,eAAe,EAAE;gBACnB,MAAMC,YAAY;cACpB;YACF;YACA,OAAOM,eAAe,CAACH,WAAW,CAAC;UACrC;QACF,CAAC,CAAC;QACF,OAAO/Z,MAAM;MACf,CAAC,SAAS;QACR6Z,kBAAkB,GAAG,KAAK;MAC5B;IACF,CAAC;IACC;IACA;IACA,CAACzM,CAAC,EAAE2M,WAAW,KAAKG,eAAe,CAACH,WAAW,CAChD;IACDtf,MAAM,EAAE1D,KAAK,CAAC2f,YAAY,GAAG,CAAChc,IAAI,EAAEqf,WAAW,KAAKhjB,KAAK,CAAC2f,YAAY,CAAC;MACrE,GAAGhc,IAAI;MACPyf,YAAY,EAAE,MAAAA,CAAA,KAAY;QACxBF,+BAA+B,CAC7B,QAAQ,EACRljB,KAAK,CAACI,EAAE,EACRJ,KAAK,CAACO,SACR,CAAC;QACD,OAAO,MAAM4iB,eAAe,CAACH,WAAW,CAAC;MAC3C;IACF,CAAC,CAAC,GAAGhjB,KAAK,CAACkE,SAAS,GAAG,CAACmS,CAAC,EAAE2M,WAAW,KAAKG,eAAe,CAACH,WAAW,CAAC,GAAG,MAAM;MAC9E,MAAM7kB,oBAAoB,CAAC,QAAQ,EAAE6B,KAAK,CAACI,EAAE,CAAC;IAChD,CAAC;IACDkD,IAAI,EAAEtD,KAAK,CAACsD,IAAI;IAChBU,gBAAgB,EAAEhE,KAAK,CAACgE,gBAAgB;IACxC;IACA;IACAzD,SAAS,EAAE,IAAI;IACf6D,eAAe,EAAEpE,KAAK,CAACM,YAAY,IAAI,IAAI;IAC3C4D,SAAS,EAAElE,KAAK,CAACkE,SAAS;IAC1BC,eAAe,EAAEnE,KAAK,CAAC2f,YAAY,IAAI,IAAI;IAC3CQ,mBAAmB,EAAEngB,KAAK,CAACgE,gBAAgB,IAAI;EACjD,CAAC;EACD,IAAI,OAAO+e,SAAS,CAACnf,MAAM,KAAK,UAAU,EAAE;IAC1Cmf,SAAS,CAACnf,MAAM,CAACxC,OAAO,GAAG5C,wBAAwB,CACjDwB,KAAK,CAACI,EAAE,EACRJ,KAAK,CAACM,YAAY,EAClBN,KAAK,CAACO,SAAS,EACf,KACF,CAAC;EACH;EACA,OAAOwiB,SAAS;AAClB;AACA,SAASI,eAAeA,CAACH,WAAW,EAAE;EACpCrlB,SAAS,CAAC,OAAOqlB,WAAW,KAAK,UAAU,EAAE,+BAA+B,CAAC;EAC7E,OAAOA,WAAW,CAAC,CAAC;AACtB;AACA,SAASE,+BAA+BA,CAAC7F,IAAI,EAAEnd,OAAO,EAAEmjB,UAAU,EAAE;EAClE,IAAI,CAACA,UAAU,EAAE;IACf,IAAIC,EAAE,GAAGjG,IAAI,KAAK,QAAQ,GAAG,gBAAgB,GAAG,gBAAgB;IAChE,IAAIkG,GAAG,GAAG,0BAA0BD,EAAE,2CAA2CjG,IAAI,eAAend,OAAO,IAAI;IAC/G4M,OAAO,CAACtG,KAAK,CAAC+c,GAAG,CAAC;IAClB,MAAM,IAAIxnB,iBAAiB,CAAC,GAAG,EAAE,aAAa,EAAE,IAAIqH,KAAK,CAACmgB,GAAG,CAAC,EAAE,IAAI,CAAC;EACvE;AACF;AACA,IAAI7B,SAAS,GAAG,eAAgB,IAAI9Q,GAAG,CAAC,CAAC;AACzC,IAAI4S,sBAAsB,GAAG,GAAG;AAChC,IAAI/E,eAAe,GAAG,eAAgB,IAAI7N,GAAG,CAAC,CAAC;AAC/C,IAAI6S,SAAS,GAAG,IAAI;AACpB,SAASC,cAAcA,CAAC3N,KAAK,EAAE;EAC7B,IAAIA,KAAK,CAACjP,MAAM,KAAK,CAAC,EAAE;IACtB,OAAO,IAAI;EACb;EACA,IAAIiP,KAAK,CAACjP,MAAM,KAAK,CAAC,EAAE;IACtB,OAAO,IAAIxH,GAAG,CAAC,GAAGyW,KAAK,CAAC,CAAC,CAAC,WAAW,EAAE0G,MAAM,CAACvb,QAAQ,CAACugB,MAAM,CAAC;EAChE;EACA,MAAMjF,SAAS,GAAGC,MAAM;EACxB,IAAItR,QAAQ,GAAG,CAACqR,SAAS,CAACS,uBAAuB,CAAC9R,QAAQ,IAAI,EAAE,EAAE7M,OAAO,CACvE,UAAU,EACV,EACF,CAAC;EACD,IAAIc,GAAG,GAAG,IAAIE,GAAG,CAAC,GAAG6L,QAAQ,YAAY,EAAEsR,MAAM,CAACvb,QAAQ,CAACugB,MAAM,CAAC;EAClEriB,GAAG,CAAC0M,YAAY,CAAC8G,GAAG,CAAC,OAAO,EAAEmD,KAAK,CAAC4N,IAAI,CAAC,CAAC,CAACpN,IAAI,CAAC,GAAG,CAAC,CAAC;EACrD,OAAOnX,GAAG;AACZ;AACA,eAAesf,4BAA4BA,CAAC3I,KAAK,EAAEoG,wBAAwB,EAAEI,mBAAmB,EAAE9P,MAAM,EAAE;EACxG,IAAIrN,GAAG,GAAGskB,cAAc,CAAC3N,KAAK,CAAC;EAC/B,IAAI3W,GAAG,IAAI,IAAI,EAAE;IACf;EACF;EACA,IAAIA,GAAG,CAAC4J,QAAQ,CAAC,CAAC,CAAClC,MAAM,GAAG2c,SAAS,EAAE;IACrC/B,SAAS,CAACkC,KAAK,CAAC,CAAC;IACjB;EACF;EACA,IAAIvO,QAAQ,GAAG,MAAMkH,mBAAmB,CAAC,IAAI5P,OAAO,CAACvN,GAAG,EAAE;IAAEqN;EAAO,CAAC,CAAC,CAAC;EACtE,IAAI,CAAC4I,QAAQ,CAAC9I,IAAI,IAAI8I,QAAQ,CAAC1J,MAAM,GAAG,GAAG,IAAI0J,QAAQ,CAAC1J,MAAM,IAAI,GAAG,EAAE;IACrE,MAAM,IAAIvI,KAAK,CAAC,mDAAmD,CAAC;EACtE;EACA,IAAIyY,OAAO,GAAG,MAAMM,wBAAwB,CAAC9G,QAAQ,CAAC9I,IAAI,EAAE;IAC1DsQ,mBAAmB,EAAE,KAAK;EAC5B,CAAC,CAAC;EACF,IAAIhB,OAAO,CAACwB,IAAI,KAAK,UAAU,EAAE;IAC/B,MAAM,IAAIja,KAAK,CAAC,wBAAwB,CAAC;EAC3C;EACA2S,KAAK,CAAClI,OAAO,CAAEgW,CAAC,IAAKC,cAAc,CAACD,CAAC,EAAEpF,eAAe,CAAC,CAAC;EACxD5C,OAAO,CAAC/F,OAAO,CAACjI,OAAO,CAAEgW,CAAC,IAAK;IAC7BpH,MAAM,CAACQ,uBAAuB,CAACQ,WAAW,CACxCoG,CAAC,CAAC3gB,QAAQ,IAAI,IAAI,EAClB,CAACwa,6BAA6B,CAACmG,CAAC,CAAC,CACnC,CAAC;EACH,CAAC,CAAC;AACJ;AACA,SAASC,cAAcA,CAACxgB,IAAI,EAAEygB,KAAK,EAAE;EACnC,IAAIA,KAAK,CAACC,IAAI,IAAIR,sBAAsB,EAAE;IACxC,IAAIS,KAAK,GAAGF,KAAK,CAACnW,MAAM,CAAC,CAAC,CAACsW,IAAI,CAAC,CAAC,CAACrjB,KAAK;IACvCkjB,KAAK,CAAC/X,MAAM,CAACiY,KAAK,CAAC;EACrB;EACAF,KAAK,CAACvN,GAAG,CAAClT,IAAI,CAAC;AACjB;AACA,SAAS0e,QAAQA,CAACmC,QAAQ,EAAEC,IAAI,EAAE;EAChC,IAAI9Q,SAAS;EACb,OAAO,CAAC,GAAG3P,IAAI,KAAK;IAClB8Y,MAAM,CAAC/I,YAAY,CAACJ,SAAS,CAAC;IAC9BA,SAAS,GAAGmJ,MAAM,CAAClJ,UAAU,CAAC,MAAM4Q,QAAQ,CAAC,GAAGxgB,IAAI,CAAC,EAAEygB,IAAI,CAAC;EAC9D,CAAC;AACH;;AAEA;AACA,OAAO,KAAKC,MAAM,MAAM,OAAO;;AAE/B;AACA,IAAIC,QAAQ,GAAG,IAAIpf,WAAW,CAAC,CAAC;AAChC,IAAIqf,OAAO,GAAG,gBAAgB;AAC9B,SAASC,gBAAgBA,CAACC,SAAS,EAAE;EACnC,IAAIC,OAAO,GAAG,IAAI/iB,WAAW,CAAC,CAAC;EAC/B,IAAIgjB,wBAAwB;EAC5B,IAAIC,iBAAiB,GAAG,IAAIzH,OAAO,CAChCC,OAAO,IAAKuH,wBAAwB,GAAGvH,OAC1C,CAAC;EACD,IAAIyH,UAAU,GAAG,KAAK;EACtB,IAAIC,QAAQ,GAAG,EAAE;EACjB,IAAIC,OAAO,GAAG,IAAI;EAClB,SAASC,mBAAmBA,CAACxW,UAAU,EAAE;IACvC,KAAK,IAAIyW,KAAK,IAAIH,QAAQ,EAAE;MAC1B,IAAII,GAAG,GAAGR,OAAO,CAACS,MAAM,CAACF,KAAK,EAAE;QAAE5W,MAAM,EAAE;MAAK,CAAC,CAAC;MACjD,IAAI6W,GAAG,CAACrQ,QAAQ,CAAC0P,OAAO,CAAC,EAAE;QACzBW,GAAG,GAAGA,GAAG,CAAC/e,KAAK,CAAC,CAAC,EAAE,CAACoe,OAAO,CAACzd,MAAM,CAAC;MACrC;MACA0H,UAAU,CAACC,OAAO,CAAC6V,QAAQ,CAAChnB,MAAM,CAAC4nB,GAAG,CAAC,CAAC;IAC1C;IACAJ,QAAQ,CAAChe,MAAM,GAAG,CAAC;IACnBie,OAAO,GAAG,IAAI;EAChB;EACA,OAAO,IAAIK,eAAe,CAAC;IACzBC,SAASA,CAACJ,KAAK,EAAEzW,UAAU,EAAE;MAC3BsW,QAAQ,CAAC3Y,IAAI,CAAC8Y,KAAK,CAAC;MACpB,IAAIF,OAAO,EAAE;QACX;MACF;MACAA,OAAO,GAAGxR,UAAU,CAAC,YAAY;QAC/ByR,mBAAmB,CAACxW,UAAU,CAAC;QAC/B,IAAI,CAACqW,UAAU,EAAE;UACfA,UAAU,GAAG,IAAI;UACjBS,cAAc,CAACb,SAAS,EAAEjW,UAAU,CAAC,CAACoP,KAAK,CAAE3L,GAAG,IAAKzD,UAAU,CAAChI,KAAK,CAACyL,GAAG,CAAC,CAAC,CAAC+K,IAAI,CAAC2H,wBAAwB,CAAC;QAC5G;MACF,CAAC,EAAE,CAAC,CAAC;IACP,CAAC;IACD,MAAMY,KAAKA,CAAC/W,UAAU,EAAE;MACtB,MAAMoW,iBAAiB;MACvB,IAAIG,OAAO,EAAE;QACXrR,YAAY,CAACqR,OAAO,CAAC;QACrBC,mBAAmB,CAACxW,UAAU,CAAC;MACjC;MACAA,UAAU,CAACC,OAAO,CAAC6V,QAAQ,CAAChnB,MAAM,CAAC,gBAAgB,CAAC,CAAC;IACvD;EACF,CAAC,CAAC;AACJ;AACA,eAAegoB,cAAcA,CAACb,SAAS,EAAEjW,UAAU,EAAE;EACnD,IAAIkW,OAAO,GAAG,IAAI/iB,WAAW,CAAC,OAAO,EAAE;IAAE6jB,KAAK,EAAE;EAAK,CAAC,CAAC;EACvD,MAAMhkB,MAAM,GAAGijB,SAAS,CAAChjB,SAAS,CAAC,CAAC;EACpC,IAAI;IACF,IAAIgkB,IAAI;IACR,OAAO,CAACA,IAAI,GAAG,MAAMjkB,MAAM,CAACikB,IAAI,CAAC,CAAC,KAAK,CAACA,IAAI,CAACC,IAAI,EAAE;MACjD,MAAMT,KAAK,GAAGQ,IAAI,CAAC5kB,KAAK;MACxB,IAAI;QACF8kB,UAAU,CACRhd,IAAI,CAACC,SAAS,CAAC8b,OAAO,CAACS,MAAM,CAACF,KAAK,EAAE;UAAE5W,MAAM,EAAE;QAAK,CAAC,CAAC,CAAC,EACvDG,UACF,CAAC;MACH,CAAC,CAAC,OAAOyD,GAAG,EAAE;QACZ,IAAI2T,MAAM,GAAGjd,IAAI,CAACC,SAAS,CAAChD,IAAI,CAACC,MAAM,CAACggB,aAAa,CAAC,GAAGZ,KAAK,CAAC,CAAC,CAAC;QACjEU,UAAU,CACR,wBAAwBC,MAAM,2BAA2B,EACzDpX,UACF,CAAC;MACH;IACF;EACF,CAAC,SAAS;IACRhN,MAAM,CAACskB,WAAW,CAAC,CAAC;EACtB;EACA,IAAIC,SAAS,GAAGrB,OAAO,CAACS,MAAM,CAAC,CAAC;EAChC,IAAIY,SAAS,CAACjf,MAAM,EAAE;IACpB6e,UAAU,CAAChd,IAAI,CAACC,SAAS,CAACmd,SAAS,CAAC,EAAEvX,UAAU,CAAC;EACnD;AACF;AACA,SAASmX,UAAUA,CAACV,KAAK,EAAEzW,UAAU,EAAE;EACrCA,UAAU,CAACC,OAAO,CAChB6V,QAAQ,CAAChnB,MAAM,CACb,WAAW0oB,YAAY,CACrB,kCAAkCf,KAAK,GACzC,CAAC,WACH,CACF,CAAC;AACH;AACA,SAASe,YAAYA,CAACC,MAAM,EAAE;EAC5B,OAAOA,MAAM,CAAC3nB,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAACA,OAAO,CAAC,eAAe,EAAE,QAAQ,CAAC;AAC7E;;AAEA;AACA,IAAI4nB,SAAS,GAAG,KAAK;AACrB,IAAIC,OAAO,GAAG9B,MAAM,CAAC6B,SAAS,CAAC;AAC/B,SAASE,OAAOA,CAACC,OAAO,EAAE;EACxB,IAAIF,OAAO,EAAE;IACX,OAAOA,OAAO,CAACE,OAAO,CAAC;EACzB;EACA,MAAM,IAAIjjB,KAAK,CAAC,sDAAsD,CAAC;AACzE;AACA,eAAekjB,qBAAqBA,CAAC;EACnC/a,OAAO;EACPgb,WAAW;EACXpK,wBAAwB;EACxBqK,UAAU;EACVplB,OAAO,GAAG;AACZ,CAAC,EAAE;EACD,MAAMhC,GAAG,GAAG,IAAIE,GAAG,CAACiM,OAAO,CAACnM,GAAG,CAAC;EAChC,MAAMqnB,aAAa,GAAGC,oBAAoB,CAACtnB,GAAG,CAAC;EAC/C,MAAMunB,qBAAqB,GAAGF,aAAa,IAAIG,iBAAiB,CAACxnB,GAAG,CAAC,IAAImM,OAAO,CAACiB,OAAO,CAACsE,GAAG,CAAC,eAAe,CAAC;EAC7G,MAAM+V,cAAc,GAAG,MAAMN,WAAW,CAAChb,OAAO,CAAC;EACjD,IAAIob,qBAAqB,IAAIE,cAAc,CAACra,OAAO,CAACiB,GAAG,CAAC,uBAAuB,CAAC,KAAK,MAAM,EAAE;IAC3F,OAAOoZ,cAAc;EACvB;EACA,IAAI,CAACA,cAAc,CAACta,IAAI,EAAE;IACxB,MAAM,IAAInJ,KAAK,CAAC,iCAAiC,CAAC;EACpD;EACA,MAAM0jB,sBAAsB,GAAGD,cAAc,CAACE,KAAK,CAAC,CAAC;EACrD,IAAIC,eAAe,GAAG,IAAI;EAC1B,IAAI5lB,OAAO,EAAE;IACX4lB,eAAe,GAAGH,cAAc,CAACE,KAAK,CAAC,CAAC;EAC1C;EACA,MAAMxa,IAAI,GAAGsa,cAAc,CAACta,IAAI;EAChC,IAAI0a,MAAM;EACV,IAAIC,iBAAiB,GAAG,EAAE;EAC1B,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAACF,MAAM,EAAE;MACXA,MAAM,GAAG,EAAE;MACX,OAAO1a,IAAI,CAAC6a,WAAW,CACrB,IAAIhC,eAAe,CAAC;QAClBC,SAASA,CAACJ,KAAK,EAAEzW,UAAU,EAAE;UAC3ByY,MAAM,CAAC9a,IAAI,CAAC8Y,KAAK,CAAC;UAClBzW,UAAU,CAACC,OAAO,CAACwW,KAAK,CAAC;UACzBiC,iBAAiB,CAACrZ,OAAO,CAAEwZ,CAAC,IAAKA,CAAC,CAAC5Y,OAAO,CAACwW,KAAK,CAAC,CAAC;QACpD,CAAC;QACDM,KAAKA,CAAA,EAAG;UACN2B,iBAAiB,CAACrZ,OAAO,CAAEwZ,CAAC,IAAKA,CAAC,CAAC3Y,KAAK,CAAC,CAAC,CAAC;UAC3CwY,iBAAiB,GAAG,EAAE;QACxB;MACF,CAAC,CACH,CAAC;IACH;IACA,OAAO,IAAI5Y,cAAc,CAAC;MACxBC,KAAKA,CAACC,UAAU,EAAE;QAChByY,MAAM,CAACpZ,OAAO,CAAEoX,KAAK,IAAKzW,UAAU,CAACC,OAAO,CAACwW,KAAK,CAAC,CAAC;QACpDiC,iBAAiB,CAAC/a,IAAI,CAACqC,UAAU,CAAC;MACpC;IACF,CAAC,CAAC;EACJ,CAAC;EACD,IAAI8Y,yBAAyB,GAAG,IAAI;EACpC,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAMzK,cAAc,GAAGK,OAAO,CAACC,OAAO,CACpCjB,wBAAwB,CAACgL,YAAY,CAAC,CAAC,CACzC,CAAC;IACD,OAAOxd,MAAM,CAAC6d,gBAAgB,CAAC1K,cAAc,EAAE;MAC7C2K,0BAA0B,EAAE;QAC1Bha,GAAGA,CAAA,EAAG;UACJ,OAAO6Z,yBAAyB;QAClC,CAAC;QACD1U,GAAGA,CAAC8U,UAAU,EAAE;UACdJ,yBAAyB,GAAGI,UAAU;QACxC;MACF,CAAC;MACDC,SAAS,EAAE;QACTla,GAAGA,CAAA,EAAG;UACJ,OAAOqP,cAAc,CAACE,IAAI,CACvBnB,OAAO,IAAKA,OAAO,CAACwB,IAAI,KAAK,QAAQ,GAAGxB,OAAO,CAAC8L,SAAS,GAAG,KAAK,CACpE,CAAC;QACH;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EACD,IAAI;IACF,IAAI,CAACb,sBAAsB,CAACva,IAAI,EAAE;MAChC,MAAM,IAAInJ,KAAK,CAAC,iCAAiC,CAAC;IACpD;IACA,MAAMyY,OAAO,GAAG,MAAMM,wBAAwB,CAC5C2K,sBAAsB,CAACva,IACzB,CAAC;IACD,IAAIsa,cAAc,CAAClb,MAAM,KAAKpP,4BAA4B,IAAIsf,OAAO,CAACwB,IAAI,KAAK,UAAU,EAAE;MACzF,MAAM9M,QAAQ,GAAG,IAAIN,OAAO,CAAC4W,cAAc,CAACra,OAAO,CAAC;MACpD+D,QAAQ,CAACvE,MAAM,CAAC,kBAAkB,CAAC;MACnCuE,QAAQ,CAACvE,MAAM,CAAC,gBAAgB,CAAC;MACjCuE,QAAQ,CAACvE,MAAM,CAAC,cAAc,CAAC;MAC/BuE,QAAQ,CAACvE,MAAM,CAAC,kBAAkB,CAAC;MACnCuE,QAAQ,CAACqC,GAAG,CAAC,UAAU,EAAEiJ,OAAO,CAAC3a,QAAQ,CAAC;MAC1C,OAAO,IAAI0K,QAAQ,CAACob,eAAe,EAAEza,IAAI,IAAI,EAAE,EAAE;QAC/CC,OAAO,EAAE+D,QAAQ;QACjB5E,MAAM,EAAEkQ,OAAO,CAAClQ,MAAM;QACtBkI,UAAU,EAAEgT,cAAc,CAAChT;MAC7B,CAAC,CAAC;IACJ;IACA,MAAM+T,IAAI,GAAG,MAAMpB,UAAU,CAACe,UAAU,CAAC;IACzC,MAAM/a,OAAO,GAAG,IAAIyD,OAAO,CAAC4W,cAAc,CAACra,OAAO,CAAC;IACnDA,OAAO,CAACoG,GAAG,CAAC,cAAc,EAAE,0BAA0B,CAAC;IACvD,IAAI,CAACxR,OAAO,EAAE;MACZ,OAAO,IAAIwK,QAAQ,CAACgc,IAAI,EAAE;QACxBjc,MAAM,EAAEkb,cAAc,CAAClb,MAAM;QAC7Ba;MACF,CAAC,CAAC;IACJ;IACA,IAAI,CAACwa,eAAe,EAAEza,IAAI,EAAE;MAC1B,MAAM,IAAInJ,KAAK,CAAC,iCAAiC,CAAC;IACpD;IACA,MAAMykB,KAAK,GAAGD,IAAI,CAACR,WAAW,CAAC5C,gBAAgB,CAACwC,eAAe,CAACza,IAAI,CAAC,CAAC;IACtE,OAAO,IAAIX,QAAQ,CAACic,KAAK,EAAE;MACzBlc,MAAM,EAAEkb,cAAc,CAAClb,MAAM;MAC7Ba;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOsb,MAAM,EAAE;IACf,IAAIA,MAAM,YAAYlc,QAAQ,EAAE;MAC9B,OAAOkc,MAAM;IACf;IACA,IAAI;MACF,MAAMnc,MAAM,GAAG1N,oBAAoB,CAAC6pB,MAAM,CAAC,GAAGA,MAAM,CAACnc,MAAM,GAAG,GAAG;MACjE,MAAMic,IAAI,GAAG,MAAMpB,UAAU,CAAC,MAAM;QAClC,MAAMze,OAAO,GAAGoV,OAAO,CAACC,OAAO,CAC7BjB,wBAAwB,CAACgL,YAAY,CAAC,CAAC,CACzC,CAAC;QACD,MAAMrK,cAAc,GAAG/U,OAAO,CAACiV,IAAI,CAChCnB,OAAO,IAAKlS,MAAM,CAACe,MAAM,CAACmR,OAAO,EAAE;UAClClQ,MAAM;UACNpB,MAAM,EAAE+c,yBAAyB,GAAG;YAClC,CAACA,yBAAyB,GAAGQ;UAC/B,CAAC,GAAG,CAAC;QACP,CAAC,CACH,CAAC;QACD,OAAOne,MAAM,CAAC6d,gBAAgB,CAAC1K,cAAc,EAAE;UAC7C2K,0BAA0B,EAAE;YAC1Bha,GAAGA,CAAA,EAAG;cACJ,OAAO6Z,yBAAyB;YAClC,CAAC;YACD1U,GAAGA,CAAC8U,UAAU,EAAE;cACdJ,yBAAyB,GAAGI,UAAU;YACxC;UACF,CAAC;UACDC,SAAS,EAAE;YACTla,GAAGA,CAAA,EAAG;cACJ,OAAOqP,cAAc,CAACE,IAAI,CACvBnB,OAAO,IAAKA,OAAO,CAACwB,IAAI,KAAK,QAAQ,GAAGxB,OAAO,CAAC8L,SAAS,GAAG,KAAK,CACpE,CAAC;YACH;UACF;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,MAAMnb,OAAO,GAAG,IAAIyD,OAAO,CAAC4W,cAAc,CAACra,OAAO,CAAC;MACnDA,OAAO,CAACoG,GAAG,CAAC,cAAc,EAAE,WAAW,CAAC;MACxC,IAAI,CAACxR,OAAO,EAAE;QACZ,OAAO,IAAIwK,QAAQ,CAACgc,IAAI,EAAE;UACxBjc,MAAM;UACNa;QACF,CAAC,CAAC;MACJ;MACA,IAAI,CAACwa,eAAe,EAAEza,IAAI,EAAE;QAC1B,MAAM,IAAInJ,KAAK,CAAC,iCAAiC,CAAC;MACpD;MACA,MAAMykB,KAAK,GAAGD,IAAI,CAACR,WAAW,CAAC5C,gBAAgB,CAACwC,eAAe,CAACza,IAAI,CAAC,CAAC;MACtE,OAAO,IAAIX,QAAQ,CAACic,KAAK,EAAE;QACzBlc,MAAM;QACNa;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,MAAM,CACR;IACA,MAAMsb,MAAM;EACd;AACF;AACA,SAASC,eAAeA,CAAC;EAAER;AAAW,CAAC,EAAE;EACvC,MAAMxf,OAAO,GAAGwf,UAAU,CAAC,CAAC;EAC5B,MAAM1L,OAAO,GAAGuK,OAAO,CAACre,OAAO,CAAC;EAChC,IAAI8T,OAAO,CAACwB,IAAI,KAAK,UAAU,EAAE;IAC/B,MAAM,IAAIzR,QAAQ,CAAC,IAAI,EAAE;MACvBD,MAAM,EAAEkQ,OAAO,CAAClQ,MAAM;MACtBa,OAAO,EAAE;QACPwb,QAAQ,EAAEnM,OAAO,CAAC3a;MACpB;IACF,CAAC,CAAC;EACJ;EACA,IAAI2a,OAAO,CAACwB,IAAI,KAAK,QAAQ,EAAE,OAAO,IAAI;EAC1C,IAAI4K,iBAAiB,GAAG;IAAE,GAAGpM,OAAO,CAAC9b;EAAW,CAAC;EACjD,KAAK,MAAMC,KAAK,IAAI6b,OAAO,CAAC5b,OAAO,EAAE;IACnC,IAAIzB,wBAAwB,CAC1BwB,KAAK,CAACI,EAAE,EACRJ,KAAK,CAACM,YAAY,EAClBN,KAAK,CAACO,SAAS,EACf,KACF,CAAC,KAAKP,KAAK,CAACue,sBAAsB,IAAI,CAACve,KAAK,CAACO,SAAS,CAAC,EAAE;MACvD,OAAO0nB,iBAAiB,CAACjoB,KAAK,CAACI,EAAE,CAAC;IACpC;EACF;EACA,MAAMjB,OAAO,GAAG;IACd,IAAIsoB,0BAA0BA,CAAA,EAAG;MAC/B,OAAO1f,OAAO,CAAC0f,0BAA0B,IAAI,IAAI;IACnD,CAAC;IACD,IAAIA,0BAA0BA,CAACC,UAAU,EAAE;MACzC3f,OAAO,CAAC0f,0BAA0B,GAAGC,UAAU;IACjD,CAAC;IACD9X,UAAU,EAAEiM,OAAO,CAACjM,UAAU;IAC9BD,aAAa,EAAE,CAAC,CAAC;IACjBxE,QAAQ,EAAE0Q,OAAO,CAAC1Q,QAAQ;IAC1BZ,MAAM,EAAEsR,OAAO,CAACtR,MAAM;IACtBxK,UAAU,EAAEkoB,iBAAiB;IAC7BpY,aAAa,EAAE,CAAC,CAAC;IACjB3O,QAAQ,EAAE2a,OAAO,CAAC3a,QAAQ;IAC1B8Q,UAAU,EAAE,GAAG;IACf/R,OAAO,EAAE4b,OAAO,CAAC5b,OAAO,CAACkD,GAAG,CAAEnD,KAAK,KAAM;MACvCoL,MAAM,EAAEpL,KAAK,CAACoL,MAAM;MACpBF,QAAQ,EAAElL,KAAK,CAACkL,QAAQ;MACxBgd,YAAY,EAAEloB,KAAK,CAACkoB,YAAY;MAChC/nB,KAAK,EAAE;QACLC,EAAE,EAAEJ,KAAK,CAACI,EAAE;QACZsD,MAAM,EAAE1D,KAAK,CAACkE,SAAS,IAAI,CAAC,CAAClE,KAAK,CAAC2f,YAAY;QAC/C5b,MAAM,EAAE/D,KAAK,CAAC+D,MAAM;QACpBO,gBAAgB,EAAEtE,KAAK,CAACsE,gBAAgB;QACxCV,MAAM,EAAE5D,KAAK,CAACO,SAAS,IAAI,CAAC,CAACP,KAAK,CAACM,YAAY;QAC/CiD,KAAK,EAAEvD,KAAK,CAACuD,KAAK;QAClBD,IAAI,EAAEtD,KAAK,CAACsD,IAAI;QAChBU,gBAAgB,EAAEhE,KAAK,CAACgE;MAC1B;IACF,CAAC,CAAC;EACJ,CAAC;EACD,MAAMvD,MAAM,GAAGrD,kBAAkB,CAC/Bye,OAAO,CAAC5b,OAAO,CAACie,WAAW,CAAC,CAACC,QAAQ,EAAEne,KAAK,KAAK;IAC/C,MAAMG,KAAK,GAAG;MACZC,EAAE,EAAEJ,KAAK,CAACI,EAAE;MACZsD,MAAM,EAAE1D,KAAK,CAACkE,SAAS,IAAI,CAAC,CAAClE,KAAK,CAAC2f,YAAY;MAC/CC,OAAO,EAAE5f,KAAK,CAAC4f,OAAO;MACtBC,YAAY,EAAE7f,KAAK,CAAC6f,YAAY;MAChC9b,MAAM,EAAE/D,KAAK,CAAC+D,MAAM;MACpBO,gBAAgB,EAAE,CAAC,CAACtE,KAAK,CAAC6f,YAAY;MACtCtB,sBAAsB,EAAEve,KAAK,CAACue,sBAAsB;MACpDhb,KAAK,EAAEvD,KAAK,CAACuD,KAAK;MAClBK,MAAM,EAAE5D,KAAK,CAACO,SAAS,IAAI,CAAC,CAACP,KAAK,CAACM,YAAY;MAC/CgD,IAAI,EAAEtD,KAAK,CAACsD,IAAI;MAChBU,gBAAgB,EAAEhE,KAAK,CAACgE;IAC1B,CAAC;IACD,IAAIma,QAAQ,CAACrX,MAAM,GAAG,CAAC,EAAE;MACvB3G,KAAK,CAAC2E,QAAQ,GAAGqZ,QAAQ;IAC3B;IACA,OAAO,CAAChe,KAAK,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC,EACNhB,OACF,CAAC;EACD,MAAMsjB,gBAAgB,GAAG;IACvB7iB,MAAM,EAAE;MACN;MACA;MACA4C,aAAa,EAAE,KAAK;MACpBD,6BAA6B,EAAE;IACjC,CAAC;IACD1C,SAAS,EAAE,KAAK;IAChBiB,GAAG,EAAE,IAAI;IACTrB,WAAW,EAAE,EAAE;IACfF,QAAQ,EAAE;MACRI,MAAM,EAAE,CAAC,CAAC;MACViD,OAAO,EAAE,GAAG;MACZxD,GAAG,EAAE,EAAE;MACPqD,KAAK,EAAE;QACLE,MAAM,EAAE,EAAE;QACVD,OAAO,EAAE;MACX;IACF,CAAC;IACD3B,cAAc,EAAE;MAAE8B,IAAI,EAAE,MAAM;MAAEC,YAAY,EAAE;IAAc,CAAC;IAC7DtD,YAAY,EAAEoc,qBAAqB,CAACC,OAAO;EAC7C,CAAC;EACD,OAAO,eAAgBwI,MAAM,CAAC3jB,aAAa,CAACvE,gBAAgB,CAACyE,QAAQ,EAAE;IAAEC,KAAK,EAAE;EAAK,CAAC,EAAE,eAAgBwjB,MAAM,CAAC3jB,aAAa,CAACuZ,4BAA4B,EAAE;IAAE/Y,QAAQ,EAAE2a,OAAO,CAAC3a;EAAS,CAAC,EAAE,eAAgBmjB,MAAM,CAAC3jB,aAAa,CAAC1E,gBAAgB,CAAC4E,QAAQ,EAAE;IAAEC,KAAK,EAAE4hB;EAAiB,CAAC,EAAE,eAAgB4B,MAAM,CAAC3jB,aAAa,CAC1TjE,oBAAoB,EACpB;IACE0C,OAAO;IACPsB,MAAM;IACNW,OAAO,EAAE,KAAK;IACd/B,KAAK,EAAEwc,OAAO,CAACxc;EACjB,CACF,CAAC,CAAC,CAAC,CAAC;AACN;AACA,SAASqnB,oBAAoBA,CAACtnB,GAAG,EAAE;EACjC,OAAOA,GAAG,CAAC8L,QAAQ,CAAC2J,QAAQ,CAAC,MAAM,CAAC;AACtC;AACA,SAAS+R,iBAAiBA,CAACxnB,GAAG,EAAE;EAC9B,OAAOA,GAAG,CAAC8L,QAAQ,CAAC2J,QAAQ,CAAC,WAAW,CAAC;AAC3C;;AAEA;AACA,SAASsT,YAAYA,CAAA,EAAG;EACtB,IAAIC,QAAQ,GAAG,IAAIljB,WAAW,CAAC,CAAC;EAChC,IAAImjB,gBAAgB,GAAG,IAAI;EAC3B,IAAI5D,SAAS,GAAG,IAAInW,cAAc,CAAC;IACjCC,KAAKA,CAACC,UAAU,EAAE;MAChB,IAAI,OAAOiO,MAAM,KAAK,WAAW,EAAE;QACjC;MACF;MACA,IAAI6L,WAAW,GAAIrD,KAAK,IAAK;QAC3B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;UAC7BzW,UAAU,CAACC,OAAO,CAAC2Z,QAAQ,CAAC9qB,MAAM,CAAC2nB,KAAK,CAAC,CAAC;QAC5C,CAAC,MAAM;UACLzW,UAAU,CAACC,OAAO,CAACwW,KAAK,CAAC;QAC3B;MACF,CAAC;MACDxI,MAAM,CAAC8L,aAAa,KAAK9L,MAAM,CAAC8L,aAAa,GAAG,EAAE,CAAC;MACnD9L,MAAM,CAAC8L,aAAa,CAAC1a,OAAO,CAACya,WAAW,CAAC;MACzC7L,MAAM,CAAC8L,aAAa,CAACpc,IAAI,GAAI8Y,KAAK,IAAK;QACrCqD,WAAW,CAACrD,KAAK,CAAC;QAClB,OAAO,CAAC;MACV,CAAC;MACDoD,gBAAgB,GAAG7Z,UAAU;IAC/B;EACF,CAAC,CAAC;EACF,IAAI,OAAOoT,QAAQ,KAAK,WAAW,IAAIA,QAAQ,CAAC4G,UAAU,KAAK,SAAS,EAAE;IACxE5G,QAAQ,CAACnO,gBAAgB,CAAC,kBAAkB,EAAE,MAAM;MAClD4U,gBAAgB,EAAE3Z,KAAK,CAAC,CAAC;IAC3B,CAAC,CAAC;EACJ,CAAC,MAAM;IACL2Z,gBAAgB,EAAE3Z,KAAK,CAAC,CAAC;EAC3B;EACA,OAAO+V,SAAS;AAClB;;AAEA;AACA,SAASgE,iBAAiBA,CAACle,MAAM,EAAE;EACjC,IAAI,CAACA,MAAM,EAAE,OAAO,IAAI;EACxB,IAAIC,OAAO,GAAGb,MAAM,CAACa,OAAO,CAACD,MAAM,CAAC;EACpC,IAAIM,UAAU,GAAG,CAAC,CAAC;EACnB,KAAK,IAAI,CAACvF,GAAG,EAAEwF,GAAG,CAAC,IAAIN,OAAO,EAAE;IAC9B,IAAIM,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,oBAAoB,EAAE;MAC9CF,UAAU,CAACvF,GAAG,CAAC,GAAG,IAAIvJ,iBAAiB,CACrC+O,GAAG,CAACa,MAAM,EACVb,GAAG,CAAC+I,UAAU,EACd/I,GAAG,CAAC+D,IAAI,EACR/D,GAAG,CAAC4d,QAAQ,KAAK,IACnB,CAAC;IACH,CAAC,MAAM,IAAI5d,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,OAAO,EAAE;MACxC,IAAID,GAAG,CAACE,SAAS,EAAE;QACjB,IAAI2d,gBAAgB,GAAGlM,MAAM,CAAC3R,GAAG,CAACE,SAAS,CAAC;QAC5C,IAAI,OAAO2d,gBAAgB,KAAK,UAAU,EAAE;UAC1C,IAAI;YACF,IAAIniB,KAAK,GAAG,IAAImiB,gBAAgB,CAAC7d,GAAG,CAACH,OAAO,CAAC;YAC7CnE,KAAK,CAAC6D,KAAK,GAAGS,GAAG,CAACT,KAAK;YACvBQ,UAAU,CAACvF,GAAG,CAAC,GAAGkB,KAAK;UACzB,CAAC,CAAC,OAAOkH,CAAC,EAAE,CACZ;QACF;MACF;MACA,IAAI7C,UAAU,CAACvF,GAAG,CAAC,IAAI,IAAI,EAAE;QAC3B,IAAIkB,KAAK,GAAG,IAAIpD,KAAK,CAAC0H,GAAG,CAACH,OAAO,CAAC;QAClCnE,KAAK,CAAC6D,KAAK,GAAGS,GAAG,CAACT,KAAK;QACvBQ,UAAU,CAACvF,GAAG,CAAC,GAAGkB,KAAK;MACzB;IACF,CAAC,MAAM;MACLqE,UAAU,CAACvF,GAAG,CAAC,GAAGwF,GAAG;IACvB;EACF;EACA,OAAOD,UAAU;AACnB;AAEA,SACE3L,YAAY,EACZ2C,gBAAgB,EAChBoF,YAAY,EACZkB,QAAQ,EACR4B,UAAU,EACViD,iBAAiB,EACjBoH,oBAAoB,EACpByD,aAAa,EACbK,SAAS,EACTC,oBAAoB,EACpBW,0BAA0B,EAC1BG,0BAA0B,EAC1BrM,IAAI,EACJ8M,gBAAgB,EAChBgC,2BAA2B,EAC3BQ,gBAAgB,EAChByE,iBAAiB,EACjB2F,qBAAqB,EACrByB,eAAe,EACfI,YAAY,EACZM,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}